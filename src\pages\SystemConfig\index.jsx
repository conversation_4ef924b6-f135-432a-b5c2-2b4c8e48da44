import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Breadcrumb,
  Tabs,
  Form,
  Input,
  Select,
  Switch,
  Table,
  Tag,
  Tooltip,
  message,
  Modal,
  DatePicker
} from 'antd';
import {
  DownloadOutlined,
  UploadOutlined,
  ReloadOutlined,
  QuestionCircleOutlined,
  EditOutlined,
  SaveOutlined,
  UndoOutlined,
  ExportOutlined,
  SettingOutlined,
  GlobalOutlined,
  TeamOutlined,
  ToolOutlined,
  SafetyCertificateOutlined,
  ApiOutlined
} from '@ant-design/icons';
import './styles.css';
import { systemConfigApi } from '../../service/systemConfig';

const { Option } = Select;

const SystemConfig = () => {
  const [activeTab, setActiveTab] = useState('basic');
  const [selectedEnv, setSelectedEnv] = useState('prod');
  const [isEditing, setIsEditing] = useState(false);
  const [form] = Form.useForm();
  const [tenantList, setTenantList] = useState([]);
  const [selectedTenant, setSelectedTenant] = useState(null);
  const [tenantConfig, setTenantConfig] = useState(null);
  const [securityModalVisible, setSecurityModalVisible] = useState(false);
  const [securitySettingsData, setSecuritySettingsData] = useState(null);
  const [securityForm] = Form.useForm();
  const [notificationModalVisible, setNotificationModalVisible] = useState(false);
  const [notificationSettingsData, setNotificationSettingsData] = useState(null);
  const [notificationForm] = Form.useForm();
  const [configParamModalVisible, setConfigParamModalVisible] = useState(false);
  const [configParamSettingsData, setConfigParamSettingsData] = useState(null);
  const [configParamForm] = Form.useForm();
  const [addConfigModalVisible, setAddConfigModalVisible] = useState(false);
  const [addConfigSettingsData, setAddConfigSettingsData] = useState(null);
  const [addConfigSecurityForm] = Form.useForm();
  const [addConfigNotificationForm] = Form.useForm();
  const [addConfigParamForm] = Form.useForm();

  // Mock data for system parameters
  const systemParams = [
    {
      name: 'server.port',
      value: '8080',
      description: '服务端口',
      type: 'number'
    },
    {
      name: 'app.timezone',
      value: 'Asia/Shanghai',
      description: '系统时区',
      type: 'string'
    },
    {
      name: 'file.upload.max',
      value: '50MB',
      description: '最大上传大小',
      type: 'string'
    },
    {
      name: 'session.timeout',
      value: '30m',
      description: '会话超时时间',
      type: 'duration'
    },
    {
      name: 'mail.sender',
      value: '<EMAIL>',
      description: '系统邮件发送地址',
      type: 'email'
    }
  ];

  // Mock data for environment variables
  const envVariables = [
    {
      name: 'DB_CONN_TIMEOUT',
      currentValue: '60s',
      defaultValue: '30s',
      source: '手动'
    },
    {
      name: 'API_RATE_LIMIT',
      currentValue: '100',
      defaultValue: '50',
      source: '文件'
    },
    {
      name: 'LOG_RETENTION',
      currentValue: '90d',
      defaultValue: '30d',
      source: '手动'
    }
  ];

  // Table columns for system parameters
  const paramColumns = [
    {
      title: '参数名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (text, record) => (
        <Space direction="vertical" size={0}>
          <span>{text}</span>
          <small style={{ color: 'rgba(0, 0, 0, 0.45)' }}>{record.description}</small>
        </Space>
      )
    },
    {
      title: '参数值',
      dataIndex: 'value',
      key: 'value',
      ellipsis: true,
      render: (text, record) => (
        <Space>
          <span>{text}</span>
          <Tag>{record.type}</Tag>
        </Space>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      ellipsis: true,
      render: () => (
        <Button type="link" icon={<EditOutlined />}>
          编辑
        </Button>
      )
    }
  ];

  // Table columns for environment variables
  const envColumns = [
    {
      title: '环境变量名',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true
    },
    {
      title: '当前值',
      dataIndex: 'currentValue',
      key: 'currentValue',
      ellipsis: true
    },
    {
      title: '覆盖默认值',
      dataIndex: 'defaultValue',
      key: 'defaultValue',
      ellipsis: true,
      render: (text, record) => (
        <Space>
          <span>{text}</span>
          <Tag color="blue">{record.source}</Tag>
        </Space>
      )
    }
  ];

  // 获取租户列表
  useEffect(() => {
    if (activeTab === 'tenant' && tenantList.length === 0) {
      systemConfigApi.getTenantDropdownList()
        .then(response => {
          if (response && Array.isArray(response.data)) {
            setTenantList(response.data);
            if (response.data.length > 0) {
              setSelectedTenant(response.data[0].name);
              // 获取第一个租户的配置
              // 注意：这里只是加载数据，无需显示成功提示
              handleTenantChange(response.data[0].name, response.data[0].id);
            }
          }
        })
        .catch(error => {
          console.error('获取租户列表失败', error);
          message.error('获取租户列表失败');
        });
    }
  }, [activeTab]);

  // 租户选择变更处理
  const handleTenantChange = (value, option) => {
    setSelectedTenant(value);

    // 获取租户配置
    const tenantId = option.key || option;
    systemConfigApi.getTenantConfig(tenantId)
      .then(response => {
        if (response && response.data) {
          setTenantConfig(response.data);
          // 注意：这里只是加载数据，无需显示成功提示
        } else {
          // 如果接口返回不成功或数据为null，则设置tenantConfig为null
          setTenantConfig(null);
          if (response && response.message) {
            message.error(response.message);
          }
        }
      })
      .catch(error => {
        console.error('获取租户配置失败', error);
        message.error('获取租户配置失败');
        setTenantConfig(null);
      });
  };

  const handleSave = () => {
    form.validateFields().then(values => {
      message.success('配置保存成功！');
      setIsEditing(false);
    });
  };

  const handleReset = () => {
    form.resetFields();
    message.info('已重置为默认值');
  };

  // 处理安全设置管理按钮点击
  const handleSecurityManage = () => {
    if (!selectedTenant) {
      message.warning('请先选择租户');
      return;
    }

    // 获取选中租户的ID
    const tenantId = tenantList.find(tenant => tenant.name === selectedTenant)?.id;
    if (!tenantId) {
      message.error('获取租户ID失败');
      return;
    }

    // 调用接口获取租户配置
    systemConfigApi.getTenantConfig(tenantId)
      .then(response => {
        if (response && response.data) {
          setSecuritySettingsData(response.data);
          setSecurityModalVisible(true);

          // 设置表单初始值
          setTimeout(() => {
            if (response.data.securitySettings) {
              securityForm.setFieldsValue({
                minLength: response.data.securitySettings.passwordPolicy?.minLength,
                requireUppercase: response.data.securitySettings.passwordPolicy?.requireUppercase,
                requireLowercase: response.data.securitySettings.passwordPolicy?.requireLowercase,
                requireNumbers: response.data.securitySettings.passwordPolicy?.requireNumbers,
                requireSpecialChars: response.data.securitySettings.passwordPolicy?.requireSpecialChars,
                passwordExpiration: response.data.securitySettings.passwordPolicy?.passwordExpiration,
                passwordHistory: response.data.securitySettings.passwordPolicy?.passwordHistory,
                ipEnabled: response.data.securitySettings.ipRestrictions?.enabled,
                allowedIpRanges: response.data.securitySettings.ipRestrictions?.allowedIpRanges,
                deniedIpRanges: response.data.securitySettings.ipRestrictions?.deniedIpRanges,
                sessionTimeout: response.data.securitySettings.sessionSettings?.sessionTimeout,
                maxConcurrentSessions: response.data.securitySettings.sessionSettings?.maxConcurrentSessions,
                allowRememberMe: response.data.securitySettings.sessionSettings?.allowRememberMe
              });
            }
          }, 0);
        }
      })
      .catch(error => {
        console.error('获取租户安全配置失败', error);
        message.error('获取租户安全配置失败');
      });
  };

  // 处理安全设置保存
  const handleSaveSecurity = () => {
    securityForm.validateFields().then(values => {
      if (!selectedTenant) {
        message.warning('请先选择租户');
        return;
      }

      // 获取选中租户的ID
      const tenantId = tenantList.find(tenant => tenant.name === selectedTenant)?.id;
      if (!tenantId) {
        message.error('获取租户ID失败');
        return;
      }

      // 准备提交的数据
      const updateData = {
        tenantId: tenantId,
        securitySettings: {
          passwordPolicy: {
            minLength: parseInt(values.minLength) || 0,
            requireUppercase: values.requireUppercase || false,
            requireLowercase: values.requireLowercase || false,
            requireNumbers: values.requireNumbers || false,
            requireSpecialChars: values.requireSpecialChars || false,
            passwordExpiration: parseInt(values.passwordExpiration) || 0,
            passwordHistory: parseInt(values.passwordHistory) || 0
          },
          ipRestrictions: {
            enabled: values.ipEnabled || false,
            allowedIpRanges: values.allowedIpRanges ? values.allowedIpRanges.split(/[,，\n]/).map(ip => ip.trim()).filter(ip => ip) : [],
            deniedIpRanges: values.deniedIpRanges ? values.deniedIpRanges.split(/[,，\n]/).map(ip => ip.trim()).filter(ip => ip) : []
          },
          sessionSettings: {
            sessionTimeout: parseInt(values.sessionTimeout) || 0,
            maxConcurrentSessions: parseInt(values.maxConcurrentSessions) || 0,
            allowRememberMe: values.allowRememberMe || false
          }
        },
        notificationSettings: securitySettingsData.notificationSettings || {
          emailNotifications: true,
          smsNotifications: true,
          pushNotifications: true,
          alertChannels: [
            {
              channelType: '',
              enabled: true,
              recipients: ''
            }
          ]
        },
        integrationSettings: securitySettingsData.integrationSettings || '',
        dataSettings: securitySettingsData.dataSettings || '',
        uiSettings: securitySettingsData.uiSettings || '',
        featureFlags: securitySettingsData.featureFlags || '',
        complianceSettings: securitySettingsData.complianceSettings || '',
        defaultValues: securitySettingsData.defaultValues || '',
        configVersion: securitySettingsData.configVersion || 0,
        lastApplied: securitySettingsData.lastApplied || '',
        isActive: securitySettingsData.isActive || 0,
        notes: securitySettingsData.notes || '',
        id: securitySettingsData.id || ''
      };

      // 调用API保存数据
      systemConfigApi.updateTenantConfig(updateData)
        .then(response => {
          if (response && response.success) {
            // 检查是否是静默响应，只有在没有suppressGlobalMessage标记时才显示成功消息
            if (!response.suppressGlobalMessage) {
              message.success('安全设置保存成功');
            }
            setSecurityModalVisible(false);

            // 刷新租户配置数据
            systemConfigApi.getTenantConfig(tenantId)
              .then(response => {
                if (response && response.data) {
                  setTenantConfig(response.data);
                }
              });
          } else {
            message.error('安全设置保存失败: ' + (response.message || '未知错误'));
          }
        })
        .catch(error => {
          console.error('保存安全设置失败', error);
          message.error('保存安全设置失败');
        });
    });
  };

  // 处理通知设置管理按钮点击
  const handleNotificationManage = () => {
    if (!selectedTenant) {
      message.warning('请先选择租户');
      return;
    }

    // 获取选中租户的ID
    const tenantId = tenantList.find(tenant => tenant.name === selectedTenant)?.id;
    if (!tenantId) {
      message.error('获取租户ID失败');
      return;
    }

    // 调用接口获取租户配置
    systemConfigApi.getTenantConfig(tenantId)
      .then(response => {
        if (response && response.data) {
          setNotificationSettingsData(response.data);
          setNotificationModalVisible(true);

          // 设置表单初始值
          setTimeout(() => {
            if (response.data.notificationSettings) {
              notificationForm.setFieldsValue({
                emailNotifications: response.data.notificationSettings.emailNotifications,
                smsNotifications: response.data.notificationSettings.smsNotifications,
                pushNotifications: response.data.notificationSettings.pushNotifications,
                channelType: response.data.notificationSettings.alertChannels?.[0]?.channelType,
                channelEnabled: response.data.notificationSettings.alertChannels?.[0]?.enabled,
                recipients: response.data.notificationSettings.alertChannels?.[0]?.recipients
              });
            }
          }, 0);
        }
      })
      .catch(error => {
        console.error('获取租户通知配置失败', error);
        message.error('获取租户通知配置失败');
      });
  };

  // 处理通知设置保存
  const handleSaveNotification = () => {
    notificationForm.validateFields().then(values => {
      if (!selectedTenant) {
        message.warning('请先选择租户');
        return;
      }

      // 获取选中租户的ID
      const tenantId = tenantList.find(tenant => tenant.name === selectedTenant)?.id;
      if (!tenantId) {
        message.error('获取租户ID失败');
        return;
      }

      // 准备提交的数据
      const updateData = {
        tenantId: tenantId,
        securitySettings: notificationSettingsData.securitySettings || {
          passwordPolicy: {
            minLength: 0,
            requireUppercase: true,
            requireLowercase: true,
            requireNumbers: true,
            requireSpecialChars: true,
            passwordExpiration: 0,
            passwordHistory: 0
          },
          ipRestrictions: {
            enabled: true,
            allowedIpRanges: [],
            deniedIpRanges: []
          },
          sessionSettings: {
            sessionTimeout: 0,
            maxConcurrentSessions: 0,
            allowRememberMe: true
          }
        },
        notificationSettings: {
          emailNotifications: values.emailNotifications || false,
          smsNotifications: values.smsNotifications || false,
          pushNotifications: values.pushNotifications || false,
          alertChannels: [
            {
              channelType: values.channelType || '',
              enabled: values.channelEnabled || false,
              recipients: values.recipients || ''
            }
          ]
        },
        integrationSettings: notificationSettingsData.integrationSettings || '',
        dataSettings: notificationSettingsData.dataSettings || '',
        uiSettings: notificationSettingsData.uiSettings || '',
        featureFlags: notificationSettingsData.featureFlags || '',
        complianceSettings: notificationSettingsData.complianceSettings || '',
        defaultValues: notificationSettingsData.defaultValues || '',
        configVersion: notificationSettingsData.configVersion || 0,
        lastApplied: notificationSettingsData.lastApplied || '',
        isActive: notificationSettingsData.isActive || 0,
        notes: notificationSettingsData.notes || '',
        id: notificationSettingsData.id || ''
      };

      // 调用API保存数据
      systemConfigApi.updateTenantConfig(updateData)
        .then(response => {
          if (response && response.success) {
            // 检查是否是静默响应，只有在没有suppressGlobalMessage标记时才显示成功消息
            if (!response.suppressGlobalMessage) {
              message.success('通知设置保存成功');
            }
            setNotificationModalVisible(false);

            // 刷新租户配置数据
            systemConfigApi.getTenantConfig(tenantId)
              .then(response => {
                if (response && response.data) {
                  setTenantConfig(response.data);
                }
              });
          } else {
            message.error('通知设置保存失败: ' + (response.message || '未知错误'));
          }
        })
        .catch(error => {
          console.error('保存通知设置失败', error);
          message.error('保存通知设置失败');
        });
    });
  };

  // 处理配置参数管理按钮点击
  const handleConfigParamManage = () => {
    if (!selectedTenant) {
      message.warning('请先选择租户');
      return;
    }

    // 获取选中租户的ID
    const tenantId = tenantList.find(tenant => tenant.name === selectedTenant)?.id;
    if (!tenantId) {
      message.error('获取租户ID失败');
      return;
    }

    // 调用接口获取租户配置
    systemConfigApi.getTenantConfig(tenantId)
      .then(response => {
        if (response && response.data) {
          setConfigParamSettingsData(response.data);
          setConfigParamModalVisible(true);

          // 设置表单初始值
          setTimeout(() => {
            if (response.data) {
              configParamForm.setFieldsValue({
                integrationSettings_apiKeys: response.data.integrationSettings?.apiKeys ? JSON.stringify(response.data.integrationSettings.apiKeys) : '',
                integrationSettings_webhooks: response.data.integrationSettings?.webhooks || '',
                integrationSettings_thirdPartyIntegrations: response.data.integrationSettings?.thirdPartyIntegrations || '',
                dataSettings_dataExportEnabled: response.data.dataSettings?.dataExportEnabled || false,
                dataSettings_dataImportEnabled: response.data.dataSettings?.dataImportEnabled || false,
                dataSettings_dataSharingEnabled: response.data.dataSettings?.dataSharingEnabled || false,
                dataSettings_dataEncryptionLevel: response.data.dataSettings?.dataEncryptionLevel || 0,
                uiSettings: response.data.uiSettings || {},
                featureFlags: response.data.featureFlags || {},
                complianceSettings: response.data.complianceSettings || {},
                defaultValues: response.data.defaultValues || {},
                configVersion: response.data.configVersion || 0,
                lastApplied: response.data.lastApplied || '',
                isActive: response.data.isActive === 1,
                notes: response.data.notes || ''
              });
            }
          }, 0);
        }
      })
      .catch(error => {
        console.error('获取租户配置参数失败', error);
        message.error('获取租户配置参数失败');
      });
  };

  // 处理配置参数保存
  const handleSaveConfigParam = () => {
    configParamForm.validateFields().then(values => {
      if (!selectedTenant) {
        message.warning('请先选择租户');
        return;
      }

      // 获取选中租户的ID
      const tenantId = tenantList.find(tenant => tenant.name === selectedTenant)?.id;
      if (!tenantId) {
        message.error('获取租户ID失败');
        return;
      }

      // 准备提交的数据 - 保持原有数据结构，仅更新表单中修改的字段
      const updateData = {
        tenantId: tenantId,
        securitySettings: configParamSettingsData.securitySettings || {
          passwordPolicy: {
            minLength: 0,
            requireUppercase: true,
            requireLowercase: true,
            requireNumbers: true,
            requireSpecialChars: true,
            passwordExpiration: 0,
            passwordHistory: 0
          },
          ipRestrictions: {
            enabled: true,
            allowedIpRanges: [],
            deniedIpRanges: []
          },
          sessionSettings: {
            sessionTimeout: 0,
            maxConcurrentSessions: 0,
            allowRememberMe: true
          }
        },
        notificationSettings: configParamSettingsData.notificationSettings || {
          emailNotifications: true,
          smsNotifications: true,
          pushNotifications: true,
          alertChannels: [
            {
              channelType: '',
              enabled: true,
              recipients: ''
            }
          ]
        },
        integrationSettings: {
          apiKeys: values.integrationSettings_apiKeys ? JSON.parse(values.integrationSettings_apiKeys) : [],
          webhooks: values.integrationSettings_webhooks || '',
          thirdPartyIntegrations: values.integrationSettings_thirdPartyIntegrations || ''
        },
        dataSettings: {
          dataExportEnabled: values.dataSettings_dataExportEnabled || false,
          dataImportEnabled: values.dataSettings_dataImportEnabled || false,
          dataSharingEnabled: values.dataSettings_dataSharingEnabled || false,
          dataEncryptionLevel: values.dataSettings_dataEncryptionLevel || 0
        },
        uiSettings: configParamSettingsData.uiSettings || {},
        featureFlags: configParamSettingsData.featureFlags || {},
        complianceSettings: configParamSettingsData.complianceSettings || {},
        defaultValues: configParamSettingsData.defaultValues || {},
        configVersion: configParamSettingsData.configVersion || 0,
        lastApplied: configParamSettingsData.lastApplied || '',
        isActive: configParamSettingsData.isActive || 0,
        notes: configParamSettingsData.notes || '',
        id: configParamSettingsData.id || ''
      };

      // 调用API保存数据
      systemConfigApi.updateTenantConfig(updateData)
        .then(response => {
          if (response && response.success) {
            // 检查是否是静默响应，只有在没有suppressGlobalMessage标记时才显示成功消息
            if (!response.suppressGlobalMessage) {
              message.success('配置参数保存成功');
            }
            setConfigParamModalVisible(false);

            // 刷新租户配置数据
            systemConfigApi.getTenantConfig(tenantId)
              .then(response => {
                if (response && response.data) {
                  setTenantConfig(response.data);
                }
              });
          } else {
            message.error('配置参数保存失败: ' + (response.message || '未知错误'));
          }
        })
        .catch(error => {
          console.error('保存配置参数失败', error);
          message.error('保存配置参数失败');
        });
    });
  };

  // 处理添加配置按钮点击
  const handleAddConfig = () => {
    if (!selectedTenant) {
      message.warning('请先选择租户');
      return;
    }

    // 获取选中租户的ID
    const tenantId = tenantList.find(tenant => tenant.name === selectedTenant)?.id;
    if (!tenantId) {
      message.error('获取租户ID失败');
      return;
    }

    // 创建一个新的空配置
    const emptyConfig = {
      tenantId: tenantId,
      securitySettings: {
        passwordPolicy: {
          minLength: 8,
          requireUppercase: false,
          requireLowercase: false,
          requireNumbers: false,
          requireSpecialChars: false,
          passwordExpiration: 90,
          passwordHistory: 5
        },
        ipRestrictions: {
          enabled: false,
          allowedIpRanges: '',
          deniedIpRanges: ''
        },
        sessionSettings: {
          sessionTimeout: 30,
          maxConcurrentSessions: 5,
          allowRememberMe: true
        }
      },
      notificationSettings: {
        emailNotifications: false,
        smsNotifications: false,
        pushNotifications: false,
        alertChannels: [
          {
            channelType: '',
            enabled: false,
            recipients: ''
          }
        ]
      },
      integrationSettings: '',
      dataSettings: '',
      uiSettings: '',
      featureFlags: false,
      complianceSettings: '',
      defaultValues: '',
      configVersion: 1.0,
      lastApplied: '',
      isActive: 0,
      notes: ''
    };

    setAddConfigSettingsData(emptyConfig);
    setAddConfigModalVisible(true);

    // 设置表单初始值
    setTimeout(() => {
      // 设置安全设置表单初始值
      addConfigSecurityForm.setFieldsValue({
        minLength: 8,
        requireUppercase: false,
        requireLowercase: false,
        requireNumbers: false,
        requireSpecialChars: false,
        passwordExpiration: 90,
        passwordHistory: 5,
        ipEnabled: false,
        allowedIpRanges: '',
        deniedIpRanges: '',
        sessionTimeout: 30,
        maxConcurrentSessions: 5,
        allowRememberMe: true
      });

      // 设置通知设置表单初始值
      addConfigNotificationForm.setFieldsValue({
        emailNotifications: false,
        smsNotifications: false,
        pushNotifications: false,
        channelType: '',
        channelEnabled: false,
        recipients: ''
      });

      // 设置配置参数表单初始值
      addConfigParamForm.setFieldsValue({
        integrationSettings_apiKeys: '',
        integrationSettings_webhooks: '',
        integrationSettings_thirdPartyIntegrations: '',
        dataSettings_dataExportEnabled: false,
        dataSettings_dataImportEnabled: false,
        dataSettings_dataSharingEnabled: false,
        dataSettings_dataEncryptionLevel: 0,
        uiSettings: '',
        featureFlags: false,
        complianceSettings: '',
        defaultValues: '',
        configVersion: 1.0,
        lastApplied: null,
        isActive: false,
        notes: ''
      });
    }, 0);
  };

  // 处理添加配置保存
  const handleSaveAddConfig = () => {
    // 验证所有表单
    Promise.all([
      addConfigSecurityForm.validateFields(),
      addConfigNotificationForm.validateFields(),
      addConfigParamForm.validateFields()
    ]).then(([securityValues, notificationValues, paramValues]) => {
      if (!selectedTenant) {
        message.warning('请先选择租户');
        return;
      }

      // 获取选中租户的ID
      const tenantId = tenantList.find(tenant => tenant.name === selectedTenant)?.id;
      if (!tenantId) {
        message.error('获取租户ID失败');
        return;
      }

      // 准备提交的数据
      const addData = {
        tenantId: tenantId,
        securitySettings: {
          passwordPolicy: {
            minLength: parseInt(securityValues.minLength) || 0,
            requireUppercase: securityValues.requireUppercase || true,
            requireLowercase: securityValues.requireLowercase || true,
            requireNumbers: securityValues.requireNumbers || true,
            requireSpecialChars: securityValues.requireSpecialChars || true,
            passwordExpiration: parseInt(securityValues.passwordExpiration) || 0,
            passwordHistory: parseInt(securityValues.passwordHistory) || 0
          },
          ipRestrictions: {
            enabled: securityValues.ipEnabled || true,
            allowedIpRanges: [],
            deniedIpRanges: []
          },
          sessionSettings: {
            sessionTimeout: parseInt(securityValues.sessionTimeout) || 0,
            maxConcurrentSessions: parseInt(securityValues.maxConcurrentSessions) || 0,
            allowRememberMe: securityValues.allowRememberMe || true
          }
        },
        notificationSettings: {
          emailNotifications: notificationValues.emailNotifications || true,
          smsNotifications: notificationValues.smsNotifications || true,
          pushNotifications: notificationValues.pushNotifications || true,
          alertChannels: [
            {
              channelType: notificationValues.channelType || "",
              enabled: notificationValues.channelEnabled || true,
              recipients: notificationValues.recipients || ""
            }
          ]
        },
        integrationSettings: {
          apiKeys: [],
          webhooks: "",
          thirdPartyIntegrations: ""
        },
        dataSettings: {
          dataExportEnabled: true,
          dataImportEnabled: true,
          dataSharingEnabled: true,
          dataEncryptionLevel: 0
        },
        uiSettings: {},
        featureFlags: {},
        complianceSettings: {},
        defaultValues: {},
        configVersion: parseFloat(paramValues.configVersion) || 0,
        lastApplied: paramValues.lastApplied ? paramValues.lastApplied.format("YYYY-MM-DD HH:mm:ss") : "",
        isActive: paramValues.isActive ? 1 : 0,
        notes: paramValues.notes || ""
      };

      // 调用API添加数据
      systemConfigApi.addTenantConfig(addData)
        .then(response => {
          if (response && response.success) {
            message.success('配置添加成功');
            setAddConfigModalVisible(false);

            // 刷新租户配置数据
            systemConfigApi.getTenantConfig(tenantId)
              .then(response => {
                if (response && response.data) {
                  setTenantConfig(response.data);
                }
              });
          } else {
            message.error('配置添加失败: ' + (response.message || '未知错误'));
          }
        })
        .catch(error => {
          console.error('添加配置失败', error);
          message.error('添加配置失败');
        });
    }).catch(error => {
      console.error('表单验证失败', error);
    });
  };

  return (
    <div className="system-config">
      {/* Header */}
      <div className="page-header">
        <div className="header-left">
          <Breadcrumb
            items={[
              { title: '首页' },
              { title: '系统管理' },
              { title: '系统配置' }
            ]}
          />
          <h2>系统配置</h2>
        </div>
        <div className="header-right">
          <Space>
            <Button icon={<DownloadOutlined />}>导出</Button>
            <Button icon={<UploadOutlined />}>导入</Button>
            {/* <Button icon={<ReloadOutlined />} onClick={() => {
              // 根据当前激活的标签页刷新相应的数据
              if (activeTab === 'tenant' && selectedTenant) {
                // 获取选中租户的ID
                const tenantId = tenantList.find(tenant => tenant.name === selectedTenant)?.id;
                if (tenantId) {
                  // 刷新租户配置数据
                  systemConfigApi.getTenantConfig(tenantId)
                    .then(response => {
                      if (response && response.data) {
                        setTenantConfig(response.data);
                      }
                    })
                    .catch(error => {
                      console.error('刷新租户配置失败', error);
                      message.error('刷新租户配置失败');
                    });
                }
              } else if (activeTab === 'tenant' && tenantList.length === 0) {
                // 如果没有租户列表数据，刷新租户列表
                systemConfigApi.getTenantDropdownList()
                  .then(response => {
                    if (response && Array.isArray(response.data)) {
                      setTenantList(response.data);
                      if (response.data.length > 0) {
                        setSelectedTenant(response.data[0].name);
                        // 获取第一个租户的配置
                        handleTenantChange(response.data[0].name, response.data[0].id);
                      }
                    }
                  })
                  .catch(error => {
                    console.error('刷新租户列表失败', error);
                    message.error('刷新租户列表失败');
                  });
              } else {
                message.info('当前页面没有需要刷新的数据');
              }
            }}>刷新</Button> */}
            <Button icon={<QuestionCircleOutlined />}>帮助</Button>
          </Space>
        </div>
      </div>

      {/* Main Tabs */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'basic',
            label: (
              <span>
                <SettingOutlined />
                基础设置
              </span>
            ),
            children: (
              <Card>
                <Form
                  form={form}
                  layout="vertical"
                  initialValues={{
                    version: '3.2.1',
                    envName: 'PROD',
                    logLevel: 'INFO',
                    debugMode: false,
                    maintenanceMode: false
                  }}
                  disabled={!isEditing}
                >
                  <Row gutter={24}>
                    <Col span={8}>
                      <Form.Item
                        name="version"
                        label="版本"
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        name="envName"
                        label="环境名称"
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        name="logLevel"
                        label="日志级别"
                      >
                        <Select>
                          <Option value="DEBUG">DEBUG</Option>
                          <Option value="INFO">INFO</Option>
                          <Option value="WARN">WARN</Option>
                          <Option value="ERROR">ERROR</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        name="debugMode"
                        label="调试模式"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="maintenanceMode"
                        label="维护模式"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              </Card>
            )
          },
          {
            key: 'env',
            label: (
              <span>
                <GlobalOutlined />
                环境配置
              </span>
            ),
            children: (
              <div className="env-config">
                <Card>
                  <Space className="env-header">
                    <span>配置环境选择:</span>
                    <Select
                      value={selectedEnv}
                      onChange={setSelectedEnv}
                      style={{ width: 200 }}
                    >
                      <Option value="prod">生产环境</Option>
                      <Option value="staging">预发环境</Option>
                      <Option value="test">测试环境</Option>
                      <Option value="dev">开发环境</Option>
                    </Select>
                    <span>上次更新: 2023-10-20 15:30 (管理员)</span>
                  </Space>
                </Card>

                <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
                  <Col span={8}>
                    <Card title="基本参数">
                      <Form layout="vertical" form={form}>
                        <Form.Item label="版本">3.2.1</Form.Item>
                        <Form.Item label="环境名称">PROD</Form.Item>
                        <Form.Item label="日志级别">INFO</Form.Item>
                        <Form.Item label="调试模式">关闭</Form.Item>
                        <Form.Item label="维护模式">关闭</Form.Item>
                      </Form>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card title="服务参数">
                      <Form layout="vertical" form={form}>
                        <Form.Item label="API超时">30秒</Form.Item>
                        <Form.Item label="连接池大小">50</Form.Item>
                        <Form.Item label="最大线程数">100</Form.Item>
                        <Form.Item label="队列容量">1000</Form.Item>
                        <Form.Item label="缓存时间">3600秒</Form.Item>
                      </Form>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card title="高级选项">
                      <Form layout="vertical" form={form}>
                        <Form.Item label="热更新">启用</Form.Item>
                        <Form.Item label="加密存储">启用</Form.Item>
                        <Form.Item label="变更审计">完整</Form.Item>
                        <Form.Item label="参数覆盖">允许</Form.Item>
                        <Form.Item label="配置验证">严格</Form.Item>
                      </Form>
                    </Card>
                  </Col>
                </Row>

                <Card title="系统参数" style={{ marginTop: 16 }} extra={<Button>查看全部</Button>}>
                  <Table
                    columns={paramColumns}
                    dataSource={systemParams}
                    pagination={false}
                  />
                </Card>

                <Card title="环境变量覆盖" style={{ marginTop: 16 }} extra={<Button>管理</Button>}>
                  <Table
                    columns={envColumns}
                    dataSource={envVariables}
                    pagination={false}
                  />
                </Card>
              </div>
            )
          },
          {
            key: 'tenant',
            label: (
              <span>
                <TeamOutlined />
                租户配置
              </span>
            ),
            children: (
              <div className="tenant-config">
                <Card>
                  <Space className="tenant-header">
                    <span>租户选择:</span>
                    <Select
                      value={selectedTenant}
                      onChange={(value, option) => handleTenantChange(value, option)}
                      style={{ width: 200 }}
                      placeholder="请选择租户"
                      loading={tenantList.length === 0}
                    >
                      {tenantList.map(tenant => (
                        <Option key={tenant.id} value={tenant.name}>{tenant.name}</Option>
                      ))}
                    </Select>
                    {selectedTenant && !tenantConfig && (
                      <Button type="primary" onClick={handleAddConfig}>添加配置</Button>
                    )}
                    {selectedTenant && tenantConfig && (
                      <>
                        <Button type="primary" onClick={() => {
                          if (tenantConfig.id) {
                            systemConfigApi.activeTenantConfig([tenantConfig.id])
                              .then(response => {
                                if (response && response.success) {
                                  message.success('配置激活成功');
                                  // 刷新租户配置数据
                                  systemConfigApi.getTenantConfig(tenantConfig.id)
                                    .then(response => {
                                      if (response && response.data) {
                                        setTenantConfig(response.data);
                                      }
                                    });
                                } else {
                                  message.error('配置激活失败: ' + (response.message || '未知错误'));
                                }
                              });
                          }
                        }}>激活配置</Button>
                        <Button onClick={() => {
                          if (tenantConfig.id) {
                            systemConfigApi.unActiveTenantConfig([tenantConfig.id])
                              .then(response => {
                                if (response && response.success) {
                                  message.success('取消激活成功');
                                  // 刷新租户配置数据
                                  systemConfigApi.getTenantConfig(tenantConfig.id)
                                    .then(response => {
                                      if (response && response.data) {
                                        setTenantConfig(response.data);
                                      }
                                    });
                                } else {
                                  message.error('取消激活失败: ' + (response.message || '未知错误'));
                                }
                              });
                          }
                        }}>取消激活</Button>
                      </>
                    )}
                  </Space>
                </Card>

                {!tenantConfig ? (
                  // 当租户配置数据为空时显示简单界面
                  <div style={{ marginTop: '16px' }}>
                    <Card>
                      <div>
                        <span>租户选择: </span>
                        <span>{selectedTenant}</span>
                      </div>
                    </Card>
                  </div>
                ) : (
                  // 有租户配置数据时显示详细信息
                  <div>
                    <Card title="安全设置" style={{ marginTop: 16 }} extra={<Button onClick={handleSecurityManage}>管理</Button>}>
                      <Row gutter={[16, 16]}>
                        <Col span={8}>
                          <Card title="密码策略">
                            <Form layout="vertical" form={securityForm}>
                              {tenantConfig.securitySettings && tenantConfig.securitySettings.passwordPolicy ? (
                                <>
                                  <Row gutter={[16, 0]}>
                                    <Col span={12}>
                                      <Form.Item label="最小密码长度：">{tenantConfig.securitySettings.passwordPolicy.minLength || '暂无数据'}</Form.Item>
                                    </Col>
                                    <Col span={12}>
                                      <Form.Item label="是否要求大写字母：">{tenantConfig.securitySettings.passwordPolicy.requireUppercase ? '是' : '否'}</Form.Item>
                                    </Col>
                                  </Row>
                                  <Row gutter={[16, 0]}>
                                    <Col span={12}>
                                      <Form.Item label="是否要求小写字母：">{tenantConfig.securitySettings.passwordPolicy.requireLowercase ? '是' : '否'}</Form.Item>
                                    </Col>
                                    <Col span={12}>
                                      <Form.Item label="是否要求数字：">{tenantConfig.securitySettings.passwordPolicy.requireNumbers ? '是' : '否'}</Form.Item>
                                    </Col>
                                  </Row>
                                  <Row gutter={[16, 0]}>
                                    <Col span={12}>
                                      <Form.Item label="是否要求特殊字符：">{tenantConfig.securitySettings.passwordPolicy.requireSpecialChars ? '是' : '否'}</Form.Item>
                                    </Col>
                                    <Col span={12}>
                                      <Form.Item label="密码过期天数：">{tenantConfig.securitySettings.passwordPolicy.passwordExpiration || '暂无数据'}</Form.Item>
                                    </Col>
                                  </Row>
                                  <Row gutter={[16, 0]}>
                                    <Col span={12}>
                                      <Form.Item label="密码历史记录数：">{tenantConfig.securitySettings.passwordPolicy.passwordHistory || '暂无数据'}</Form.Item>
                                    </Col>
                                  </Row>
                                </>
                              ) : (
                                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                                  暂无数据
                                </div>
                              )}
                            </Form>
                          </Card>
                        </Col>
                        <Col span={8}>
                          <Card title="IP限制">
                            <Form layout="vertical" form={securityForm}>
                              {tenantConfig.securitySettings && tenantConfig.securitySettings.ipRestrictions ? (
                                <>
                                  <Form.Item label="是否启用：">{tenantConfig.securitySettings.ipRestrictions.enabled ? '是' : '否'}</Form.Item>
                                  <Form.Item label="允许的IP范围：">{Array.isArray(tenantConfig.securitySettings.ipRestrictions.allowedIpRanges) ? (tenantConfig.securitySettings.ipRestrictions.allowedIpRanges.length > 0 ? tenantConfig.securitySettings.ipRestrictions.allowedIpRanges.join('，') : '暂无数据') : (tenantConfig.securitySettings.ipRestrictions.allowedIpRanges || '暂无数据')}</Form.Item>
                                  <Form.Item label="禁止的IP范围：">{Array.isArray(tenantConfig.securitySettings.ipRestrictions.deniedIpRanges) ? (tenantConfig.securitySettings.ipRestrictions.deniedIpRanges.length > 0 ? tenantConfig.securitySettings.ipRestrictions.deniedIpRanges.join('，') : '暂无数据') : (tenantConfig.securitySettings.ipRestrictions.deniedIpRanges || '暂无数据')}</Form.Item>
                                </>
                              ) : (
                                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                                  暂无数据
                                </div>
                              )}
                            </Form>
                          </Card>
                        </Col>
                        <Col span={8}>
                          <Card title="会话设置">
                            <Form layout="vertical" form={securityForm}>
                              {tenantConfig.securitySettings && tenantConfig.securitySettings.sessionSettings ? (
                                <>
                                  <Form.Item label="会话超时时间：">{tenantConfig.securitySettings.sessionSettings.sessionTimeout || '暂无数据'}</Form.Item>
                                  <Form.Item label="会话最大数量：">{tenantConfig.securitySettings.sessionSettings.maxConcurrentSessions || '暂无数据'}</Form.Item>
                                  <Form.Item label="是否允许记住登录状态：">{tenantConfig.securitySettings.sessionSettings.allowRememberMe ? '是' : '否'}</Form.Item>
                                </>
                              ) : (
                                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                                  暂无数据
                                </div>
                              )}
                            </Form>
                          </Card>
                        </Col>
                      </Row>
                    </Card>

                    <Card title="通知设置" style={{ marginTop: 16 }} extra={<Button onClick={handleNotificationManage}>管理</Button>}>
                      <Row gutter={[16, 0]}>
                        <Col span={12}>
                          <Card title="通知开关" bordered={false}>
                            <Form layout="vertical" form={notificationForm}>
                              {tenantConfig.notificationSettings ? (
                                <>
                                  <Form.Item label="是否启用邮件通知：">{tenantConfig.notificationSettings.emailNotifications ? '是' : '否'}</Form.Item>
                                  <Form.Item label="是否启用短信通知：">{tenantConfig.notificationSettings.smsNotifications ? '是' : '否'}</Form.Item>
                                  <Form.Item label="是否启用推送通知：">{tenantConfig.notificationSettings.pushNotifications ? '是' : '否'}</Form.Item>
                                </>
                              ) : (
                                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                                  暂无数据
                                </div>
                              )}
                            </Form>
                          </Card>
                        </Col>
                        <Col span={12}>
                          <Card title="通知渠道" bordered={false}>
                            <Form layout="vertical" form={notificationForm}>
                              {tenantConfig.notificationSettings && tenantConfig.notificationSettings.alertChannels ? (
                                <>
                                  <Form.Item label="通道类型：">{tenantConfig.notificationSettings.alertChannels[0]?.channelType || '暂无数据'}</Form.Item>
                                  <Form.Item label="是否启用：">{tenantConfig.notificationSettings.alertChannels[0]?.enabled ? '是' : '否'}</Form.Item>
                                  <Form.Item label="接收人：">{tenantConfig.notificationSettings.alertChannels[0]?.recipients || '暂无数据'}</Form.Item>
                                </>
                              ) : (
                                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                                  暂无数据
                                </div>
                              )}
                            </Form>
                          </Card>
                        </Col>
                      </Row>
                    </Card>
                    <Card title="配置参数" style={{ marginTop: 16 }} extra={<Button onClick={handleConfigParamManage}>管理</Button>}>
                      <Row gutter={[16, 0]}>
                        <Col span={12}>
                          <Card title="集成设置" bordered={false}>
                            <Form layout="vertical" form={configParamForm}>
                              {tenantConfig && tenantConfig.integrationSettings ? (
                                <>
                                  <Form.Item label="API密钥：">{(tenantConfig.integrationSettings.apiKeys && tenantConfig.integrationSettings.apiKeys.length > 0) ? JSON.stringify(tenantConfig.integrationSettings.apiKeys) : '暂无数据'}</Form.Item>
                                  <Form.Item label="Webhook配置：">{tenantConfig.integrationSettings.webhooks || '暂无数据'}</Form.Item>
                                  <Form.Item label="第三方集成配置：">{tenantConfig.integrationSettings.thirdPartyIntegrations || '暂无数据'}</Form.Item>
                                </>
                              ) : (
                                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                                  暂无数据
                                </div>
                              )}
                            </Form>
                          </Card>
                        </Col>
                        <Col span={12}>
                          <Card title="数据设置" bordered={false}>
                            <Form layout="vertical" form={configParamForm}>
                              {tenantConfig && tenantConfig.dataSettings ? (
                                <>
                                  <Form.Item label="是否允许数据导出：">{tenantConfig.dataSettings.dataExportEnabled ? '是' : '否'}</Form.Item>
                                  <Form.Item label="是否允许数据导入：">{tenantConfig.dataSettings.dataImportEnabled ? '是' : '否'}</Form.Item>
                                  <Form.Item label="是否允许数据共享：">{tenantConfig.dataSettings.dataSharingEnabled ? '是' : '否'}</Form.Item>
                                  <Form.Item label="数据加密级别：">{tenantConfig.dataSettings.dataEncryptionLevel || '暂无数据'}</Form.Item>
                                </>
                              ) : (
                                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                                  暂无数据
                                </div>
                              )}
                            </Form>
                          </Card>
                        </Col>
                      </Row>
                    </Card>
                  </div>
                )}
              </div>
            )
          },
          {
            key: 'maintenance',
            label: (
              <span>
                <ToolOutlined />
                系统维护
              </span>
            ),
            children: 'System Maintenance Content'
          },
          {
            key: 'security',
            label: (
              <span>
                <SafetyCertificateOutlined />
                安全设置
              </span>
            ),
            children: 'Security Settings Content'
          },
          {
            key: 'integration',
            label: (
              <span>
                <ApiOutlined />
                集成管理
              </span>
            ),
            children: 'Integration Management Content'
          }
        ]}
      />

      {/* 安全设置管理弹窗 */}
      <Modal
        title="安全设置管理"
        open={securityModalVisible}
        onCancel={() => setSecurityModalVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setSecurityModalVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleSaveSecurity}>
            保存
          </Button>
        ]}
      >
        {securitySettingsData ? (
          <Form form={securityForm} layout="vertical">
            <Tabs defaultActiveKey="passwordPolicy" items={[
              {
                key: 'passwordPolicy',
                label: '密码策略',
                children: (
                  <>
                    {securitySettingsData.securitySettings && securitySettingsData.securitySettings.passwordPolicy ? (
                      <>
                        <Form.Item label="最小密码长度" name="minLength">
                          <Input placeholder="0" type="number" />
                        </Form.Item>
                        <Form.Item label="是否要求大写字母" name="requireUppercase" valuePropName="checked">
                          <Switch />
                        </Form.Item>
                        <Form.Item label="是否要求小写字母" name="requireLowercase" valuePropName="checked">
                          <Switch />
                        </Form.Item>
                        <Form.Item label="是否要求数字" name="requireNumbers" valuePropName="checked">
                          <Switch />
                        </Form.Item>
                        <Form.Item label="是否要求特殊字符" name="requireSpecialChars" valuePropName="checked">
                          <Switch />
                        </Form.Item>
                        <Form.Item label="密码过期天数" name="passwordExpiration">
                          <Input placeholder="0" type="number" />
                        </Form.Item>
                        <Form.Item label="密码历史记录数" name="passwordHistory">
                          <Input placeholder="0" type="number" />
                        </Form.Item>
                      </>
                    ) : (
                      <div>暂无密码策略数据</div>
                    )}
                  </>
                )
              },
              {
                key: 'ipRestrictions',
                label: 'IP限制',
                children: (
                  <>
                    {securitySettingsData.securitySettings && securitySettingsData.securitySettings.ipRestrictions ? (
                      <>
                        <Form.Item label="是否启用" name="ipEnabled" valuePropName="checked">
                          <Switch />
                        </Form.Item>
                        <Form.Item label="允许的IP范围" name="allowedIpRanges">
                          <Input.TextArea rows={4} />
                        </Form.Item>
                        <Form.Item label="禁止的IP范围" name="deniedIpRanges">
                          <Input.TextArea rows={4} />
                        </Form.Item>
                      </>
                    ) : (
                      <div>暂无IP限制数据</div>
                    )}
                  </>
                )
              },
              {
                key: 'sessionSettings',
                label: '会话设置',
                children: (
                  <>
                    {securitySettingsData.securitySettings && securitySettingsData.securitySettings.sessionSettings ? (
                      <>
                        <Form.Item label="会话超时时间" name="sessionTimeout">
                          <Input placeholder="0" type="number" />
                        </Form.Item>
                        <Form.Item label="会话最大数量" name="maxConcurrentSessions">
                          <Input placeholder="0" type="number" />
                        </Form.Item>
                        <Form.Item label="是否允许记住登录状态" name="allowRememberMe" valuePropName="checked">
                          <Switch />
                        </Form.Item>
                      </>
                    ) : (
                      <div>暂无会话设置数据</div>
                    )}
                  </>
                )
              }
            ]} />
          </Form>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>加载中...</div>
        )}
      </Modal>

      {/* 通知设置管理弹窗 */}
      <Modal
        title="通知设置管理"
        open={notificationModalVisible}
        onCancel={() => setNotificationModalVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setNotificationModalVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleSaveNotification}>
            保存
          </Button>
        ]}
      >
        {notificationSettingsData ? (
          <Form form={notificationForm} layout="vertical">
            <Tabs defaultActiveKey="notificationSettings" items={[
              {
                key: 'notificationSettings',
                label: '通知设置',
                children: (
                  <>
                    <Form.Item label="是否启用邮件通知" name="emailNotifications" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                    <Form.Item label="是否启用短信通知" name="smsNotifications" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                    <Form.Item label="是否启用推送通知" name="pushNotifications" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </>
                )
              },
              {
                key: 'channelSettings',
                label: '通知渠道',
                children: (
                  <>
                    <Form.Item label="通道类型" name="channelType">
                      <Input />
                    </Form.Item>
                    <Form.Item label="是否启用" name="channelEnabled" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                    <Form.Item label="接收人" name="recipients">
                      <Input />
                    </Form.Item>
                  </>
                )
              }
            ]} />
          </Form>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>加载中...</div>
        )}
      </Modal>

      {/* 配置参数管理弹窗 */}
      <Modal
        title="配置参数管理"
        open={configParamModalVisible}
        onCancel={() => setConfigParamModalVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setConfigParamModalVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleSaveConfigParam}>
            保存
          </Button>
        ]}
      >
        {configParamSettingsData ? (
          <Form form={configParamForm} layout="vertical">
            <Tabs defaultActiveKey="basicConfig" items={[
              {
                key: 'basicConfig',
                label: '集成设置',
                children: (
                  <>
                    <Form.Item label="API密钥" name="integrationSettings_apiKeys">
                      <Input.TextArea rows={4} placeholder="API密钥" />
                    </Form.Item>
                    <Form.Item label="Webhook配置" name="integrationSettings_webhooks">
                      <Input placeholder="Webhook配置" />
                    </Form.Item>
                    <Form.Item label="第三方集成配置" name="integrationSettings_thirdPartyIntegrations">
                      <Input placeholder="第三方集成配置" />
                    </Form.Item>
                  </>
                )
              },
              {
                key: 'configInfo',
                label: '数据设置',
                children: (
                  <>
                    <Form.Item label="是否允许数据导出" name="dataSettings_dataExportEnabled" valuePropName="checked">
                      <Switch checkedChildren="是" unCheckedChildren="否" />
                    </Form.Item>
                    <Form.Item label="是否允许数据导入" name="dataSettings_dataImportEnabled" valuePropName="checked">
                      <Switch checkedChildren="是" unCheckedChildren="否" />
                    </Form.Item>
                    <Form.Item label="是否允许数据共享" name="dataSettings_dataSharingEnabled" valuePropName="checked">
                      <Switch checkedChildren="是" unCheckedChildren="否" />
                    </Form.Item>
                    <Form.Item label="数据加密级别" name="dataSettings_dataEncryptionLevel">
                      <Select>
                        <Option value={0}>无加密</Option>
                        <Option value={1}>基础加密</Option>
                        <Option value={2}>高级加密</Option>
                        <Option value={3}>最高级别加密</Option>
                      </Select>
                    </Form.Item>
                  </>
                )
              }
            ]} />
          </Form>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>加载中...</div>
        )}
      </Modal>

      {/* 添加配置弹窗 */}
      <Modal
        title="添加配置"
        open={addConfigModalVisible}
        onCancel={() => setAddConfigModalVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setAddConfigModalVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleSaveAddConfig}>
            保存
          </Button>
        ]}
      >
        {addConfigSettingsData ? (
          <Tabs defaultActiveKey="security" items={[
            {
              key: 'security',
              label: '安全设置',
              children: (
                <Form form={addConfigSecurityForm} layout="vertical">
                  <Tabs defaultActiveKey="passwordPolicy" items={[
                    {
                      key: 'passwordPolicy',
                      label: '密码策略',
                      children: (
                        <>
                          <Form.Item label="最小密码长度" name="minLength">
                            <Input placeholder="8" type="number" />
                          </Form.Item>
                          <Form.Item label="是否要求大写字母" name="requireUppercase" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                          <Form.Item label="是否要求小写字母" name="requireLowercase" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                          <Form.Item label="是否要求数字" name="requireNumbers" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                          <Form.Item label="是否要求特殊字符" name="requireSpecialChars" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                          <Form.Item label="密码过期天数" name="passwordExpiration">
                            <Input placeholder="90" type="number" />
                          </Form.Item>
                          <Form.Item label="密码历史记录数" name="passwordHistory">
                            <Input placeholder="5" type="number" />
                          </Form.Item>
                        </>
                      )
                    },
                    {
                      key: 'ipRestrictions',
                      label: 'IP限制',
                      children: (
                        <>
                          <Form.Item label="是否启用" name="ipEnabled" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                          <Form.Item label="允许的IP范围" name="allowedIpRanges">
                            <Input.TextArea rows={4} />
                          </Form.Item>
                          <Form.Item label="禁止的IP范围" name="deniedIpRanges">
                            <Input.TextArea rows={4} />
                          </Form.Item>
                        </>
                      )
                    },
                    {
                      key: 'sessionSettings',
                      label: '会话设置',
                      children: (
                        <>
                          <Form.Item label="会话超时时间" name="sessionTimeout">
                            <Input placeholder="30" type="number" />
                          </Form.Item>
                          <Form.Item label="会话最大数量" name="maxConcurrentSessions">
                            <Input placeholder="5" type="number" />
                          </Form.Item>
                          <Form.Item label="是否允许记住登录状态" name="allowRememberMe" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                        </>
                      )
                    }
                  ]} />
                </Form>
              )
            },
            {
              key: 'notification',
              label: '通知设置',
              children: (
                <Form form={addConfigNotificationForm} layout="vertical">
                  <Tabs defaultActiveKey="notificationSettings" items={[
                    {
                      key: 'notificationSettings',
                      label: '通知设置',
                      children: (
                        <>
                          <Form.Item label="是否启用邮件通知" name="emailNotifications" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                          <Form.Item label="是否启用短信通知" name="smsNotifications" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                          <Form.Item label="是否启用推送通知" name="pushNotifications" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                        </>
                      )
                    },
                    {
                      key: 'channelSettings',
                      label: '通知渠道',
                      children: (
                        <>
                          <Form.Item label="通道类型" name="channelType">
                            <Input />
                          </Form.Item>
                          <Form.Item label="是否启用" name="channelEnabled" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                          <Form.Item label="接收人" name="recipients">
                            <Input />
                          </Form.Item>
                        </>
                      )
                    }
                  ]} />
                </Form>
              )
            },
            {
              key: 'configParam',
              label: '配置参数',
              children: (
                <Form form={addConfigParamForm} layout="vertical">
                  <Tabs defaultActiveKey="basicConfig" items={[
                    {
                      key: 'basicConfig',
                      label: '集成设置',
                      children: (
                        <>
                          <Form.Item label="API密钥" name="integrationSettings_apiKeys">
                            <Input.TextArea rows={4} placeholder="API密钥" />
                          </Form.Item>
                          <Form.Item label="Webhook配置" name="integrationSettings_webhooks">
                            <Input placeholder="Webhook配置" />
                          </Form.Item>
                          <Form.Item label="第三方集成配置" name="integrationSettings_thirdPartyIntegrations">
                            <Input placeholder="第三方集成配置" />
                          </Form.Item>
                        </>
                      )
                    },
                    {
                      key: 'configInfo',
                      label: '数据设置',
                      children: (
                        <>
                          <Form.Item label="是否允许数据导出" name="dataSettings_dataExportEnabled" valuePropName="checked">
                            <Switch checkedChildren="是" unCheckedChildren="否" />
                          </Form.Item>
                          <Form.Item label="是否允许数据导入" name="dataSettings_dataImportEnabled" valuePropName="checked">
                            <Switch checkedChildren="是" unCheckedChildren="否" />
                          </Form.Item>
                          <Form.Item label="是否允许数据共享" name="dataSettings_dataSharingEnabled" valuePropName="checked">
                            <Switch checkedChildren="是" unCheckedChildren="否" />
                          </Form.Item>
                          <Form.Item label="数据加密级别" name="dataSettings_dataEncryptionLevel">
                            <Select>
                              <Option value={0}>无加密</Option>
                              <Option value={1}>基础加密</Option>
                              <Option value={2}>高级加密</Option>
                              <Option value={3}>最高级别加密</Option>
                            </Select>
                          </Form.Item>
                        </>
                      )
                    }
                  ]} />
                </Form>
              )
            }
          ]} />
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>加载中...</div>
        )}
      </Modal>

      {/* Bottom Action Bar */}
      <div className="action-bar">
        <Space>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
          >
            保存更改
          </Button>
          <Button
            icon={<UndoOutlined />}
            onClick={handleReset}
          >
            重置为默认值
          </Button>
          <Button
            icon={<ExportOutlined />}
          >
            导出配置
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default SystemConfig;