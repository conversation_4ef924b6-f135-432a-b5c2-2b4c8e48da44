import {useEffect,useRef} from 'react';
import echarts from "./../../../utils/echart"
import { Card, List, Tag, Space, Tabs } from 'antd';
import {
  AlertOutlined,
  PieChartOutlined,
} from '@ant-design/icons';
import {  useNavigate } from 'react-router-dom'

const LeftPanel = ({deviceCountGroupByStatus
}) => {
  const alertsColor = {
    "低": "blue",
    "中": "orange",
    "高": "gold",
    "紧急": "red"
  };

  const chartInstance = useRef(null);
  const navigate = useNavigate()

  useEffect(() => {
    if (deviceCountGroupByStatus) {
      chartInit(deviceCountGroupByStatus);
      
      // Add resize handler
      const handleResize = () => {
        if (chartInstance.current) {
          chartInstance.current.resize();
        }
      };
      
      window.addEventListener('resize', handleResize);
      
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [deviceCountGroupByStatus]);

  // chart初始化
  const chartInit = chartData => {
    let chartDom = document.getElementById('dashboard-chart1');
    if (!chartDom) {
      return;
    }

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartDom);
    }
    
      
  
    const option = {
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '13%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        formatter: function(name) {
          // 找到对应的数据项
          const item = chartData.find(i =>
            (i.status === 0 && name === "离线") ||
            (i.status === 1 && name === "在线") ||
            (i.status === 2 && name === "驱鼠中") ||
            (i.status >= 2 && name === "禁用")
          );
          return item ? `${name} ${item.count}` : name;
        }
      },
      series: [
        {
          name: '设备状态',
          type: 'pie',
          radius: ['30%', '55%'],
          data: chartData.map(item =>({
            value: item.count,
            name:  item.status === 0 ? "离线" : item.status === 1 ? "在线" : item.status === 2 ? "驱鼠中" : "禁用",
            itemStyle: {
              color: item.status === 0 ? '#ff4d4f' : item.status === 1 ? '#52c41a' :item.status === 2 ? '#1677ff' : "#faad14"
            }
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    option && chartInstance.current.setOption(option, true);

     // 添加点击事件
     chartInstance.current.off('click'); // 先解绑，防止重复绑定
     chartInstance.current.on('click', function(params) {

      const statusMap = {
       '离线': 0,
       "在线": 1,
       "驱鼠中": 2,
       '禁用': 3,
      }

      navigate('/device/repellent',
        {
          state: {
            status: statusMap[params.name]
          }   
        })
      // console.log(params,888)
     });
  };

  const items = [
    {
      key: 'status',
      label: (
        <span>
          <PieChartOutlined />
          此刻设备状态
        </span>
      ),
      children: <div id='dashboard-chart1' style={{height: "100%"}}></div>
    },
    // {
    //   key: 'alerts',
    //   label: (
    //     <span>
    //       <AlertOutlined />
    //       本月告警概览
    //     </span>
    //   ),
    //   children: (
    //     <List
    //       dataSource={alarms}
    //       renderItem={(item) => (
    //         <List.Item>
    //           <Space style={{ width: '100%', justifyContent: 'space-between' }}>
    //             <Tag color={alertsColor[item.level]}>{item.level}</Tag>
    //             <span>{item.count}条</span>
    //           </Space>
    //         </List.Item>
    //       )}
    //     />
    //   )
    // },
    // {
    //   key: 'warnings',
    //   label: (
    //     <span>
    //       <WarningOutlined />
    //       最新预警
    //     </span>
    //   ),
    //   children: (
    //     <List
    //       dataSource={warnings}
    //       renderItem={(item) => (
    //         <List.Item>
    //           <Space direction="vertical" style={{ width: '100%' }}>
    //             <Space>
    //               <Tag color={item.severity === 'error' ? 'red' : 'orange'}>
    //                 {item.type}
    //               </Tag>
    //               <span>{item.device}</span>
    //             </Space>
    //             <span style={{ color: 'rgba(0,0,0,0.45)' }}>
    //               位置: {item.location}
    //             </span>
    //           </Space>
    //         </List.Item>
    //       )}
    //     />
    //   )
    // }
  ];

  return (
    <Card className="dashboard-card panel-card">
      <Tabs
        defaultActiveKey="status"
        items={items}
        tabPosition="top"
        className="panel-tabs"
        onChange={(value) =>{
          if(value === "status"){
            setTimeout(() =>{
              chartInstance.current?.resize();
            },0)
          }
        }}  
      />
    </Card>
  );
};

export default LeftPanel;