import fetch from './fetch';

import {API_PREFIX} from "./constant"

// 获取设备统计数据
export const getDevicesStatisticsData = (data) => {
  return fetch({
    url: `${API_PREFIX["view-service"]}/statistics/devices/data`,
    method: 'post',
    data
  })
}

// 获取鼠类活动数据
export const getMousesStatisticsData = (data) => {
  return fetch({
    url: `${API_PREFIX["view-service"]}/statistics/mouses/data`,
    method: 'post',
    data
  })
}

// 获取鼠类活动概览数据
export const getMousesTotalData = (data) => {
  return fetch({
    url: `${API_PREFIX["view-service"]}/statistics/mouses/total/data`,
    method: 'post',
    data
  })
}

// 获取鼠类活动数据按周统计
export const getMousesTotalWeek = (data) => {
  return fetch({
    url: `${API_PREFIX["view-service"]}/statistics/mouses/week/data`,
    method: 'post',
    data
  })
}

// 获取鼠类活动数据按天统计
export const getMousesTotalDay = (data) => {
  return fetch({
    url: `${API_PREFIX["view-service"]}/statistics/mouses/day/data`,
    method: 'post',
    data
  })
}


// 获取驱鼠效果数据
export const getEffectsStatisticsData = (data) => {
  return fetch({
    url: `${API_PREFIX["view-service"]}/statistics/effects/data`,
    method: 'post',
    data
  })
}


// 导出驱鼠效果数据
export const exportDeviceStatus = (data) => {
  return fetch({
    url: `${API_PREFIX["view-service"]}/statistics/effect/data/export`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// 导出鼠类活动数据
export const exportMousesStatisticsData = (data,tab) => {
  const url = tab === 0 ?  `${API_PREFIX["view-service"]}/statistics/mouse/data/export` :  `${API_PREFIX["view-service"]}/statistics/mouse/data/day/export`
  return fetch({
    url,
    method: 'post',
    data,
    responseType: 'blob',
  })
}


// 导出设备统计数据
export const exportDevicesStatisticsData = (data) => {
  return fetch({
    url: `${API_PREFIX["view-service"]}/statistics/devices/data/export`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}


