import React from 'react';
import { Card,  List, Tag, Space, Tabs } from 'antd';
import {
  FireOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom'

const RightPanel = ({ratEventsGroupByCamera = []}) => {
  const navigate = useNavigate()

  // Ensure ratEventsGroupByCamera is always an array
  const safeRatEvents = Array.isArray(ratEventsGroupByCamera) ? ratEventsGroupByCamera : []

  // // Mock data for hotspots
  // const hotspots = [
  //   { location: '1号仓库', activity: '频繁', risk: 'high' },
  //   { location: '后厨区域', activity: '中等', risk: 'medium' },
  //   { location: '垃圾处理站', activity: '频繁', risk: 'high' }
  // ];

  // renderItem={(item) => (
  //   <List.Item>
  //     <Space style={{ width: '100%', justifyContent: 'space-between' }}>
  //       <span>{item.location}</span>
  //       <Space>
  //         <Tag color={item.activity === '频繁' ? 'red' : 'orange'}>
  //           {item.activity}
  //         </Tag>
  //         <Tag color={item.risk === 'high' ? 'red' : 'orange'}>
  //           {item.risk === 'high' ? '高风险' : '中风险'}
  //         </Tag>
  //       </Space>
  //     </Space>
  //   </List.Item>
  // )}

  const items = [
    {
      key: 'hotspots',
      label: (
        <span>
          <FireOutlined />
          本月活动热点
        </span>
      ),
      children: (
        <List
          dataSource={safeRatEvents}
          header={<Space style={{ width: '100%', justifyContent: 'space-between',fontWeight: 900 }}>
            <span>摄像头编号</span>
            <div>鼠患事件</div>
          </Space>}
          renderItem={(item, index) => (
            <List.Item key={item?.cameraNo || index}>
              <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                <a onClick={() =>{
                   navigate('/sensing/event',
                    {
                      state: {
                        cameraNo: item?.cameraNo
                      }
                    }
                  )
                }}>{item?.cameraNo}</a>
                <span>{item?.eventCount || 0}</span>
              </Space>
            </List.Item>
          )}

        />
      )
    }
  ];

  return (
    <Card className="dashboard-card panel-card">
      <Tabs
        defaultActiveKey="scores"
        items={items}
        tabPosition="top"
        className="panel-tabs"
      />
    </Card>
  );
};

export default RightPanel;