.maintenance-audio {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.page-header {
  margin-bottom: 24px;
}

.header-left {
  margin-bottom: 16px;
}

.header-left h2 {
  margin: 8px 0 0;
  font-size: 24px;
  font-weight: 500;
}

.maintenance-audio-filter-bar {
  margin-bottom: 16px;
}

.view-switch {
  margin-bottom: 16px;
}

.view-switch .ant-radio-group {
  display: flex;
  gap: 8px;
}

.view-switch .ant-radio-button-wrapper {
  flex: 1;
  text-align: center;
}

.audio-list {
  flex: 1;
  min-height: 0;
}

/* Upload Modal Styles */
.ant-upload-drag {
  padding: 24px;
}

.ant-upload-drag-icon {
  margin-bottom: 16px;
}

.ant-upload-text {
  margin-bottom: 8px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .maintenance-audio {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .view-switch .ant-radio-group {
    flex-direction: column;
  }

  .ant-modal {
    max-width: 100vw;
    margin: 8px;
  }
}