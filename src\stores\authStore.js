import { makeAutoObservable } from 'mobx';
import axios from 'axios';
import Cookies from 'js-cookie';
import sha256 from 'crypto-js/sha256';

class AuthStore {
  isAuthenticated = false;
  user = null;
  loginError = null;
  isLoading = false;
  loginAttempts = 0;
  lastLoginAttempt = null;

  constructor() {
    makeAutoObservable(this);
  }

  setIsAuthenticated(value) {
    this.isAuthenticated = value;
  }

  setUser(user) {
    this.user = user;
    // 保存用户数据到localStorage
    if (user) {
      localStorage.setItem('userData', JSON.stringify(user));
    } else {
      localStorage.removeItem('userData');
    }
  }

  setLoginError(error) {
    this.loginError = error;
  }

  setIsLoading(value) {
    this.isLoading = value;
  }

  async login(email, password, captcha) {
    try {
      this.setIsLoading(true);
      this.setLoginError(null);

      // Check login attempts
      if (this.loginAttempts >= 5 && Date.now() - this.lastLoginAttempt < 600000) {
        throw new Error('登录尝试次数过多，请10分钟后再试');
      }

      // Hash password before sending
      const hashedPassword = sha256(password).toString();

      // Simulate API call - replace with actual API endpoint
      const response = await new Promise((resolve, reject) => {
        setTimeout(() => {
          if (email === '<EMAIL>' && hashedPassword === sha256('admin123').toString()) {
            resolve({
              data: {
                token: 'mock-jwt-token',
                user: {
                  id: 1,
                  email: '<EMAIL>',
                  name: 'Admin'
                }
              }
            });
          } else {
            reject(new Error('用户名或密码错误'));
          }
        }, 1000);
      });

      const { token, user } = response.data;
      
      // 优先存储到localStorage
      localStorage.setItem('token', token);
      
      // 尝试设置Cookie，但不依赖它的成功
      try {
        Cookies.set('token', token, { 
          expires: 2/24, 
          path: '/',
          // 移除可能导致Cookie设置失败的选项
          sameSite: undefined,
          secure: undefined
        });
      } catch (e) {
        console.warn('登录时设置Cookie失败，但继续使用localStorage:', e);
      }
      
      // 同时设置到sessionStorage作为备份
      sessionStorage.setItem('token', token);
      
      this.setUser(user);
      this.setIsAuthenticated(true);
      this.loginAttempts = 0;
      
      return true;
    } catch (error) {
      this.lastLoginAttempt = Date.now();
      this.loginAttempts += 1;
      this.setLoginError(error.message);
      return false;
    } finally {
      this.setIsLoading(false);
    }
  }

  async loginWithPhone(phone, smsCode) {
    try {
      this.setIsLoading(true);
      this.setLoginError(null);

      // Simulate API call - replace with actual API endpoint
      const response = await new Promise((resolve, reject) => {
        setTimeout(() => {
          if (phone === '13800138000' && smsCode === '123456') {
            resolve({
              data: {
                token: 'mock-jwt-token',
                user: {
                  id: 1,
                  phone: '13800138000',
                  name: 'Admin'
                }
              }
            });
          } else {
            reject(new Error('验证码错误'));
          }
        }, 1000);
      });

      const { token, user } = response.data;
      
      // 优先存储到localStorage
      localStorage.setItem('token', token);
      
      // 尝试设置Cookie，但不依赖它的成功
      try {
        Cookies.set('token', token, { 
          expires: 2/24, 
          path: '/',
          // 移除可能导致Cookie设置失败的选项
          sameSite: undefined,
          secure: undefined
        });
      } catch (e) {
        console.warn('登录时设置Cookie失败，但继续使用localStorage:', e);
      }
      
      // 同时设置到sessionStorage作为备份
      sessionStorage.setItem('token', token);
      
      this.setUser(user);
      this.setIsAuthenticated(true);
      
      return true;
    } catch (error) {
      this.setLoginError(error.message);
      return false;
    } finally {
      this.setIsLoading(false);
    }
  }

  logout() {
    Cookies.remove('token');
    localStorage.removeItem('token');
    localStorage.removeItem('userData');
    localStorage.removeItem('userMenu'); // 清除用户菜单权限缓存
    localStorage.removeItem('logEntry'); // 清除登录日志
    localStorage.removeItem('lastPage'); // 清除上次访问页面记录

    sessionStorage.removeItem('token');
    sessionStorage.removeItem('userData');
    sessionStorage.removeItem('userMenu'); // 清除用户菜单权限缓存
    sessionStorage.removeItem('layoutLoaded'); // 清除布局加载状态

    this.setUser(null);
    this.setIsAuthenticated(false);
  }

  checkAuth() {
    // 在开发环境中打印更多调试信息
    // 检查各种存储位置的token
    const localToken = localStorage.getItem('token');
    const sessionToken = sessionStorage.getItem('token');
    const cookieToken = Cookies.get('token');
    const hasAnyToken = !!(localToken || sessionToken || cookieToken);
    
    // 如果有任何token，立即设置认证状态为true
    if (hasAnyToken) {
      this.setIsAuthenticated(true);
      
      // 更新所有存储位置以确保一致性
      const effectiveToken = localToken || sessionToken || cookieToken;
      
      if (!localToken && effectiveToken) {
        localStorage.setItem('token', effectiveToken);
      }
      
      if (!sessionToken && effectiveToken) {
        sessionStorage.setItem('token', effectiveToken);
      }
      
      // 尝试设置Cookie
      try {
        Cookies.set('token', effectiveToken, { 
          expires: 2/24, 
          path: '/',
          sameSite: undefined,
          secure: undefined
        });
      } catch (e) {
        console.warn('设置Cookie失败:', e);
      }
      
      // 尝试恢复用户数据
      const userData = localStorage.getItem('userData') || sessionStorage.getItem('userData');
      if (userData && !this.user) {
        try {
          this.setUser(JSON.parse(userData));
        } catch (e) {
          console.error('解析用户数据失败:', e);
        }
      }
      
      return true;
    }
    
    // 如果没有token，设置为未认证状态
    this.setIsAuthenticated(false);
    return false;
  }

  // 获取当前token的方法
  getToken() {
    try {
      const localToken = localStorage.getItem('token');
      const sessionToken = sessionStorage.getItem('token');
      const cookieToken = Cookies.get('token');
      return localToken || sessionToken || cookieToken || '';
    } catch (error) {
      console.error('获取token出错:', error);
      return '';
    }
  }
}

// 创建一个单例实例并导出
const authStoreInstance = new AuthStore();

// 确保在window对象上也暴露authStore，方便调试
if (typeof window !== 'undefined') {
  window.authStore = authStoreInstance;
}

// 修复导出方式，确保getToken方法可用
export const authStore = authStoreInstance;
export default authStoreInstance;