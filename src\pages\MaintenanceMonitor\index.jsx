import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Breadcrumb,
  Statistic,
  Progress,
  Tabs,
  DatePicker,
  Radio,
  Select,
  Tag,
  List,
  Timeline,
  Tooltip,
  Badge
} from 'antd';
import {
  ReloadOutlined,
  DownloadOutlined,
  SettingOutlined,
  <PERSON>UpOutlined,
  ArrowDownOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  DesktopOutlined,
  ApiOutlined,
  CloudServerOutlined,
  DatabaseOutlined,
  AppstoreOutlined,
  BarChartOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { Line, Gauge, Area, Pie } from '@ant-design/plots';
import dayjs from 'dayjs';
import './styles.css';

const { RangePicker } = DatePicker;

const MaintenanceMonitor = () => {
  const [activeTab, setActiveTab] = useState('basic');
  const [timeRange, setTimeRange] = useState('realtime');
  const [granularity, setGranularity] = useState('5min');

  // Mock data for alerts
  const alerts = [
    {
      type: 'critical',
      count: 2,
      icon: <ExclamationCircleOutlined style={{ fontSize: 24, color: '#ff4d4f' }} />,
      title: '严重',
      color: '#ff4d4f'
    },
    {
      type: 'warning',
      count: 5,
      icon: <WarningOutlined style={{ fontSize: 24, color: '#faad14' }} />,
      title: '警告',
      color: '#faad14'
    },
    {
      type: 'info',
      count: 12,
      icon: <InfoCircleOutlined style={{ fontSize: 24, color: '#1890ff' }} />,
      title: '信息',
      color: '#1890ff'
    },
    {
      type: 'healthy',
      count: 86,
      icon: <CheckCircleOutlined style={{ fontSize: 24, color: '#52c41a' }} />,
      title: '健康',
      color: '#52c41a'
    }
  ];

  // Mock data for resource monitoring
  const cpuData = Array.from({ length: 24 }, (_, i) => ({
    time: dayjs().subtract(24 - i, 'hour').format('HH:00'),
    value: 30 + Math.random() * 40,
    type: 'CPU使用率'
  }));

  const memoryData = Array.from({ length: 24 }, (_, i) => ({
    time: dayjs().subtract(24 - i, 'hour').format('HH:00'),
    value: 40 + Math.random() * 30,
    type: '内存使用率'
  }));

  const resourceData = [...cpuData, ...memoryData];

  const resourceConfig = {
    data: resourceData,
    xField: 'time',
    yField: 'value',
    seriesField: 'type',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000
      }
    }
  };

  // Mock data for disk usage
  const diskData = [
    { type: '系统盘', value: 65 },
    { type: '数据盘A', value: 45 },
    { type: '数据盘B', value: 78 }
  ];

  const diskConfig = {
    data: diskData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'circle',
      content: '{name}\n{percentage}'
    }
  };

  // Mock data for network traffic
  const networkData = Array.from({ length: 24 }, (_, i) => ({
    time: dayjs().subtract(24 - i, 'hour').format('HH:00'),
    value: Math.random() * 100,
    type: '入站流量'
  })).concat(Array.from({ length: 24 }, (_, i) => ({
    time: dayjs().subtract(24 - i, 'hour').format('HH:00'),
    value: Math.random() * 100,
    type: '出站流量'
  })));

  const networkConfig = {
    data: networkData,
    xField: 'time',
    yField: 'value',
    seriesField: 'type',
    smooth: true
  };

  // Mock data for system events
  const systemEvents = [
    {
      time: '2024-02-20 15:30:00',
      type: 'critical',
      content: 'CPU使用率超过90%'
    },
    {
      time: '2024-02-20 15:15:00',
      type: 'warning',
      content: '内存使用率达到85%'
    },
    {
      time: '2024-02-20 15:00:00',
      type: 'info',
      content: '系统自动清理临时文件'
    }
  ];

  const tabItems = [
    {
      key: 'basic',
      label: (
        <span>
          <DesktopOutlined />
          基础资源
        </span>
      ),
      children: (
        <div className="resource-monitoring">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Card title="CPU监控">
                <Statistic
                  title="当前使用率"
                  value={35.8}
                  suffix="%"
                  prefix={<SyncOutlined spin />}
                />
                <Progress
                  percent={35.8}
                  status="active"
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
                <Line {...resourceConfig} />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="内存监控">
                <Statistic
                  title="当前使用率"
                  value={42.5}
                  suffix="%"
                  prefix={<SyncOutlined spin />}
                />
                <Progress
                  percent={42.5}
                  status="active"
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
                <Line {...resourceConfig} />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="磁盘使用">
                <Pie {...diskConfig} />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="网络流量">
                <Area {...networkConfig} />
              </Card>
            </Col>
          </Row>
        </div>
      )
    },
    {
      key: 'os',
      label: (
        <span>
          <ApiOutlined />
          操作系统
        </span>
      ),
      children: (
        <div className="os-monitoring">
         
      {/* Time Control */}
      <Card className="time-control">
        <Space>
          <Radio.Group value={timeRange} onChange={(e) => setTimeRange(e.target.value)}>
            <Radio.Button value="realtime">实时</Radio.Button>
            <Radio.Button value="1h">1小时</Radio.Button>
            <Radio.Button value="6h">6小时</Radio.Button>
            <Radio.Button value="1d">1天</Radio.Button>
            <Radio.Button value="1w">1周</Radio.Button>
            <Radio.Button value="1m">1月</Radio.Button>
            <Radio.Button value="custom">自定义</Radio.Button>
          </Radio.Group>
          {timeRange === 'custom' && <RangePicker showTime />}
          <Select
            value={granularity}
            onChange={setGranularity}
            options={[
              { value: '5s', label: '5秒' },
              { value: '30s', label: '30秒' },
              { value: '1min', label: '1分钟' },
              { value: '5min', label: '5分钟' },
              { value: '15min', label: '15分钟' },
              { value: '1h', label: '1小时' },
              { value: '1d', label: '1天' }
            ]}
            style={{ width: 120 }}
          />
        </Space>
      </Card>

      {/* System Events */}
      <Card title="系统事件" className="system-events">
        <Timeline
          items={systemEvents.map(event => ({
            color: event.type === 'critical' ? 'red' :
                   event.type === 'warning' ? 'orange' : 'blue',
            children: (
              <Space direction="vertical">
                <span>{event.content}</span>
                <small style={{ color: '#999' }}>{event.time}</small>
              </Space>
            )
          }))}
        />
      </Card>
        </div>
      )
    },
    {
      key: 'hardware',
      label: (
        <span>
          <CloudServerOutlined />
          硬件
        </span>
      ),
      children: (
        <div className="hardware-monitoring">
          {/* Hardware monitoring content */}
        </div>
      )
    },
    {
      key: 'middleware',
      label: (
        <span>
          <DatabaseOutlined />
          中间件服务
        </span>
      ),
      children: (
        <div className="middleware-monitoring">
          {/* Middleware monitoring content */}
        </div>
      )
    },
    {
      key: 'microservice',
      label: (
        <span>
          <AppstoreOutlined />
          微服务
        </span>
      ),
      children: (
        <div className="microservice-monitoring">
          {/* Microservice monitoring content */}
        </div>
      )
    },
    {
      key: 'business',
      label: (
        <span>
          <BarChartOutlined />
          业务指标
        </span>
      ),
      children: (
        <div className="business-monitoring">
          {/* Business metrics content */}
        </div>
      )
    }
  ];

  return (
    <div className="maintenance-monitor">
      {/* Header */}
      <div className="page-header">
        <div className="header-left">
          <Breadcrumb
            items={[
              { title: '首页' },
              { title: '维护升级' },
              { title: '系统监控' }
            ]}
          />
          <h2>系统监控</h2>
        </div>
        <div className="header-right">
          <Space>
            {/* <Button icon={<ReloadOutlined />}>刷新</Button> */}
            <Button icon={<DownloadOutlined />}>导出</Button>
            <Button icon={<SettingOutlined />}>设置</Button>
          </Space>
        </div>
      </div>

      {/* Health Score */}
      <Row gutter={[16, 16]} className="health-score">
        <Col span={16}>
          <Card>
            <Space align="center" size="large">
              <Statistic
                title="系统健康评分"
                value={92}
                suffix="/100"
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              />
              <Tag color="success">
                <ArrowUpOutlined /> +3
              </Tag>
            </Space>
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Space>
              <span>最后更新：</span>
              <span>{dayjs().format('YYYY-MM-DD HH:mm:ss')}</span>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* Alert Overview */}
      <Row gutter={[16, 16]} className="alert-overview">
        {alerts.map((alert, index) => (
          <Col span={6} key={index}>
            <Card>
              <Statistic
                title={alert.title}
                value={alert.count}
                prefix={alert.icon}
                valueStyle={{ color: alert.color }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Main Content */}
      <Card className="maintenance-monitor-content">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          className="monitor-tabs"
        />
      </Card>

    </div>
  );
};

export default MaintenanceMonitor;