.device-camera-container {
  height: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.group-sider {
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

.group-header {
  padding: 16px 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.group-tools {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  gap: 8px;
}

.icon_img{
  width: 14px;
  height: 14px;
}

.device-content {
  padding: 16px;
  background: #fff;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.toolbar-matrix {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.toolbar-section {
  height: 100%;
}

.toolbar-section .ant-card-body {
  padding: 12px;
}

.search-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.camera-table {
  margin-top: 8px;
}

.expanded-row {
  padding: 16px;
  background: #fafafa;
}

.camera-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.camera-details p {
  margin: 0;
}

.device-camera-tree .ant-tree-treenode{
  width: 99%;
}

.customer-tree .ant-tree-switcher{
  display: none;
}

.customer-tree .ant-tree-node-content-wrapper{
  padding-left: 15px;
}

/* Tree node customization */
.ant-tree-node-content-wrapper {
  width: 100%;
}

.ant-tree-node-selected {
  background-color: #e6f7ff !important;
}

.ant-tree-treenode-disabled .ant-tree-node-content-wrapper {
  cursor: not-allowed !important;
}

/* 摄像头详情抽屉样式 */
.drawer-title {
  display: flex;
  flex-direction: column;
}

.drawer-subtitle {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 4px;
  font-weight: normal;
}

.camera-detail-container {
  padding: 12px 0;
}

.camera-detail-section {
  margin-bottom: 24px;
  background-color: #fafafa;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.camera-detail-section h3 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
  color: #1890ff;
  display: flex;
  align-items: center;
}

.camera-detail-section h3::before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 16px;
  background-color: #1890ff;
  margin-right: 8px;
  border-radius: 2px;
}

.detail-item {
  display: flex;
  margin-bottom: 12px;
  line-height: 22px;
  padding: 4px 0;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  min-width: 110px;
  color: rgba(0, 0, 0, 0.65);
  font-weight: 500;
  padding-right: 12px;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
  color: rgba(0, 0, 0, 0.85);
  word-break: break-word;
}

.detail-item:hover {
  background-color: rgba(24, 144, 255, 0.05);
  border-radius: 4px;
  padding-left: 8px;
  transition: all 0.3s;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .toolbar-matrix {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .group-sider {
    display: none;
  }
  
  .toolbar-matrix {
    grid-template-columns: 1fr;
  }
  
  .search-filter {
    flex-direction: column;
    gap: 16px;
  }
  
  .search-left,
  .search-right {
    width: 100%;
  }
}