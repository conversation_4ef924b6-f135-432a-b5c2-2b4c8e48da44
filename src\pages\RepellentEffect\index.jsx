import { useState, useEffect, useRef } from "react";
import {
  Card,
  Button,
  Space,
  Select,
  DatePicker,
  Tabs,
  Layout,
  message,
  TimePicker,
  Form,
  Spin
} from "antd";
import { DownloadOutlined } from "@ant-design/icons";
import { debounce } from "lodash";
import { getDeviceRepellentList } from "../../service/deviceRepellent";
import {
  getEffectsStatisticsData,
  exportDeviceStatus,
} from "./../../service/statistics";

import echarts from "./../../utils/echart";
import dayjs from "dayjs";

import useGroup from "../../hooks/useGroup";
import "./styles.css";
import { use } from "react";

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Sider, Content } = Layout;

const RepellentEffect = () => {
  const [repellentEffectData, setRepellentEffectData] = useState({});
  const [tab, setTab] = useState("overview");
  const [selectedDevice1, setSelectedDevice1] = useState([]); // tab1选中的驱鼠器
  const [deviceRepellentData, setDeviceRepellentData] = useState([]); // 获取驱鼠器数据
  const [selectedCamera2, setSelectedCamera2] = useState([]); // tab2选中的摄像头
  const [selected1Date, setSelected1Date] = useState(
    dayjs().subtract(1, "day")
  );
  const [selected2Date, setSelected2Date] = useState(dayjs());
  const [loading, setLoading] = useState(true); // 加载loading
  const [deviceRepellentLoading, setDeviceRepellentLoading] = useState(false); //摄像头loading
  const [searchDeviceData, setSearchDeviceData] = useState({
    keyword: "",
    size: 50,
    page: 0,
  }); // 分页查询驱鼠器
  const [totalPages, setTotalPages] = useState(0);
  const [searchData, setSearchData] = useState({
    dateType: "hour",
    startTime: dayjs().format("YYYY-MM-DD 00:00:00"),
    endTime: dayjs().format("YYYY-MM-DD 23:59:59"),
    deviceIds: null,
    firstStartTime: dayjs().startOf("day").format("HH:mm:ss"),
    firstEndTime: dayjs().endOf("day").format("HH:mm:ss"),
    // isNotGroup: 1,
    // regionId: "",
    // customerId: '',
    // groupType: 1
  });

  const chartInstance1 = useRef(null);
  const chartInstance2 = useRef(null);

  const [timeRange, setTimeRange] = useState("hour");
  const [dateRange, setDateRange] = useState([dayjs(), dayjs()]);
  const [timePicker, setTimePick] = useState([
    dayjs().startOf("day"),
    dayjs().endOf("day"),
  ]);

  const [gropNode, gropSearch] = useGroup();

  // chart初始化
  const chartInit1 = (chartData) => {
    let chartDom = document.getElementById("effect-chart1");
    if (!chartDom) {
      return;
    }

    if (!chartInstance1.current) {
      chartInstance1.current = echarts.init(chartDom);
    }

    // 检查是否有数据
    const hasData = chartData && chartData.length > 0;
    console.log("chartData:", chartData, "hasData:", hasData);

    // 默认时间轴数据（0-23点）
    const defaultTimeData = Array.from({ length: 24 }, (_, i) => `${i}:00`);

    const option = {
      // 当没有数据时显示标题
      ...(!hasData && {
        title: {
          text: "暂无驱鼠记录",
          left: "center",
          top: "middle",
          textStyle: {
            fontSize: 18,
            fontWeight: "bold",
            color: "#666",
          },
        },
      }),
      grid: {
        left: "5%",
        right: "5%",
        bottom: "10%",
        top: "8%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            show: false,
            backgroundColor: "#6a7985",
            fontSize: "12px",
          },
        },
      },
      xAxis: {
        type: "category",
        boundaryGap: true,
        axisLabel: {
          fontSize: "12px", // 可选：调整字体大小
        },
        axisLine: {
          show: true, // 显示x轴轴线
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        data: hasData ? chartData.map((item) => item.time) : defaultTimeData,
      },
      yAxis: {
        type: "value",
        name: "次",
        nameLocation: "end",
        nameGap: 10,
        minInterval: 1,
        min: 0,
        max: hasData ? undefined : 100,
        nameTextStyle: {
          fontSize: "12px",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
        axisLine: {
          show: true, // 显示y轴轴线
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
      },
      // 当没有数据时显示提示文字
      ...(!hasData && {
        graphic: [
          {
            type: "text",
            left: "center",
            top: "middle",
            z: 100,
            style: {
              text: "暂无驱鼠记录",
              fontSize: 18,
              fontWeight: "bold",
              fill: "#666",
            },
          },
        ],
      }),
      ...(chartData &&
        chartData.length > 30 && {
          toolbox: {
            feature: {
              dataZoom: {
                yAxisIndex: "none",
                title: {
                  zoom: "区域缩放",
                  back: "区域缩放还原",
                },
              },
              restore: {
                title: "还原",
              },
            },
            right: 20,
          },
          dataZoom: [
            {
              type: "slider",
              xAxisIndex: 0,
              start: 0,
              end: 100,
            },
            {
              type: "inside",
              xAxisIndex: [0],
              zoomLock: false,
              moveOnMouseMove: true,
              moveOnMouseWheel: true,
            },
          ],
        }),
      series: [
        {
          name: "",
          type: "bar",
          data: hasData
            ? chartData.map((item) => item.count)
            : Array(24).fill(0),
          ...(chartData && chartData.length < 10
            ? {
                barWidth: 30,
              }
            : {}),
        },
      ],
    };
    option && chartInstance1.current.setOption(option, true);
  };

  // chart初始化
  const chartInit2 = (chartData) => {
    let chartDom = document.getElementById("effect-chart2");
    if (!chartDom) {
      return;
    }

    if (!chartInstance2.current) {
      chartInstance2.current = echarts.init(chartDom);
    }

    const startDate = selected1Date.isBefore(selected2Date)
      ? selected1Date
      : selected2Date;
    const endDate = selected1Date.isAfter(selected2Date)
      ? selected1Date
      : selected2Date;

    const option = {
      grid: {
        left: "5%",
        right: "5%",
        bottom: "10%",
        top: "5%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            show: false,
            backgroundColor: "#6a7985",
            fontSize: "12px",
          },
        },
      },
      legend: {
        data: [startDate.format("YYYY-MM-DD"), endDate.format("YYYY-MM-DD")],
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        axisLabel: {
          fontSize: "12px", // 可选：调整字体大小
        },
        data: chartData?.firstTime?.map((item) => item.hour || item.date) || [],
      },
      yAxis: {
        type: "value",
        name: "",
        nameLocation: "center",
        nameGap: 40,
        nameTextStyle: {
          fontSize: "12px",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      series: [
        {
          name: startDate.format("YYYY-MM-DD"),
          type: "line",
          // stack: 'Total',
          data: chartData?.firstTime?.map((item) => item.count) || [],
        },
        {
          name: endDate.format("YYYY-MM-DD"),
          type: "line",
          // stack: 'Total',
          data: chartData?.secondTime?.map((item) => item.count) || [],
        },
      ],
    };
    option && chartInstance2.current.setOption(option, true);
  };

  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
    setSearchData((prev) => ({
      ...prev,
      startTime: dates[0].format("YYYY-MM-DD 00:00:00"),
      endTime: dates[1].format("YYYY-MM-DD 23:59:59"),
    }));
  };

  const renderRangePicker = () => {
    return (
      <RangePicker
        value={dateRange}
        allowClear={false}
        onChange={handleDateRangeChange}
        disabledDate={(current) => {
          return current && current > dayjs().endOf("day");
        }}
      />
    );
  };

  const handleExport = async () => {
    const startDate = selected1Date.isBefore(selected2Date)
      ? selected1Date
      : selected2Date;
    const endDate = selected1Date.isAfter(selected2Date)
      ? selected1Date
      : selected2Date;
    try {
      const result = await exportDeviceStatus({
        ...searchData,
        startTime: dateRange[0].format("YYYY-MM-DD 00:00:00"),
        endTime: dateRange[1].format("YYYY-MM-DD 23:59:59"),
        secondStartTime: startDate.format("YYYY-MM-DD 00:00:00"),
        secondEndTime: endDate.format("YYYY-MM-DD 23:59:59"),
        isNotGroup: gropSearch.isNotGroup,
        regionId: gropSearch.groupId,
        customerId: gropSearch.orgId,
        groupType: gropSearch.groupType,
      });
      const blob = new Blob([result], { type: "application/xlsx" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "download.xlsx";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Export failed:", error);
    }
  };

  // 获取驱鼠器列表
  const fetchDiviceList = async (obj = {}) => {
    try {
      const res = await getDeviceRepellentList({
        ...searchDeviceData,
        ...gropSearch,
        ...obj,
      });
      setTotalPages(res.totalPages);
      return res.content;
    } catch (error) {
      console.error("Failed to fetch camera list:", error);
    }
  };

  // 下拉搜索
  const handleSearch = async (value) => {
    setDeviceRepellentLoading(true);
    try {
      const data = await fetchDiviceList({ keyword: value, page: 0 });
      setDeviceRepellentData([{ deviceNo: "全选", deviceId: "全选" }, ...data]);
    } catch (error) {
      console.error("Failed to search cameras:", error);
    } finally {
      setDeviceRepellentLoading(false);
    }
  };

  // 滚动加载
  const loadMore = async (e) => {
    if (deviceRepellentLoading) return;
    const bottom =
      e.target.scrollHeight === e.target.scrollTop + e.target.clientHeight;
    if (bottom && totalPages - 1 > searchDeviceData.page) {
      setSearchDeviceData({
        ...searchDeviceData,
        page: searchDeviceData.page + 1,
      });
      setDeviceRepellentLoading(true);
      const newOptions = await fetchDiviceList({
        page: searchDeviceData.page + 1,
      });
      setDeviceRepellentData((prevOptions) => [...prevOptions, ...newOptions]);
      setDeviceRepellentLoading(false);
    }
  };

  useEffect(() => {
    (async () => {
      // 获取驱鼠器列表
      if (gropSearch) {
        const res = await fetchDiviceList();
        if (res && res.length > 0) {
          setDeviceRepellentData([
            { deviceNo: "全选", deviceId: "全选" },
            ...res,
          ]);
          setSelectedDevice1(["全选"]);
          setSearchData((prev) => ({
            ...prev,
            deviceIds: [],
          }));
        } else {
          setSelectedDevice1([]);
          setRepellentEffectData({});
          setDeviceRepellentData([]);
          // tab === 'overview' ? setSelectedCamera1([]) : setSelectedCamera1([])
        }
      }
    })();
  }, [gropSearch]);

  const fetchData = async (obj = {}) => {
    if (!gropSearch) {
      return;
    }
    setLoading(true);
    try {
      const res = await getEffectsStatisticsData({
        ...searchData,
        ...obj,
        isNotGroup: gropSearch.isNotGroup,
        regionId: gropSearch.groupId,
        customerId: gropSearch.orgId,
        groupType: gropSearch.groupType,
      });
      setRepellentEffectData(res);
      setLoading(false);
    } catch (error) {
      console.log(error);
    }
  };

  function chartResize() {
    chartInstance1.current?.resize();
    // chartInstance2.current?.resize()
  }

  window.addEventListener("resize", chartResize);

  useEffect(() => {
    if (deviceRepellentData.length > 0 && selectedDevice1.length > 0) {
      fetchData();
    }
  }, [searchData]);

  useEffect(() => {
    chartInit1(repellentEffectData?.repeller || []);
    // chartInit2(repellentEffectData?.ratEventsTimeAtCameras || [])
    return () => {
      window.removeEventListener("resize", chartResize);
    };
  }, [repellentEffectData]);

  useEffect(() => {
    // 清理函数，在组件卸载时取消请求
    return () => {
      if (window.cancelToken) {
        window.cancelToken(); // 取消请求
      }
    };
  }, []);

  // const renderCameraSelect = (ifMultiple = false) => {
  //   return (
  //     <Select
  //       showSearch
  //       allowClear
  //       mode={ifMultiple ? 'multiple' : 'default'}
  //       placeholder='请选择摄像头编号'
  //       onPopupScroll={loadMore}
  //       onSearch={debounce(handleSearch, 300)}
  //       loading={deviceRepellentLoading}
  //       style={{ width: '100%' }}
  //       optionFilterProp='children'
  //       filterOption={false}
  //       value={tab === 'overview' ? selectedCamera1 : selectedCamera2}
  //       onChange={value => {
  //         if (ifMultiple && value && value.length > 5) {
  //           value = value.slice(0, 5)
  //           message.warning('最多只能选择5个摄像头')
  //           return
  //         }
  //         if (tab === 'overview') {
  //           setSelectedCamera1(value)
  //           setSearchData(prev => ({
  //             ...prev,
  //             cameraIds: value
  //           }))
  //         } else {
  //           setSelectedCamera2([value])
  //           setSearchData(prev => ({
  //             ...prev,
  //             cameraIds: [value]
  //           }))
  //         }
  //       }}
  //       maxTagCount={5}
  //       maxTagTextLength={10}
  //       maxTagPlaceholder={omittedValues => `+ ${omittedValues.length} 个...`}
  //       onDropdownVisibleChange={async open => {
  //         if (open) {
  //           const tempSearch = {
  //             keyword: '',
  //             size: 50,
  //             page: 0
  //           }
  //           const res = await fetchCameraList(tempSearch)
  //           setSearchCameraData(tempSearch)
  //           setCameraData(res)
  //           // if (
  //           //   !ifMultiple &&
  //           //   res &&
  //           //   res.length > 0 &&
  //           //   !selectedCamera2.length
  //           // ) {
  //           //   setSelectedCamera2(res[0].cameraId)
  //           // } else if (
  //           //   ifMultiple &&
  //           //   res &&
  //           //   res.length > 0 &&
  //           //   !selectedCamera1.length
  //           // ) {
  //           //   if (res.length >= 2) {
  //           //     setSelectedCamera1([res[0].cameraId, res[1].cameraId])
  //           //   } else if (res.length === 1) {
  //           //     setSelectedCamera1(res[0].cameraId)
  //           //   }
  //           // }
  //         }
  //       }}
  //     >
  //       {cameraData?.map(option => (
  //         <Option key={option.cameraId} value={option.cameraId}>
  //           {option.cameraNo}
  //         </Option>
  //       ))}
  //     </Select>
  //   )
  // }

  return (
    <div className="repellent-effect">
      {/* Header */}
      <div className="repellent-effect-page-header">
        <div className="header-title"></div>
        {/* <div className='header-actions'>
          <Space>
            <Button icon={<DownloadOutlined />} onClick={handleExport}>
              导出
            </Button>
          </Space>
        </div> */}
      </div>
      <Layout>
        <Sider width={260} className="repellent-effect-group-sider">
          {gropNode()}
        </Sider>
        <Content className="repellent-effect-content">
          {/* Main Content */}
          <Tabs
            defaultActiveKey={tab}
            onChange={(value) => {
              setTab(value);
              if (value === "timeAnalysis") {
                const startDate = selected1Date.isBefore(selected2Date)
                  ? selected1Date
                  : selected2Date;
                const endDate = selected1Date.isAfter(selected2Date)
                  ? selected1Date
                  : selected2Date;
                setSearchData((prev) => ({
                  ...prev,
                  startTime: startDate.format("YYYY-MM-DD 00:00:00"),
                  endTime: endDate.format("YYYY-MM-DD 23:59:59"),
                  deviceIds: selectedCamera2,
                  firstStartTime: timePicker[0].format("HH:mm:ss"),
                  firstEndTime: timePicker[1].format("HH:mm:ss"),
                  dateType: "day",
                }));
              } else {
                setSearchData((prev) => ({
                  ...prev,
                  startTime: dateRange[0].format("YYYY-MM-DD 00:00:00"),
                  endTime: dateRange[1].format("YYYY-MM-DD 23:59:59"),
                  deviceIds: selectedDevice1,
                }));
              }
            }}
            items={[
              {
                key: "overview",
                label: "效果概览",
                children: (
                  <>
                    <Card className="overview-filter-bar">
                      <Form layout="inline">
                        <Form.Item label="统计方式">
                          <Select
                            value={timeRange}
                            onChange={(value) => {
                              setTimeRange(value);
                              setSearchData((prev) => ({
                                ...prev,
                                dateType: value,
                              }));
                            }}
                            style={{ width: 120 }}
                          >
                            <Option value="hour">按小时</Option>
                            <Option value="day">按天</Option>
                            <Option value="month">按月</Option>
                            <Option value="year">按年</Option>
                          </Select>
                        </Form.Item>
                        <Form.Item label="日期范围">
                          {renderRangePicker()}
                        </Form.Item>
                        <Form.Item label="驱鼠器编号">
                          <div style={{ minWidth: 200 }}>
                            <Select
                              showSearch
                              allowClear
                              mode="multiple"
                              placeholder="请选择驱鼠器编号"
                              maxTagCount={4}
                              onPopupScroll={loadMore}
                              onSearch={debounce(handleSearch, 300)}
                              loading={deviceRepellentLoading}
                              style={{ width: "100%" }}
                              optionFilterProp="children"
                              filterOption={false}
                              onDeselect={(value) => {
                                if (window.cancelToken) {
                                  window.cancelToken();
                                }
                                setSearchData((prev) => ({
                                  ...prev,
                                  deviceIds: selectedDevice1.filter(
                                    (item) => item != value
                                  ),
                                }));
                              }}
                              value={selectedDevice1}
                              onChange={(value) => {
                                // 如果选择了全选，清空其他选项
                                if (value.includes("全选")) {
                                  setSelectedDevice1(["全选"]);
                                } else {
                                  setSelectedDevice1(value);
                                }

                                if (value.length === 0) {
                                  setRepellentEffectData({});
                                }
                              }}
                              onBlur={() => {
                                setSearchData((prev) => ({
                                  ...prev,
                                  deviceIds: selectedDevice1.filter(
                                    (item) => item != "全选"
                                  ),
                                }));
                                // if (selectedDevice1.includes('全选')) {
                                //   fetchData({
                                //     deviceIds: []
                                //   })
                                // }
                              }}
                              onDropdownVisibleChange={async (open) => {
                                if (open) {
                                  const tempSearch = {
                                    keyword: "",
                                    size: 50,
                                    page: 0,
                                  };
                                  const res = await fetchDiviceList(tempSearch);
                                  setSearchDeviceData(tempSearch);
                                  if (res && res.length > 0) {
                                    setDeviceRepellentData([
                                      { deviceNo: "全选", deviceId: "全选" },
                                      ...res,
                                    ]);
                                  } else {
                                    setDeviceRepellentData([]);
                                  }
                                }
                              }}
                            >
                              {deviceRepellentData?.map((option) => (
                                <Option
                                  key={option.deviceId}
                                  value={option.deviceId}
                                  disabled={
                                    (option.deviceId === "全选" &&
                                      selectedDevice1.length > 0 &&
                                      !selectedDevice1.includes("全选")) ||
                                    (option.deviceId !== "全选" &&
                                      selectedDevice1.includes("全选"))
                                  }
                                >
                                  {option.deviceNo}
                                </Option>
                              ))}
                            </Select>
                          </div>
                        </Form.Item>
                      </Form>
                    </Card>
                    <Card
                      title="驱鼠次数统计"
                      className="repellent-effect-cart"
                      loading={false}
                    >
                      <Spin spinning={loading} size="large" tip="加载中...">
                        <div
                          id="effect-chart1"
                          style={{
                            width: "100%",
                            minHeight: "55vh",
                            padding: 20,
                          }}
                        ></div>
                      </Spin>
                    </Card>
                  </>
                ),
              },
              // {
              //   key: 'timeAnalysis',
              //   label: '时间分析',
              //   children: (
              //     <>
              //       <Card className='overview-filter-bar'>
              //         <Form layout='inline'>
              //           <Form.Item label='日期1'>
              //             <DatePicker
              //               style={{ width: '100%' }}
              //               value={selected1Date}
              //               onChange={date => {
              //                 if (
              //                   date &&
              //                   selected2Date &&
              //                   date.isSame(selected2Date, 'day')
              //                 ) {
              //                   message.warning('不能选择相同的日期')
              //                   return
              //                 }
              //                 setSelected1Date(date)
              //                 if (date) {
              //                   const startDate = date.isBefore(selected2Date)
              //                     ? date
              //                     : selected2Date
              //                   const endDate = date.isAfter(selected2Date)
              //                     ? date
              //                     : selected2Date
              //                   setSearchData(prev => ({
              //                     ...prev,
              //                     startTime: startDate.format(
              //                       'YYYY-MM-DD 00:00:00'
              //                     ),
              //                     endTime: endDate.format('YYYY-MM-DD 23:59:59')
              //                   }))
              //                 }
              //               }}
              //               allowClear={false}
              //               disabledDate={current => {
              //                 return (
              //                   current &&
              //                   (current > dayjs().endOf('day') ||
              //                     (selected2Date &&
              //                       current.isSame(selected2Date, 'day')))
              //                 )
              //               }}
              //             />
              //           </Form.Item>
              //           <Form.Item label='日期2'>
              //             <DatePicker
              //               style={{ width: '100%' }}
              //               value={selected2Date}
              //               onChange={date => {
              //                 if (
              //                   date &&
              //                   selected1Date &&
              //                   date.isSame(selected1Date, 'day')
              //                 ) {
              //                   message.warning('不能选择相同的日期')
              //                   return
              //                 }
              //                 setSelected2Date(date)
              //                 if (date) {
              //                   const startDate = selected1Date.isBefore(date)
              //                     ? selected1Date
              //                     : date
              //                   const endDate = selected1Date.isAfter(date)
              //                     ? selected1Date
              //                     : date
              //                   setSearchData(prev => ({
              //                     ...prev,
              //                     startTime: startDate.format(
              //                       'YYYY-MM-DD 00:00:00'
              //                     ),
              //                     endTime: endDate.format('YYYY-MM-DD 23:59:59')
              //                   }))
              //                 }
              //               }}
              //               allowClear={false}
              //               disabledDate={current => {
              //                 return (
              //                   current &&
              //                   (current > dayjs().endOf('day') ||
              //                     (selected1Date &&
              //                       current.isSame(selected1Date, 'day')))
              //                 )
              //               }}
              //             />
              //           </Form.Item>
              //           <Form.Item label='时间范围'>
              //             <TimePicker.RangePicker
              //               defaultValue={timePicker}
              //               format={'HH:mm:ss'}
              //               onChange={time => {
              //                 if (time) {
              //                   setTimePick(time)
              //                   setSearchData(prev => ({
              //                     ...prev,
              //                     firstStartTime: time[0].format('HH:mm:ss'),
              //                     firstEndTime: time[1].format('HH:mm:ss')
              //                   }))
              //                 }
              //               }}
              //             />
              //           </Form.Item>
              //           <Form.Item label='摄像头'>
              //             <div style={{ minWidth: 200 }}>
              //               {renderCameraSelect()}
              //             </div>
              //           </Form.Item>
              //         </Form>
              //       </Card>
              //       <Card
              //         title='不同日期同一时间驱鼠次数统计'
              //         className='repellent-effect-cart'
              //       >
              //         <div
              //           id='effect-chart2'
              //           style={{
              //             width: '100%',
              //             minHeight: '55vh',
              //             padding: 20
              //           }}
              //         >
              //           暂无数据
              //         </div>
              //       </Card>
              //     </>
              //   )
              // }
            ]}
          />
        </Content>
      </Layout>
    </div>
  );
};

export default RepellentEffect;
