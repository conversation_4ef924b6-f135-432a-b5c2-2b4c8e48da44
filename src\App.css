#root {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  text-align: left;
}

.layout-div.ant-layout {
  min-height: 100vh;
  width: 100vw;
}

.ant-layout-content.detail-content {
  height: calc(100vh - 64px);
  overflow-y: auto;
  padding: 16px;
  background-color: #f0f2f5;
}

/* .ant-menu-item-selected {
  background-color: #1890ff !important;
  color: #ffffff !important;
} */

/* .ant-menu-submenu-inline .ant-menu-item-selected {
  background-color: #1890ff !important;
  color: #ffffff !important;
} */

/* 增加选择器优先级 */
/* .ant-menu.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff !important;
  color: #ffffff !important;
} */

/* .ant-menu.ant-menu-dark .ant-menu-submenu-inline .ant-menu-item-selected {
  background-color: #1890ff !important;
  color: #ffffff !important;
} */

/* 添加二级菜单的鼠标悬停样式 */
/* .ant-menu-item:hover {
  background-color: #1890ff !important;
  color: #ffffff !important;
} */

/* .ant-menu-submenu-inline .ant-menu-item:hover {
  background-color: #1890ff !important;
  color: #ffffff !important;
} */

/* 增强选择器优先级 */
/* .ant-menu.ant-menu-dark .ant-menu-item:hover {
  background-color: #1890ff !important;
  color: #ffffff !important;
} */

/* .ant-menu.ant-menu-dark .ant-menu-submenu-inline .ant-menu-item:hover {
  background-color: #1890ff !important;
  color: #ffffff !important;
} */

.ant-menu .ant-menu-item {
  border-radius: 0px;
}

.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):hover {
  background-color: #1D3B97;
  border-radius: 0px;
}

.layout-header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.group-tree-scroll {
  height: calc(100% - 150px);
  overflow-y: auto;
}

/*  
设备温度
*/
.icon_tab_tem {
  display: flex;
  align-items: center;
}

.icon_tab_tem>span {
  display: inline-block;
  width: 14px;
  height: 14px;
  background: url("/temperature2.png") no-repeat;
  background-size: 100% 100%;
}

.ant-tabs-tab:hover .icon_tab_tem>span {
  display: inline-block;
  width: 14px;
  height: 14px;
  background: url("/temperature3.png") no-repeat;
  background-size: 100% 100%;
}

.ant-tabs-tab-active .icon_tab_tem>span {
  display: inline-block;
  width: 14px;
  height: 14px;
  background: url("/temperature3.png") no-repeat;
  background-size: 100% 100%;
}


/*  
设备湿度
*/
.icon_tab_hum {
  display: flex;
  align-items: center;
}

.icon_tab_hum>span {
  display: inline-block;
  width: 14px;
  height: 14px;
  background: url("/humidity2.png") no-repeat;
  background-size: 100% 100%;
}

.ant-tabs-tab:hover .icon_tab_hum>span {
  display: inline-block;
  width: 14px;
  height: 14px;
  background: url("/humidity3.png") no-repeat;
  background-size: 100% 100%;
}

.ant-tabs-tab-active .icon_tab_hum>span {
  display: inline-block;
  width: 14px;
  height: 14px;
  background: url("/humidity3.png") no-repeat;
  background-size: 100% 100%;
}

.user-dropdown {
  height: 43px;
  border: 1px solid #D5D5D5;
  border-radius: 25px;
  display: flex;
  align-items: center;
  padding-right: 12px;
  padding-left: 2px;
  padding-bottom: 2px;
  cursor: pointer;
}