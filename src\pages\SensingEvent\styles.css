.sensing-event {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .sensing-record {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .statistics-cards .ant-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .main-content {
    flex-direction: column;
  }

  .group-sider {
    width: 100% !important;
    max-width: 100% !important;
    margin-right: 0;
    margin-bottom: 24px;
  }
}
.searchBox {
  width: 100%;
  height: 80px;
  padding: 24px;
  margin-bottom: 24px;
  background-color: #fff;
  border-radius: 8px;
}
.tableBox {
  flex: 1;
  background-color: #fff;
  margin-bottom: 24px;
  border-radius: 8px;
  padding: 24px;
}

.contentBox {
  flex: 1;
  margin-bottom: 24px;
}

/* 地理分组样式 */
.group-sider {
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

.group-header {
  padding: 16px 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.group-tree-scroll {
  height: calc(100% - 150px);
  overflow-y: auto;
  padding: 0 16px;
}

/* 地理位置树样式 */
.location-tree-card .ant-card-head {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px 8px 0 0;
}

.location-tree-card .ant-card-head-title {
  color: #fff;
  font-weight: 600;
  font-size: 14px;
}

/* 树形卡片Tabs样式 */
.tree-card .ant-tabs {
  height: 100%;
}

.tree-card .ant-tabs-content-holder {
  height: calc(100% - 46px);
  overflow: hidden;
}

.tree-card .ant-tabs-tabpane {
  height: 100%;
}

.tree-card .ant-tabs-tab {
  padding: 8px 16px;
  font-weight: 500;
}

.tree-card .ant-tabs-tab .anticon {
  margin-right: 6px;
}

.tree-card .ant-tabs-ink-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.tree-card .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #667eea;
  font-weight: 600;
}

.location-tree-card .ant-tree {
  background: transparent;
}

.location-tree-card .ant-tree .ant-tree-node-content-wrapper {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.location-tree-card .ant-tree .ant-tree-node-content-wrapper:hover {
  background-color: #f0f5ff;
}

.location-tree-card .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.location-tree-card .ant-tree .ant-tree-title {
  font-size: 13px;
  color: #262626;
}

/* 表格卡片样式 */
.table-card .ant-card-body {
  padding: 0;
}

.table-card .ant-table-wrapper {
  border-radius: 0 0 8px 8px;
}

.table-card .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
}

.table-card .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 搜索框优化 */
.searchBox .ant-form-item {
  margin-bottom: 0;
}

.searchBox .ant-input,
.searchBox .ant-picker,
.searchBox .ant-select-selector {
  border-radius: 6px;
}

.searchBox .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}


