import { useEffect, useRef } from 'react'
import echarts from './../../../utils/echart'
import PropTypes from 'prop-types'

const DeviceHealth = ({ data, isActive = false }) => {
  const chartInstance = useRef(null)

  useEffect(() => {
    if (isActive && chartInstance.current) {
      setTimeout(() => {
        chartInstance.current.resize()
      }, 0)
    }
  }, [isActive])

  useEffect(() => {
    chartInit(data.chart2)
    // Add resize handler
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize()
      }
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [data?.chart2])

  // chart初始化
  const chartInit = chartData => {
    let chartDom = document.getElementById('chart2')
    if (!chartDom) {
      return
    }

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartDom)
    }

    const option = {
      grid: {
        left: '6%',
        right: '4%',
        bottom: '10%',
        top: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            show: false,
            backgroundColor: '#6a7985',
            fontSize: '12px'
          }
        }
      },
      legend: {
        show: false
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLabel: {
          fontSize: '12px' // 可选：调整字体大小
        },
        axisLine: {
          show: true, // 显示y轴轴线
          lineStyle: {
            color: '#B0B0B0',
            width: 1,
          },
        },
        data: chartData.x?.length ? chartData.x : []
      },
      yAxis: {
        type: 'value',
        name: '℃',
        // interval: 1,
        // min: 0,
        // max: 2,
        nameLocation: 'end',
        nameGap: 10,
        nameTextStyle: {
          fontSize: '12px'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        },
        axisLine: {
          show: true, // 显示y轴轴线
          lineStyle: {
            color: '#B0B0B0',
            width: 1,
          },
        },
        axisTick: {
          show: false
        }
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none',
            title: {
              zoom: '区域缩放',
              back: '区域缩放还原'
            }
          },
          restore: {
            title: '还原'
          }
        },
        right: 20
      },
      dataZoom: [
        {
          type: 'slider',
          xAxisIndex: 0,
          start: 0,
          end: 100
        },
        {
          type: 'inside',
          xAxisIndex: [0],
          zoomLock: false,
          moveOnMouseMove: true,
          moveOnMouseWheel: true
        }
      ],
      series: [
        {
          type: 'line',
          smooth: true,
          emphasis: {
            focus: 'series'
          },
          data: chartData?.y?.length ? chartData.y : []
        }
      ]
    }
    option && chartInstance.current.setOption(option, true)
  }

  return (
    <div className='health-content'>
      <div
        style={{ width: '100%', height: '66vh', padding: 20 }}
        id='chart2'
      ></div>
    </div>
  )
}

DeviceHealth.propTypes = {
  data: PropTypes.shape({
    chart2: PropTypes.shape({
      x: PropTypes.array,
      y: PropTypes.array
    })
  }).isRequired,
  isActive: PropTypes.bool
}

export default DeviceHealth
