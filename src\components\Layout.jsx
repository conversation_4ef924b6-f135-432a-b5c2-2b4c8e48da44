import React, { useState, useEffect } from 'react'
import {
  Layout,
  Menu,
  Button,
  Input,
  Avatar,
  Space,
  Tooltip,
  Dropdown,
  Breadcrumb,
  message,
  Badge
} from 'antd'
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom'
import Cookies from 'js-cookie'
import UserSettingsModal from './UserSettingsModal'
import { usrManagementApi } from '../service/userManagement'
import { layoutApi } from '../service/layout'

import {
  DashboardOutlined,
  DesktopOutlined,
  RadarChartOutlined,
  ThunderboltOutlined,
  BarChartOutlined,
  ToolOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  SecurityScanOutlined,
  AlertOutlined,
  LogoutOutlined,
  UserSwitchOutlined,
  BugOutlined,
  DownOutlined 
} from '@ant-design/icons'
import NotificationDrawer from './NotificationDrawer'
import Dashboard from '../pages/Dashboard'
import DeviceDistribution from '../pages/DeviceDistribution'
import DeviceGroups from '../pages/DeviceGroups'
import DeviceRepellent from '../pages/DeviceRepellent'
import DeviceCamera from '../pages/DeviceCamera'
import DeviceMonitor from '../pages/DeviceMonitor'
import TenantList from '../pages/TenantList'
import TenantOrg from '../pages/TenantOrg'
import SensingRecord from '../pages/SensingRecord'
import SensingSchedule from '../pages/SensingSchedule'
import SensingEvent from '../pages/SensingEvent'
import StrategyScene from '../pages/StrategyScene'
import StrategyConfig from '../pages/StrategyConfig'
import DeviceStatistics from '../pages/DeviceStatistics'
import MouseActivity from '../pages/MouseActivity'
import UpgradePackages from '../pages/UpgradePackages'
import MaintenanceAudio from '../pages/MaintenanceAudio'
import MaintenanceUpgrade from '../pages/MaintenanceUpgrade'
import MaintenanceMonitor from '../pages/MaintenanceMonitor'
import AlertInfo from '../pages/AlertInfo'
import AlertsNotice from '../pages/AlertsNotice'
import SecurityAudit from '../pages/SecurityAudit'
import UserManagement from '../pages/UserManagement'
import RolePermission from '../pages/RolePermission'
import LogManagement from '../pages/LogManagement'
import DictionaryConfig from '../pages/DictionaryConfig'
import FileManagement from '../pages/FileManagement'
import SystemConfig from '../pages/SystemConfig'
import RepellentEffect from '../pages/RepellentEffect'
import Organization from '../pages/Organization'

const { Header, Sider, Content } = Layout
const { Search } = Input

const MainLayout = () => {
  const [collapsed, setCollapsed] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [notificationDrawerVisible, setNotificationDrawerVisible] =
    useState(false)
  const [openKeys, setOpenKeys] = useState([])
  const [selectedMenuItemKey, setSelectedMenuItemKey] = useState('')
  const [userSettingsVisible, setUserSettingsVisible] = useState(false)
  const [userAvatar, setUserAvatar] = useState(null)
  const [username, setUsername] = useState('Admin')
  const [apiMenuItems, setApiMenuItems] = useState([])
  const navigate = useNavigate()
  const location = useLocation()
  const BreadCrumb = {
    "/": [
      {
        title: "Dashboard",
      },
      {
        title: "首页大屏"
      }
    ],
    "/device/monitor": [
      {
        title: "设备管理",
      },
      {
        title: "状态监测"
      }
    ],
      "/device/distribution": [
      {
        title: "设备管理",
      },
      {
        title: "设备分布"
      }
    ],
    "/device/repellent": [
      {
        title: "设备管理",
      },
      {
        title: "驱鼠器管理"
      }
    ],
    "/device/camera": [
      {
        title: "设备管理",
      },
      {
        title: "摄像头管理"
      }
    ],
    "/device/groups": [
      {
        title: "设备管理",
      },
      {
        title: "设备分组"
      }
    ],
    "/sensing/record": [
      {
        title: "智能感知",
      },
      {
        title: "驱鼠记录"
      }
    ],
    "/sensing/event": [
      {
        title: "智能感知",
      },
      {
        title: "鼠患事件"
      }
    ],
    "/sensing/schedule": [
      {
        title: "智能感知",
      },
      {
        title: "自适应调度"
      }
    ],
    "/strategy/scene": [
      {
        title: "驱鼠策略",
      },
      {
        title: "驱鼠场景"
      }
    ],
    "/strategy/config": [
      {
        title: "驱鼠策略",
      },
      {
        title: "策略配置"
      }
    ],
    "/statistics/device": [
      {
        title: "数据统计",
      },
      {
        title: "设备运行"
      }
    ],
    "/statistics/mouse": [
      {
        title: "数据统计",
      },
      {
        title: "鼠类活动"
      }
    ],
    "/statistics/effect": [
      {
        title: "数据统计",
      },
      {
        title: "驱赶效果"
      }
    ],
    "/maintenance/packages": [
      {
        title: "维护升级",
      },
      {
        title: "升级包管理"
      }
    ],
    "/maintenance/audio": [
      {
        title: "维护升级",
      },
      {
        title: "驱鼠音频"
      }
    ],
    "/maintenance/upgrade": [
      {
        title: "维护升级",
      },
      {
        title: "远程升级"
      }
    ],
    "/maintenance/monitor": [
      {
        title: "维护升级",
      },
      {
        title: "系统监控"
      }
    ],
    "/alerts/info": [
      {
        title: "告警通知",
      },
      {
        title: "告警信息"
      }
    ],
    "/alerts/notice": [
      {
        title: "告警通知",
      },
      {
        title: "通知公告"
      }
    ],
    "/system/users": [
      {
        title: "系统管理",
      },
      {
        title: "用户管理"
      }
    ],
    "/system/tenant": [
      {
        title: "系统管理",
      },
      {
        title: "租户管理"
      }
    ],
    "/system/organization": [
      {
        title: "系统管理",
      },
      {
        title: "组织管理"
      }
    ],
    "/system/roles": [
      {
        title: "系统管理",
      },
      {
        title: "角色权限"
      }
    ],
    "/system/logs": [
      {
        title: "系统管理",
      },
      {
        title: "日志查看"
      }
    ],
    "/system/dict": [
      {
        title: "系统管理",
      },
      {
        title: "字典配置"
      }
    ],
    "/system/files": [
      {
        title: "系统管理",
      },
      {
        title: "文件管理"
      }
    ],
    "/system/config": [
      {
        title: "系统管理",
      },
      {
        title: "系统配置"
      }
    ]
  } 

  // Define menu items configuration

  // const menuItems = [
  //   {
  //     key: "dashboard",
  //     icon: <DashboardOutlined />,
  //     label: "Dashboard",
  //     children: [
  //       {
  //         key: "dashboard-main",
  //         label: "首页大屏",
  //         path: "/",
  //       },
  //     ],
  //   },
  //   {
  //     key: "device",
  //     icon: <DesktopOutlined />,
  //     label: "设备管理",
  //     children: [
  //       {
  //         key: "device-monitor",
  //         label: "状态监测",
  //         path: "/device/monitor",
  //       },
  //       {
  //         key: "device-repellent",
  //         label: "驱鼠器管理",
  //         path: "/device/repellent",
  //       },
  //       {
  //         key: "device-camera",
  //         label: "摄像头管理",
  //         path: "/device/camera",
  //       },
  //       {
  //         key: "device-group",
  //         label: "设备分组",
  //         path: "/device/groups",
  //       },
  //     ],
  //   },
  //   {
  //     key: "sensing",
  //     icon: <RadarChartOutlined />,
  //     label: "智能感知",
  //     children: [
  //       {
  //         key: "sensing-record",
  //         label: "驱鼠记录",
  //         path: "/sensing/record",
  //       },
  //       {
  //         key: "sensing-event",
  //         label: "鼠患事件",
  //         path: "/sensing/event",
  //       },
  //       {
  //         key: "sensing-schedule",
  //         label: "自适应调度",
  //         path: "/sensing/schedule",
  //       },
  //     ],
  //   },
  //   {
  //     key: "strategy",
  //     icon: <ThunderboltOutlined />,
  //     label: "驱鼠策略",
  //     children: [
  //       {
  //         key: "strategy-scene",
  //         label: "驱鼠场景",
  //         path: "/strategy/scene",
  //       },
  //       {
  //         key: "strategy-config",
  //         label: "策略配置",
  //         path: "/strategy/config",
  //       },
  //     ],
  //   },
  //   {
  //     key: "statistics",
  //     icon: <BarChartOutlined />,
  //     label: "数据统计",
  //     children: [
  //       {
  //         key: "statistics-device",
  //         label: "设备运行",
  //         path: "/statistics/device",
  //       },
  //       {
  //         key: "statistics-mouse",
  //         label: "鼠类活动",
  //         path: "/statistics/mouse",
  //       },
  //       {
  //         key: "statistics-effect",
  //         label: "驱赶效果",
  //         path: "/statistics/effect",
  //       },
  //     ],
  //   },
  //   {
  //     key: "maintenance",
  //     icon: <ToolOutlined />,
  //     label: "维护升级",
  //     children: [
  //       {
  //         key: "maintenance-packages",
  //         label: "升级包管理",
  //         path: "/maintenance/packages",
  //       },
  //       {
  //         key: "maintenance-audio",
  //         label: "驱鼠音频",
  //         path: "/maintenance/audio",
  //       },
  //       {
  //         key: "maintenance-upgrade",
  //         label: "远程升级",
  //         path: "/maintenance/upgrade",
  //       },
  //       {
  //         key: "maintenance-monitor",
  //         label: "系统监控",
  //         path: "/maintenance/monitor",
  //       },
  //     ],
  //   },
  //   {
  //     key: "alerts",
  //     icon: <AlertOutlined />,
  //     label: "告警通知",
  //     children: [
  //       {
  //         key: "alerts-info",
  //         label: "告警信息",
  //         path: "/alerts/info",
  //       },
  //       // {
  //       //   key: 'alerts-notice',
  //       //   label: '通知公告',
  //       //   path: '/alerts/notice'
  //       // }
  //     ],
  //   },
  //   {
  //     key: "security",
  //     icon: <SecurityScanOutlined />,
  //     label: "安全控制",
  //     children: [
  //       {
  //         key: "security-audit",
  //         label: "操作审计",
  //         path: "/security/audit",
  //       },
  //     ],
  //   },
  //   {
  //     key: "system",
  //     icon: <SettingOutlined />,
  //     label: "系统管理",
  //     children: [
  //       {
  //         key: "system-users",
  //         label: "用户管理",
  //         path: "/system/users",
  //       },
  //       {
  //         key: "system-tenant",
  //         label: "租户管理",
  //         path: "/system/tenant",
  //       },
  //       {
  //         key: "system-organization",
  //         label: "组织管理",
  //         path: "/system/organization",
  //       },
  //       {
  //         key: "system-roles",
  //         label: "角色权限",
  //         path: "/system/roles",
  //       },
  //       {
  //         key: "system-logs",
  //         label: "日志查看",
  //         path: "/system/logs",
  //       },
  //       {
  //         key: "system-dict",
  //         label: "字典配置",
  //         path: "/system/dict",
  //       },
  //       {
  //         key: "system-files",
  //         label: "文件管理",
  //         path: "/system/files",
  //       },
  //       {
  //         key: "system-config",
  //         label: "系统配置",
  //         path: "/system/config",
  //       },
  //     ],
  //   },
  // ];

  // 统一的菜单项查找函数，同时可以按路径或键名查找
  const findMenuItem = (pathOrKey, isPath = true) => {
    // 在单个菜单集合中递归查找
    const searchInMenus = menus => {
      for (const item of menus) {
        // 按路径或键名查找
        if (
          (isPath && item.path === pathOrKey) ||
          (!isPath && item.key === pathOrKey)
        ) {
          return { key: item.key, item, isParent: true }
        }
        if (item.children) {
          let childResult = null
          if (isPath) {
            // 按路径查找子项
            const childItem = item.children.find(
              child => child.path === pathOrKey
            )
            if (childItem) {
              childResult = {
                key: childItem.key,
                item: childItem,
                parent: item
              }
            }
          } else {
            // 按键名查找子项
            const childItem = item.children.find(
              child => child.key === pathOrKey
            )
            if (childItem) {
              childResult = {
                key: childItem.key,
                item: childItem,
                parent: item
              }
            }
          }
          if (childResult) return childResult
        }
      }
      return null
    }

    // 在API菜单中查找
    return searchInMenus(apiMenuItems)
  }

  // 添加useEffect来同步路由和菜单状态
  useEffect(() => {
    const result = findMenuItem(location.pathname, true)
    if (result) {
      setSelectedMenuItemKey(result.key)
      // 如果有父菜单，设置展开状态
      if (result.parent && !openKeys.includes(result.parent.key)) {
        setOpenKeys(prevKeys => [...prevKeys, result.parent.key])
      }
    }
  }, [location.pathname, apiMenuItems])

  
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }

  const handleRefresh = () => {
    // 保存当前页面路径
    const currentPath = window.location.pathname
    if (currentPath !== '/login') {
      localStorage.setItem('lastPage', currentPath)
    }

    // 检查并更新认证信息
    const token = localStorage.getItem('token')
    if (token) {
      // 尝试设置Cookie
      try {
        Cookies.set('token', token, {
          expires: 2 / 24,
          path: '/',
          sameSite: undefined,
          secure: undefined
        })
      } catch (e) {
        console.warn('刷新时设置Cookie失败:', e)
      }
    }

    // 执行刷新
    window.location.reload()
  }

  const handleMenuClick = ({ key }) => {
    setSelectedMenuItemKey(key)

    const result = findMenuItem(key, false)
    if (result) {
      // 如果是子菜单且有父菜单，保持父菜单展开
      if (result.parent && !openKeys.includes(result.parent.key)) {
        setOpenKeys(prev => [...prev, result.parent.key])
      }

      // 如果菜单项有路径，导航到该路径
      if (result.item.path) {
        navigate(result.item.path)
      }
    }
  }

  // User dropdown menu items
  const userMenuItems = {
    items: [
      {
        key: 'settings',
        label: '用户设置',
        icon: <SettingOutlined />,
        onClick: () => {
          setUserSettingsVisible(true)
        }
      },
      // {
      //   key: 'switch',
      //   label: '切换账号',
      //   icon: <UserSwitchOutlined />,
      //   onClick: async () => {
      //     try {
      //       await layoutApi.logout()
      //       // 清除保存的菜单数据
      //       localStorage.removeItem('userMenu')
      //       navigate('/login')
      //     } catch (error) {
      //       console.error('登出失败:', error)
      //     }
      //   }
      // },
      {
        type: 'divider'
      },
      {
        key: 'logout',
        label: '退出登录',
        icon: <LogoutOutlined />,
        onClick: async () => {
          try {
            await layoutApi.logout()
            // 清除保存的菜单数据
            localStorage.removeItem('userMenu')
            navigate('/login')
          } catch (error) {
            console.error('登出失败:', error)
          }
        }
      }
    ]
  }

  const onOpenChange = keys => {
    // 如果关闭所有菜单，则直接设置空数组
    if (keys.length === 0) {
      setOpenKeys([]);
      return;
    }
    
    // 获取最新打开的菜单（数组中的最后一个元素）
    const latestOpenKey = keys[keys.length - 1];
    
    // 只保留最新打开的菜单，关闭其他所有菜单
    setOpenKeys([latestOpenKey]);
  }

  // 获取用户信息，包括头像
  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const response = await usrManagementApi.getLoginUserInfo()
        if (response.code === 200 && response.data) {
          if (response.data.user && response.data.user.username) {
            setUsername(response.data.user.username)
            if (response.data.userProfile && response.data.userProfile.avatar) {
              // 使用avatar ID获取预签名URL
              const presignedResponse = await layoutApi.getFilePresignedUrl(
                response.data.userProfile.avatar
              )
              const avatarUrl = presignedResponse.url
              setUserAvatar(avatarUrl)
            }
          }
        }
      } catch (error) {
          // message.error('获取用户信息失败:', error)
      }
    }

    fetchUserInfo()
  }, [])

  // 根据菜单名称获取图标
  const getIconForMenu = menuName => {
    // 根据菜单名称返回对应的图标组件
    if (!menuName) return <DashboardOutlined />

    // 根据菜单名称包含的关键词返回对应图标
    if (
      menuName.includes('Dashboard') ||
      menuName.includes('首页') ||
      menuName.includes('大屏')
    ) {
      return <DashboardOutlined />
    } else if (
      menuName.includes('设备') ||
      menuName.includes('驱鼠器') ||
      menuName.includes('摄像头')
    ) {
      return <DesktopOutlined />
    } else if (menuName.includes('感知') || menuName.includes('驱鼠记录')) {
      return <RadarChartOutlined />
    } else if (menuName.includes('策略') || menuName.includes('驱鼠策略')) {
      return <ThunderboltOutlined />
    } else if (menuName.includes('统计') || menuName.includes('数据')) {
      return <BarChartOutlined />
    } else if (menuName.includes('维护') || menuName.includes('升级')) {
      return <ToolOutlined />
    } else if (menuName.includes('告警') || menuName.includes('通知')) {
      return <AlertOutlined />
    } else if (menuName.includes('安全') || menuName.includes('审计')) {
      return <SecurityScanOutlined />
    } else if (
      menuName.includes('系统') ||
      menuName.includes('配置') ||
      menuName.includes('管理')
    ) {
      return <SettingOutlined />
    } else {
      // 默认图标
      return <DashboardOutlined />
    }
  }

  // 新增：菜单数据转换函数
  const transformMenuData = menuData => {
    if (!Array.isArray(menuData) || menuData.length === 0) {
      return []
    }

    return menuData.map(item => {
      const menuItem = {
        key: item.id,
        icon: getIconForMenu(item.name),
        label: item.name
      }

      if (item.childrenList && item.childrenList.length > 0) {
        menuItem.children = item.childrenList.map(child => ({
          key: child.id,
          label: child.name,
          path:
            child.metadata && child.metadata.menu
              ? child.metadata.menu
              : `/menu/${item.id}/${child.id}`
        }))
      }
      return menuItem
    })
  }

  // 新增：获取用户菜单的useEffect
  useEffect(() => {
    const fetchUserMenu = async () => {
      try {
        // 检查是否有有效的token
        const currentToken = localStorage.getItem('token')
        if (!currentToken) {
          console.warn('没有有效的token，无法获取用户菜单')
          return
        }

        // 首先尝试从localStorage获取保存的菜单数据
        const savedMenu = localStorage.getItem('userMenu')
        const savedToken = localStorage.getItem('menuToken') // 保存菜单对应的token

        // 只有在token匹配时才使用缓存的菜单数据
        if (savedMenu && savedToken === currentToken) {
          const parsedMenu = JSON.parse(savedMenu)

          // 修复从localStorage获取的菜单数据中的图标问题
          const fixedMenu = parsedMenu.map(item => {
            // 重新生成图标组件
            const newItem = {
              ...item,
              icon: getIconForMenu(item.label)
            }

            // 处理子菜单
            if (newItem.children && newItem.children.length > 0) {
              newItem.children = newItem.children.map(child => ({
                ...child
              }))
            }

            return newItem
          })

          setApiMenuItems(fixedMenu)
        } else {
          // 如果没有保存的菜单数据或token不匹配，则调用API获取
          const response = await layoutApi.getUserMenu()
          console.log('response',response)
          if (response && response.code === 200 && response.data) {
            // 将API返回的菜单数据转换为Menu组件所需的格式
            const formattedMenuItems = transformMenuData(response.data)
            setApiMenuItems(formattedMenuItems)

            // 将转换后的菜单数据保存到localStorage，但移除icon属性（因为它是React组件，不能被序列化）
            const menuToSave = formattedMenuItems.map(item => {
              const { icon, ...rest } = item

              if (rest.children && rest.children.length > 0) {
                rest.children = rest.children.map(child => {
                  const { icon: childIcon, ...childRest } = child
                  return childRest
                })
              }

              return rest
            })

            localStorage.setItem('userMenu', JSON.stringify(menuToSave))
            localStorage.setItem('menuToken', currentToken) // 保存菜单对应的token
          } else {
            console.error('获取用户菜单失败:', response)
          }
        }
      } catch (error) {
        console.error('获取用户菜单出错:', error)
      }
    }

    fetchUserMenu()
  }, [])

  // // 添加页面刷新/关闭前的处理函数
  useEffect(() => {
    const handleBeforeUnload = () => {
      // 首先保存当前页面路径
      localStorage.setItem('lastPage', window.location.pathname)

      // 如果有token，确保所有存储位置保持一致
      if (localStorage.getItem('token')) {
        const token = localStorage.getItem('token')

        // 确保sessionStorage也有token
        sessionStorage.setItem('token', token)

        // 尝试设置Cookie
        try {
          Cookies.set('token', token, {
            expires: 2 / 24,
            path: '/',
            sameSite: undefined,
            secure: undefined
          })
        } catch (e) {
          console.warn('beforeUnload时设置Cookie失败:', e)
        }
      }
    }

    // 监听页面刷新/关闭事件
    window.addEventListener('beforeunload', handleBeforeUnload)

    // 清理函数
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [])

  // 检查是否有上次保存的页面路径，如果有则导航到该页面
  useEffect(() => {
    // 检查是否是首次加载
    const firstLoad = sessionStorage.getItem('layoutLoaded') !== 'true'
    if (!firstLoad) {
      // 如果不是首次加载，不执行导航，只标记已加载
      return
    }

    // 检查是否已经导航过，避免重复处理
    const hasNavigated = sessionStorage.getItem('hasNavigated') === 'true'
    if (hasNavigated) {
      return
    }

    // 标记Layout已经加载过，避免重复处理
    sessionStorage.setItem('layoutLoaded', 'true')

    // 获取上次页面路径和当前路径
    const lastPage = localStorage.getItem('lastPage')
    const currentPath = window.location.pathname

    // 如果当前路径是根路径'/'，说明是从登录页直接导航过来的，不需要重定向
    if (currentPath === '/') {
      sessionStorage.setItem('hasNavigated', 'true')
      return
    }
  }, [navigate])

  // 添加清除认证状态的处理函数
  const handleClearAuth = () => {
    // 清除所有存储位置的认证信息
    localStorage.removeItem('token')
    // 清除保存的菜单数据
    localStorage.removeItem('userMenu')
    // 导航到登录页面
    navigate('/login')
  }

  return (
    <Layout className='layout-div'>
      <Sider collapsible collapsed={collapsed} onCollapse={setCollapsed}>
        <div className='logo-container'>
          <img src='/qsq.png' alt='Logo' className='logo' />
          {!collapsed && <h1 className='platform-name'>生物驱离系统</h1>}
        </div>
        <Menu
          theme='dark'
          selectedKeys={[selectedMenuItemKey]}
          mode='inline'
          items={apiMenuItems}
          onClick={handleMenuClick}
          openKeys={openKeys}
          onOpenChange={onOpenChange}
          className='custom-menu'
        />
      </Sider>
      <Layout>
        <Header className='header-container'>
          <div className='layout-header-left'>
            {/* <Button
              type='text'
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
            /> */}
            {/* <Button
              type='text'
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            /> */}
            <Breadcrumb
              items={BreadCrumb[location.pathname]}
            />

            {/* 开发环境工具按钮 */}
            {import.meta.env.DEV && (
              <Tooltip title="清除认证并返回登录页面（仅开发环境）">
                <Button
                  type="text"
                  danger
                  icon={<BugOutlined />}
                  onClick={handleClearAuth}
                >
                  开发：退出
                </Button>
              </Tooltip>
            )}
          </div>
          <div className='header-right'>
            {/* <div className="search-container">
              <Search
                placeholder="搜索..."
                style={{ width: 200 }}
                className="header-search"
              />
            </div> */}
            <Space size='middle'>
              {/* <Tooltip title="消息通知">
                <Badge count={5}>
                  <Button
                    type="text"
                    icon={<AlertOutlined />}
                    onClick={() => setNotificationDrawerVisible(true)}
                  />
                </Badge>
              </Tooltip> */}
              {/* <Tooltip title="在线交流">
                <Button type="text" icon={<WechatOutlined />} />
              </Tooltip> */}
              {/* <Tooltip title="云盘">
                <Button type="text" icon={<CloudOutlined />} />
              </Tooltip> */}
              {/* <Tooltip title={isFullscreen ? '退出全屏' : '全屏'}>
                <Button
                  type='text'
                  icon={
                    isFullscreen ? (
                      <FullscreenExitOutlined />
                    ) : (
                      <FullscreenOutlined />
                    )
                  }
                  onClick={toggleFullscreen}
                />
              </Tooltip> */}
              <Dropdown menu={userMenuItems} trigger={['click']}>
                <Space className='user-dropdown'>
                  <Avatar
                    // icon={<UserOutlined />}
                    src={userAvatar || '/public/profile.jpg'}
                    alt='用户头像'
                  />
                  <span>{username}</span>
                  <DownOutlined />
                </Space>
              </Dropdown>
            </Space>
          </div>
        </Header>
        <Content className='detail-content'>
          <Routes>
            <Route path='/' element={<Dashboard />} />
            <Route path='/device/groups' element={<DeviceGroups />} />
            <Route path='/device/distribution' element={<DeviceDistribution />} />
            <Route path='/device/repellent' element={<DeviceRepellent />} />
            <Route path='/device/camera' element={<DeviceCamera />} />
            <Route path='/device/monitor' element={<DeviceMonitor />} />
            <Route path='/tenant/list' element={<TenantList />} />
            <Route path='/tenant/org' element={<TenantOrg />} />
            <Route path='/sensing/record' element={<SensingRecord />} />
            <Route path='/sensing/schedule' element={<SensingSchedule />} />
            <Route path='/sensing/event' element={<SensingEvent />} />
            <Route path='/strategy/scene' element={<StrategyScene />} />
            <Route path='/strategy/config' element={<StrategyConfig />} />
            <Route path='/statistics/device' element={<DeviceStatistics />} />
            <Route path='/statistics/mouse' element={<MouseActivity />} />
            <Route path='/statistics/effect' element={<RepellentEffect />} />
            <Route path='/maintenance/packages' element={<UpgradePackages />} />
            <Route path='/maintenance/audio' element={<MaintenanceAudio />} />
            <Route path='/maintenance/upgrade' element={<MaintenanceUpgrade />}/>
            <Route
              path='/maintenance/monitor'
              element={<MaintenanceMonitor />}
            />
            <Route path='/alerts/info' element={<AlertInfo />} />
            <Route path='/alerts/notice' element={<AlertsNotice />} />
            <Route path='/security/audit' element={<SecurityAudit />} />
            <Route path='/system/users' element={<UserManagement />} />
            <Route path='/system/tenant' element={<TenantList />} />
            <Route path='/system/roles' element={<RolePermission />} />
            <Route path='/system/logs' element={<LogManagement />} />
            <Route path='/system/dict' element={<DictionaryConfig />} />
            <Route path='/system/files' element={<FileManagement />} />
            <Route path='/system/config' element={<SystemConfig />} />
            <Route path='/system/organization' element={<Organization />} />
          </Routes>
        </Content>
      </Layout>

      {/* Notification Drawer */}
      <NotificationDrawer
        open={notificationDrawerVisible}
        onClose={() => setNotificationDrawerVisible(false)}
      />

      {/* User Settings Modal */}
      <UserSettingsModal
        open={userSettingsVisible}
        onClose={() => {
          setUserSettingsVisible(false)
          // 弹窗关闭后，刷新用户信息
          const fetchUserInfo = async () => {
            try {
              const response = await usrManagementApi.getLoginUserInfo()
              if (response.code === 200 && response.data) {
                if (response.data.user && response.data.user.username) {
                  setUsername(response.data.user.username)
                  if (
                    response.data.userProfile &&
                    response.data.userProfile.avatar
                  ) {
                    // 使用avatar ID获取预签名URL
                    const presignedResponse =
                      await layoutApi.getFilePresignedUrl(
                        response.data.userProfile.avatar
                      )
                    const avatarUrl = presignedResponse.url
                    setUserAvatar(avatarUrl)
                  } else {
                    setUserAvatar(null)
                  }
                }
              }
            } catch (error) {
              console.error('获取用户信息失败:', error)
            }
          }
          fetchUserInfo()
        }}
      />
    </Layout>
  )
}

export default MainLayout
