import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Table,
  Breadcrumb,
  Input,
  Select,
  Modal,
  Form,
  Radio,
  Tooltip,
  Statistic,
  message,
  InputNumber,
  TreeSelect,
  Tree,
} from "antd";
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  UserOutlined,
  TeamOutlined,
  LockOutlined,
  UnlockOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined,
  CaretDownOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { organizationApi } from "../../service/organization";
import "./styles.css";
import { Label } from "recharts";

const { Search } = Input;
const { Option } = Select;

// 模拟数据
const mockStatisticsData = {};

// 模拟用户数据
const mockTenants = [];

// 创建一个自定义的Label组件
const CustomLabel = ({ children }) => (
  <span className="filter-label">{children}</span>
);

const Organization = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [users, setUsers] = useState(mockTenants);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageNum: 1,
    pageSize: 10,
    total: mockTenants.length,
  });
  const [filters, setFilters] = useState({
    status: "all",
    accountType: "all",
    tenantId: "all",
    content: "",
    tenantCode: "",
    tenantType: "all",
  });
  const [resetPasswordModalVisible, setResetPasswordModalVisible] =
    useState(false);
  const [resetPasswordForm] = Form.useForm();
  const [selectedUser, setSelectedUser] = useState(null);
  const [statisticsData, setStatisticsData] = useState(mockStatisticsData);
  const [parentTenants, setParentTenants] = useState([]);
  const [tenantOptions, setTenantOptions] = useState([]);
  const [orgTreeData, setOrgTreeData] = useState([]);
  const [userOptions, setUserOptions] = useState([]);
  const [selectedOrgKey, setSelectedOrgKey] = useState(null);

  // 获取租户下拉列表数据
  const fetchTenantOptions = async () => {
    try {
      const response = await organizationApi.getTenantDropdown();
      if (response.code === 200) {
        const options = response.data.map((item) => ({
          label: item.name,
          value: item.id,
        }));
        setTenantOptions(options);
      }
    } catch (error) {
      console.error("获取租户列表失败:", error);
    }
  };

  // 获取组织树数据
  const fetchOrgTree = async () => {
    try {
      const response = await organizationApi.getOrgTree();
      if (response.code === 200) {
        const transformTreeData = (data) => {
          if (!Array.isArray(data)) return [];
          return data.map((item) => ({
            ...item,
            title: item.name,
            key: item.id,
            value: item.id,
            children: item.childrenList
              ? transformTreeData(item.childrenList)
              : [],
          }));
        };
        setOrgTreeData(transformTreeData(response.data || []));
      }
    } catch (error) {
      console.error("获取组织树失败:", error);
      setOrgTreeData([]);
    }
  };

  // 获取用户下拉列表数据
  const fetchUserOptions = async () => {
    try {
      const response = await organizationApi.getUserDropdown();
      if (response.code === 200) {
        const options = response.data.map((item) => ({
          label: item.name,
          value: item.id,
        }));
        setUserOptions(options);
      }
    } catch (error) {
      console.error("获取用户列表失败:", error);
    }
  };

  // 获取统计数据
  const fetchStatistics = async () => {
    // 直接使用模拟数据
    setStatisticsData(mockStatisticsData);
  };

  // 获取用户列表数据
  const fetchUserList = async (params = {}) => {
    setLoading(true);
    try {
      const response = await organizationApi.getOrgPage({
        pageNum: params.pageNum || 1,
        pageSize: params.pageSize || 10,
        tenantId: params.tenantId || "",
        name: params.name || params.tenantName || "",
        code: params.code || params.tenantCode || "",
        type: params.type || params.tenantType || "",
        status: params.status || "",
      });

      if (response.code === 200) {
        // 确保 response.data 是一个数组
        const orgList = Array.isArray(response.data)
          ? response.data
          : response.data?.list || response.data?.content || [];

        setUsers(orgList);
        // 确保current和pageNum相同
        const currentPage = params.pageNum || 1;

        // 获取总数据量，考虑到不同的API返回格式
        const totalItems =
          response.data?.total ||
          response.data?.totalElements ||
          response.total ||
          response.data?.totalCount ||
          orgList.length;
        setPagination({
          ...pagination,
          total: totalItems,
          current: currentPage,
          pageNum: currentPage,
          pageSize: params.pageSize || 10,
        });
      }
      else {
        setUsers([]); // 如果请求失败，设置为空数组
      }
    } catch (error) {
      console.error("获取组织列表失败:", error);
      setUsers([]); // 发生错误时，设置为空数组
    } finally {
      setLoading(false);
    }
  };

  // 获取上级租户下拉列表
  const fetchParentTenants = async () => {
    try {
      // 使用模拟数据
      const mockParentTenants = mockTenants.map((tenant) => ({
        id: tenant.id,
        name: tenant.tenantName,
      }));
      setParentTenants(mockParentTenants);
    } catch (error) {
      console.error("获取上级租户列表失败:", error);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    fetchUserList();
    fetchStatistics();
    fetchParentTenants();
    fetchTenantOptions();
    fetchUserOptions();
    fetchOrgTree();
  }, []);

  // 处理表格变化
  const handleTableChange = (newPagination, filters, sorter) => {
    fetchUserList({
      pageNum: newPagination.current,
      pageSize: newPagination.pageSize,
      ...filters,
    });
  };

  // Mock data for statistics
  const statistics = [
    {
      title: "总用户",
      value: statisticsData.totalNum,
      subTitle: "本月新增",
      subValue: statisticsData.monthAddNum,
      icon: <TeamOutlined style={{ fontSize: 24, color: "#1890ff" }} />,
    },
    {
      title: "活跃用户",
      value: statisticsData.activeNum,
      subTitle: "今日登录",
      subValue: statisticsData.todayLogInNum,
      icon: <UserOutlined style={{ fontSize: 24, color: "#52c41a" }} />,
    },
    {
      title: "异常账户",
      value: statisticsData.lockedNum,
      subTitle: "待验证",
      subValue: statisticsData.pendingNum,
      icon: <LockOutlined style={{ fontSize: 24, color: "#faad14" }} />,
    },
    {
      title: "管理员",
      value: statisticsData.adminNum,
      subTitle: "系统用户",
      subValue: statisticsData.systemNum,
      icon: <SettingOutlined style={{ fontSize: 24, color: "#722ed1" }} />,
    },
  ];

  // 处理禁用/启用状态切换
  const handleLockStatusChange = async (record) => {
    try {
      const response = await (record.status === "disabled"
        ? organizationApi.enableOrg([record.id])
        : organizationApi.disableOrg([record.id]));

      if (response.code === 200) {
        message.success(
          record.status === "disabled" ? "组织已启用" : "组织已禁用"
        );
        setSelectedRowKeys([]);
        // 刷新组织架构树
        fetchOrgTree();
        // 刷新列表
        fetchUserList({
          pageNum: pagination.current,
          pageSize: pagination.pageSize,
        });
      }else {
        message.error(
          response.message ||
          (record.status === "disabled" ? "启用组织失败" : "禁用组织失败")
        );
      }
    } catch (error) {
      console.error(
        record.status === "disabled" ? "启用组织失败:" : "禁用组织失败:",
        error
      );
      message.error(
        record.status === "disabled" ? "启用组织失败" : "禁用组织失败"
      );
    }
  };

  // 处理编辑按钮点击
  const handleEdit = async (record) => {
    if (record.status === "disabled") {
      message.warning("禁用状态的用户不能编辑");
      return;
    }
    try {
      await fetchOrgTree();
      const response = await organizationApi.getOrgDetail(record.id);
      if (response.code === 200) {
        const formData = {
          ...response.data,
          businessUnit: response.data.attributes?.businessUnit,
          location: response.data.attributes?.location,
          adminId: response.data.managerId,
        };
        editForm.setFieldsValue(formData);
        setEditModalVisible(true);
        setCurrentUserId(record.id);
      } 
    } catch (error) {
      console.error("获取租户详情失败:", error);
    }
  };

  // 处理编辑表单提交
  const handleEditUser = async (values) => {
    try {
      const params = {
        tenantId: values.tenantId,
        name: values.name,
        code: values.code,
        type: values.type,
        parentId: values.parentId,
        path: values.path,
        level: values.level,
        managerId: values.adminId,
        description: values.description,
        status: values.status,
        attributes: {
          location: values.location,
          businessUnit: values.businessUnit,
        },
        id: currentUserId,
      };

      const response = await organizationApi.updateOrg(params);

      if (response.code === 200) {
        message.success("组织信息更新成功！");
        setEditModalVisible(false);
        setCurrentUserId(null);
        editForm.resetFields();
        // 刷新组织架构树
        fetchOrgTree();
        // 刷新列表
        fetchUserList({
          pageNum: pagination.current,
          pageSize: pagination.pageSize,
        });
      }
    } catch (error) {
      console.error("更新组织信息失败:", error);
    }
  };
  // 处理删除组织
  const handleDeleteUser = async (userId) => {
    try {
      const response = await organizationApi.deleteOrg([userId]);
      if (response.code === 200) {
        message.success("组织删除成功");
        // 刷新组织架构树
        fetchOrgTree();
        // 刷新列表，传入当前分页信息
        fetchUserList({
          pageNum: pagination.current,
          pageSize: pagination.pageSize,
        });
      }
    } catch (error) {
      console.error("删除组织失败:", error);
    }
  };

  // Table columns
  const columns = [
    {
      title: "组织名称",
      dataIndex: "name",
      key: "name",
      align: "center",
      ellipsis: true,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "组织状态",
      dataIndex: "status",
      key: "status",
      align: "center",
      ellipsis: true,
      render: (text) => {
        const typeMap = {
          active: "活跃",
          disabled: "禁用",
          deleted: "删除",
        };
        return <span>{text ? typeMap[text] || text : "-"}</span>;
      },
    },
    {
      title: "组织类型",
      dataIndex: "type",
      key: "type",
      align: "center",
      ellipsis: true,
      render: (text) => {
        const typeMap = {
          department: "部门",
          company: "公司",
          team: "团队",
          projectGroup: "项目组",
        };
        return <span>{text ? typeMap[text] || text : "-"}</span>;
      },
    },
    {
      title: "组织路径",
      dataIndex: "path",
      key: "path",
      align: "center",
      ellipsis: true,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "组织层级",
      dataIndex: "level",
      key: "level",
      align: "center",
      ellipsis: true,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "组织描述",
      dataIndex: "description",
      key: "description",
      align: "center",
      ellipsis: true,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "操作",
      key: "action",
      align: "center",
      ellipsis: true,
      render: (_, record) => (
        <Space>
          <Tooltip
            title={record.status === "disabled" ? "禁用状态不能编辑" : "编辑"}
          >
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              disabled={record.status === "disabled"}
            />
          </Tooltip>
          {record.status === "disabled" ? (
            <Tooltip title="启用">
              <Button
                type="text"
                icon={<LockOutlined />}
                onClick={() => handleLockStatusChange(record)}
              />
            </Tooltip>
          ) : (
            <Tooltip title="禁用">
              <Button
                type="text"
                icon={<UnlockOutlined />}
                onClick={() => handleLockStatusChange(record)}
              />
            </Tooltip>
          )}
          <Tooltip
            title={record.status === "disabled" ? "禁用状态不能删除" : "删除"}
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              disabled={record.status === "disabled"}
              onClick={() => {
                Modal.confirm({
                  title: "删除确认",
                  content: `确定要删除组织 "${record.name}" 吗？`,
                  okText: "确定",
                  cancelText: "取消",
                  centered: true,
                  onOk: () => handleDeleteUser(record.id),
                });
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleCreateUser = async (values) => {
    setLoading(true);
    try {
      const params = {
        tenantId: values.tenantId,
        name: values.name,
        code: values.code,
        type: values.type,
        parentId: values.parentId,
        path: values.path,
        level: values.level,
        managerId: values.adminId,
        description: values.description,
        status: values.status,
        attributes: {
          location: values.location,
          attributes: values.businessUnit,
        },
      };

      const response = await organizationApi.addOrg(params);

      if (response.code === 200) {
        message.success("组织创建成功！");
        setCreateModalVisible(false);
        form.resetFields();
        // 刷新组织架构树
        fetchOrgTree();
        // 刷新列表
        fetchUserList({
          pageNum: pagination.current,
          pageSize: pagination.pageSize,
        });
      }
    } catch (error) {
      console.error("组织创建失败:", error);
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索按钮点击
  const handleSearch = () => {
    // 执行搜索
    setLoading(true);
    organizationApi
      .getOrgPage({
        pageNum: 1,
        pageSize: pagination.pageSize,
        tenantId: filters.tenantId === "all" ? "" : filters.tenantId,
        name: filters.content,
        code: filters.tenantCode,
        type: filters.tenantType === "all" ? "" : filters.tenantType,
        status: filters.status === "all" ? "" : filters.status,
      })
      .then((response) => {
        if (response.code === 200) {
          // 确保 response.data 是一个数组
          const orgList = Array.isArray(response.data)
            ? response.data
            : response.data?.list || response.data?.content || [];

          setUsers(orgList);

          // 获取总数据量，与fetchUserList函数保持一致
          const totalItems =
            response.data?.total ||
            response.data?.totalElements ||
            response.total ||
            response.data?.totalCount ||
            orgList.length;

          setPagination({
            ...pagination,
            total: totalItems,
            current: 1,
            pageNum: 1,
            pageSize: pagination.pageSize,
          });
        }
        else {
          setUsers([]); // 如果请求失败，设置为空数组
        }
      })
      .catch((error) => {
        console.error("获取组织列表失败:", error);
        setUsers([]); // 发生错误时，设置为空数组
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 处理树节点选择
  const handleTreeSelect = (selectedKeys, e) => {
    const selectedKey = selectedKeys.length > 0 ? selectedKeys[0] : null;
    setSelectedOrgKey(selectedKey);

    // 调用API获取数据
    setLoading(true);
    organizationApi
      .getOrgPage({
        pageNum: 1,
        pageSize: pagination.pageSize,
        tenantId: filters.tenantId === "all" ? "" : filters.tenantId,
        name: filters.content,
        code: e.node?.code || "",
        type: filters.tenantType === "all" ? "" : filters.tenantType,
        status: filters.status === "all" ? "" : filters.status,
      })
      .then((response) => {
        if (response.code === 200) {
          const orgList = Array.isArray(response.data)
            ? response.data
            : response.data?.list || response.data?.content || [];

          setUsers(orgList);
          const totalItems =
            response.data?.total ||
            response.data?.totalElements ||
            response.total ||
            response.data?.totalCount ||
            orgList.length;

          setPagination({
            ...pagination,
            total: totalItems,
            current: 1,
            pageNum: 1,
          });
        }
        else {
          setUsers([]);
        }
      })
      .catch((error) => {
        console.error("获取组织列表失败:", error);
        setUsers([]);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className="user-management">
      {/* Statistics Cards */}
      <Row
        gutter={[16, 16]}
        className="statistics-cards"
        style={{ display: "none" }}
      >
        {statistics.map((stat, index) => (
          <Col span={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
              />
              <div className="sub-statistic">
                <span>{stat.subTitle}: </span>
                <span className="sub-value">{stat.subValue}</span>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Main Content Area */}
      <Row gutter={16}>
        {/* Right Side: Filter Bar and User Table */}
        <Col span={24}>
          {/* Filter Bar */}
          <Card className="filter-bar">
            <Space wrap>
              <label>组织类型:</label>
              <Select
                placeholder="组织类型"
                style={{ width: 150 }}
                value={filters.tenantType}
                onChange={(value) =>
                  setFilters({ ...filters, tenantType: value })
                }
              >
                <Select.Option value="all">全部类型</Select.Option>
                <Select.Option value="department">部门</Select.Option>
                <Select.Option value="company">公司</Select.Option>
                <Select.Option value="team">团队</Select.Option>
                <Select.Option value="projectGroup">项目组</Select.Option>
              </Select>

              <label>组织状态:</label>
              <Select
                placeholder="组织状态"
                style={{ width: 150 }}
                value={filters.status}
                onChange={(value) => setFilters({ ...filters, status: value })}
              >
                <Select.Option value="all">全部状态</Select.Option>
                <Select.Option value="active">活跃</Select.Option>
                <Select.Option value="disabled">禁用</Select.Option>
              </Select>
              <label>租户:</label>
              <Select
                placeholder="选择租户"
                style={{ width: 200 }}
                value={filters.tenantId}
                onChange={(value) =>
                  setFilters({ ...filters, tenantId: value })
                }
              >
                <Select.Option value="all">全部租户</Select.Option>
                {tenantOptions.map((option) => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>

              <label>搜索:</label>
              <Input
                placeholder="组织名称"
                style={{ width: 150 }}
                value={filters.content}
                onChange={(e) =>
                  setFilters({ ...filters, content: e.target.value })
                }
              />
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
              >
                搜索
              </Button>
              <Button
                onClick={() => {
                  // 重置所有筛选条件
                  setFilters({
                    status: "all",
                    accountType: "all",
                    tenantId: "all",
                    content: "",
                    tenantCode: "",
                    tenantType: "all",
                  });
                  setSelectedOrgKey(null);
                  // 执行搜索操作 - 调用API获取数据
                  setLoading(true);
                  organizationApi
                    .getOrgPage({
                      pageNum: 1,
                      pageSize: pagination.pageSize,
                      tenantId: "",
                      name: "",
                      code: "",
                      type: "",
                      status: "",
                    })
                    .then((response) => {
                      if (response.code === 200) {
                        // 确保 response.data 是一个数组
                        const orgList = Array.isArray(response.data)
                          ? response.data
                          : response.data?.list || response.data?.content || [];

                        setUsers(orgList);

                        // 获取总数据量，与fetchUserList函数保持一致
                        const totalItems =
                          response.data?.total ||
                          response.data?.totalElements ||
                          response.total ||
                          response.data?.totalCount ||
                          orgList.length;

                        setPagination({
                          ...pagination,
                          total: totalItems,
                          current: 1,
                          pageNum: 1,
                          pageSize: pagination.pageSize,
                        });
                      }
                      else {
                        setUsers([]); // 如果请求失败，设置为空数组
                      }
                    })
                    .catch((error) => {
                      console.error("获取组织列表失败:", error);
                      setUsers([]); // 发生错误时，设置为空数组
                    })
                    .finally(() => {
                      setLoading(false);
                    });
                }}
              >
                重置
              </Button>
              <Button
                icon={<LockOutlined />}
                onClick={() => {
                  if (selectedRowKeys.length === 0) {
                    message.warning("请选择要禁用的组织");
                    return;
                  }
                  // 调用API批量禁用组织
                  organizationApi
                    .disableOrg(selectedRowKeys)
                    .then((response) => {
                      if (response.code === 200) {
                        message.success("组织禁用成功");
                        // 刷新组织架构树
                        fetchOrgTree();
                        // 刷新数据
                        fetchUserList({
                          pageNum: pagination.current,
                          pageSize: pagination.pageSize,
                          orgId: selectedOrgKey,
                        });
                        // 清空选择
                        setSelectedRowKeys([]);
                      }
                    })
                    .catch((error) => {
                      console.error("禁用组织失败:", error);
                    });
                }}
              >
                禁用
              </Button>
              <Button
                icon={<UnlockOutlined />}
                onClick={() => {
                  if (selectedRowKeys.length === 0) {
                    message.warning("请选择要启用的组织");
                    return;
                  }
                  // 调用API批量启用组织
                  organizationApi
                    .enableOrg(selectedRowKeys)
                    .then((response) => {
                      if (response.code === 200) {
                        message.success("组织启用成功");
                        // 刷新组织架构树
                        fetchOrgTree();
                        // 刷新数据
                        fetchUserList({
                          pageNum: pagination.current,
                          pageSize: pagination.pageSize,
                          orgId: selectedOrgKey,
                        });
                        // 清空选择
                        setSelectedRowKeys([]);
                      }
                    })
                    .catch((error) => {
                      console.error("启用组织失败:", error);
                    });
                }}
              >
                启用
              </Button>
              <Button
                danger
                icon={<DeleteOutlined />}
                disabled={selectedRowKeys.length === 0}
                onClick={() => {
                  // 检查选中的组织中是否有禁用状态的组织
                  const disabledOrgs = users.filter(
                    org => selectedRowKeys.includes(org.id) && org.status === "disabled"
                  );

                  if (disabledOrgs.length > 0) {
                    message.warning("禁用状态的组织不能被删除");
                    return;
                  }

                  Modal.confirm({
                    title: "删除确认",
                    content: `确定要删除选中的 ${selectedRowKeys.length} 个组织吗？`,
                    okText: "确定",
                    cancelText: "取消",
                    centered: true,
                    onOk: () => {
                      setLoading(true);
                      organizationApi
                        .deleteOrg(selectedRowKeys)
                        .then((response) => {
                          if (response.code === 200) {
                            message.success("组织删除成功");
                            // 刷新组织架构树
                            fetchOrgTree();
                            // 刷新数据
                            fetchUserList({
                              pageNum: 1,
                              pageSize: pagination.pageSize,
                              orgId: selectedOrgKey,
                            });
                            // 清空选择
                            setSelectedRowKeys([]);
                          }
                        })
                        .finally(() => {
                          setLoading(false);
                        });
                    },
                  });
                }}
              >
                批量删除
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  fetchOrgTree();
                  setCreateModalVisible(true);
                }}
              >
                添加组织
              </Button>
            </Space>
          </Card>

          <Row gutter={16}>
            {/* Left Side: Organization Tree */}
            <Col className="org-tree-container">
              <Card
                title="组织结构"
                className="org-tree-card"
              >
                <Tree
                  showLine={false}
                  defaultExpandAll={false}
                  treeData={orgTreeData}
                  onSelect={handleTreeSelect}
                  switcherIcon={<CaretDownOutlined />}
                  selectedKeys={selectedOrgKey ? [selectedOrgKey] : []}
                  scroll={{ y: orgTreeData.length > 30 ? 1000 : undefined   }}
                />
              </Card>
            </Col>

            {/* Right Side: User Table */}
            <Col className="table-container">
              <Card className="user-table">
                <Table
                  columns={columns}
                  dataSource={users || []}
                  rowKey="id"
                  rowSelection={{
                    selectedRowKeys,
                    onChange: setSelectedRowKeys,
                  }}
                  loading={loading}
                  pagination={{
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                    total: pagination.total,
                    showQuickJumper: true,
                    showSizeChanger: true,
                    showTotal: (total) => `共 ${total} 条记录`,
                  }}
                  onChange={handleTableChange}
                  columnTitle={{ style: { textAlign: "center" } }}
                  scroll={users.length > 10 ? { y: 650 } : undefined}
                />
              </Card>
            </Col>
          </Row>
        </Col>
      </Row>

      {/* Create User Modal */}
      <Modal
        title="添加组织"
        open={createModalVisible}
        onOk={() => form.submit()}
        centered
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateUser}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="tenantId"
                label="租户"
                rules={[{ required: true, message: "请选择租户" }]}
              >
                <Select placeholder="请选择租户" options={tenantOptions} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="组织名称"
                rules={[{ required: true, message: "请输入组织名称" }]}
              >
                <Input placeholder="请输入组织名称" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="组织编码"
                rules={[{ required: true, message: "请输入组织编码" }]}
              >
                <Input placeholder="请输入组织编码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="组织类型"
                rules={[{ required: true, message: "请选择组织类型" }]}
              >
                <Select placeholder="请选择组织类型">
                  <Select.Option value="department">部门</Select.Option>
                  <Select.Option value="company">公司</Select.Option>
                  <Select.Option value="team">团队</Select.Option>
                  <Select.Option value="projectGroup">项目组</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="组织状态"
                initialValue="active"
                rules={[{ required: true, message: "请选择组织状态" }]}
              >
                <Radio.Group>
                  <Radio value="active">启用</Radio>
                  <Radio value="disabled">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="parentId" label="上级组织">
                <TreeSelect
                  treeData={orgTreeData}
                  placeholder="请选择上级组织"
                  treeDefaultExpandAll
                  allowClear
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="path"
                label="组织路径"
                rules={[{ required: true, message: "请输入组织路径" }]}
              >
                <Input placeholder="组织路径" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="level"
                label="组织层级"
                rules={[{ required: true, message: "请输入组织层级" }]}
              >
                <InputNumber
                  min={1}
                  precision={0}
                  style={{ width: "100%" }}
                  placeholder="组织层级"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="adminId" label="管理员">
                <Select
                  allowClear
                  placeholder="请选择管理员"
                  options={userOptions}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="businessUnit" label="业务单元">
                <Input placeholder="请输入业务单元" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="location" label="位置信息">
                <Input placeholder="请输入位置信息" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item name="description" label="组织描述">
                <Input.TextArea rows={4} placeholder="请输入组织描述" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 编辑租户模态框 */}
      <Modal
        title="编辑组织"
        open={editModalVisible}
        onOk={() => editForm.submit()}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
        }}
        width={800}
        centered
      >
        <Form form={editForm} layout="vertical" onFinish={handleEditUser}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="tenantId"
                label="租户"
                rules={[{ required: true, message: "请选择租户" }]}
              >
                <Select placeholder="请选择租户" options={tenantOptions} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="组织名称"
                rules={[{ required: true, message: "请输入组织名称" }]}
              >
                <Input placeholder="请输入组织名称" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="组织编码"
                rules={[{ required: true, message: "请输入组织编码" }]}
              >
                <Input placeholder="请输入组织编码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="组织类型"
                rules={[{ required: true, message: "请选择组织类型" }]}
              >
                <Select placeholder="请选择组织类型">
                  <Select.Option value="department">部门</Select.Option>
                  <Select.Option value="company">公司</Select.Option>
                  <Select.Option value="team">团队</Select.Option>
                  <Select.Option value="projectGroup">项目组</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="组织状态"
                rules={[{ required: true, message: "请选择组织状态" }]}
              >
                <Radio.Group>
                  <Radio value="active">启用</Radio>
                  <Radio value="disabled">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="parentId" label="上级组织">
                <TreeSelect
                  treeData={orgTreeData}
                  placeholder="请选择上级组织"
                  treeDefaultExpandAll
                  allowClear
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="path"
                label="组织路径"
                rules={[{ required: true, message: "请输入组织路径" }]}
              >
                <Input placeholder="组织路径" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="level"
                label="组织层级"
                rules={[{ required: true, message: "请输入组织层级" }]}
              >
                <InputNumber
                  min={1}
                  precision={0}
                  style={{ width: "100%" }}
                  placeholder="组织层级"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="adminId" label="管理员">
                <Select
                  allowClear
                  placeholder="请选择管理员"
                  options={userOptions}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="businessUnit" label="业务单元">
                <Input placeholder="请输入业务单元" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="location" label="位置信息">
                <Input placeholder="请输入位置信息" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item name="description" label="组织描述">
                <Input.TextArea rows={4} placeholder="请输入组织描述" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default Organization;
