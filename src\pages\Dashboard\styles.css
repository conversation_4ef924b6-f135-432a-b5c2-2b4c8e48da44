.dashboard-container {
  width: 100%;
  /* min-height: 100vh; */
  background: #f0f2f5;
  color: rgba(0, 0, 0, 0.88);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}

.dashboard-header {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  border-radius: 8px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dashboard-logo {
  height: 32px;
}

.header-info h1 {
  margin: 0;
  color: rgba(0, 0, 0, 0.88);
  font-size: 18px;
}

.header-info span {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.dashboard-content {
  flex: 1;
  padding: 16px 0px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.main-layout {
  flex: 1;
  display: flex;
  gap: 16px;
  height: 100%;
  border-radius: 8px;
}

.left-panel,
.right-panel {
  height: 100%;
  width: 320px;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
}

.panel-card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.panel-card .ant-card-body {
  flex: 1;
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.panel-tabs .ant-tabs-content {
  flex: 1;
  padding: 16px;
  height: 100%;
}

.right-panel .ant-spin-nested-loading{
  height: 56vh;
  overflow-y: auto;
  padding-right: 10px;
}

.panel-tabs .ant-tabs-content .ant-tabs-tabpane{
  height: 100%;
}

.panel-tabs .ant-tabs-nav {
  margin: 0;
  padding: 8px 8px 0;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 8px;
}

.panel-tabs .ant-tabs-tab {
  padding: 8px 16px;
}

.panel-tabs .ant-tabs-tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-tabs .ant-tabs-content-holder {
  overflow: auto;
}

.center-panel {
  flex: 1;
  /* height: 650px; */
  height: 100%;
  background: #fff;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  border-radius: 8px;
}

.bottom-panel {
  min-height: 200px;
  /* background: #fff; */
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  margin-bottom: 16px;
}

/* Map Controls Styles */
.map-controls {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.map-controls .ant-radio-button-wrapper {
  background: #fff;
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.65);
}

.map-controls .ant-radio-button-wrapper:hover {
  color: #1890ff;
}

.map-controls .ant-radio-button-wrapper-checked {
  background: #1890ff !important;
  border-color: #1890ff !important;
  color: #fff !important;
}

.map-controls .ant-slider {
  margin: 8px 0;
}

/* Card Styles */
.dashboard-card {
  background: #fff;
  /* border-radius: 4px; */
  /* height: 650px; */
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  height: 100%;
}

.dashboard-card-1 .ant-statistic-title,.dashboard-card-2 .ant-statistic-title,.dashboard-card-4 .ant-statistic-title{
  color: #414D55;
  font-weight: 900;
  font-size: 16px;
  margin-bottom: 10px;
}


.dashboard-card-1 .ant-statistic-content{
  display: flex;
  align-items: center;
}

.dashboard-card .ant-card-head {
  color: rgba(0, 0, 0, 0.88);
  border-bottom: 1px solid #f0f0f0;
}

.dashboard-card .ant-card-body {
  color: rgba(0, 0, 0, 0.65);
}

.dashboard-card-1{
  background: url("/card_bg_1.png") no-repeat;
  background-size: 100% 100%;
  height: 158px; 
  border-radius: 16px;
  cursor: pointer;
}

.dashboard-card-2{
  background: url("/card_bg_2.png") no-repeat;
  background-size: 100% 100%;
  height: 158px; 
  border-radius: 16px;
  cursor: pointer;
}

.dashboard-card-4{
  background: url("/card_bg_3.png") no-repeat;
  background-size: 100% 100%;
  height: 158px; 
  border-radius: 16px;
  cursor: pointer;
}

.dashboard-card-2 .ant-statistic-content .ant-statistic-content-prefix,.dashboard-card-4 .ant-statistic-content .ant-statistic-content-prefix{
  margin-right: 15px;
}

.dashboard-card-3 .ant-card-head-title{
  font-weight: 400;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  
}

/* Statistics Styles */
.stat-card {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.stat-card .ant-statistic-title {
  color: rgba(0, 0, 0, 0.45);
}

.stat-card .ant-statistic-content {
  color: rgba(0, 0, 0, 0.88);
}

/* Chart Styles */
.chart-container {
  width: 100%;
  height: 100%;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .left-panel,
  .right-panel {
    width: 280px;
  }
}

@media (max-width: 992px) {
  .main-layout {
    flex-direction: column;
  }

  .left-panel,
  .right-panel {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-controls {
    width: 100%;
    overflow-x: auto;
  }
}