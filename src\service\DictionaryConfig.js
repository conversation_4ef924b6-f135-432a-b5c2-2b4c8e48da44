// API基础URL - 可以根据实际部署环境手动配置

import { getLoginEntry } from '../utils/loginLogger';
import { API_PREFIX } from "./constant";
import { message } from 'antd';

const handleApiError = (response) => {
  return response.json().then(data => {
    if (response.status === 401 ) {
      message.error(data?.message);
      setTimeout(() => {
        window.location.href = '/login'
      }, 500);
    } else if (response.status === 400 || response.status === 404 || response.status === 500) {
      message.error(data?.message);
    } else if (response.status === 403) {
      // Handle forbidden
    }
    return Promise.reject(data);
  });
};
// 检查响应数据是否包含错误码
const checkResponseData = (data) => {
  if (data && data.code && data.code !== 200) {
    message.error(data.message || '操作失败');
    return Promise.reject(data);
  }
  return data;
};

export const dictionaryConfigApi = {
  // 获取字典类型树
  getDictTypeTree: (params = {}) => {
    const queryString = params.content
      ? `?content=${encodeURIComponent(params.content)}`
      : "";
    return fetch(`${API_PREFIX["user-service"]}/dict/type/tree${queryString}`, {
      headers: {
        'Authorization': getLoginEntry()
      },
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        // 检查响应数据的结构，如果数据嵌套在某个字段中则提取出来
        if (data && data.data) {
          return data.data;
        } else if (data && data.result) {
          return data.result;
        } else if (data && data.records) {
          return data.records;
        } else if (Array.isArray(data)) {
          return data;
        } else if (typeof data === "object") {
          // 如果是其他格式的对象，尝试转换为树结构
          return transformToTree(data);
        }
        return [];
      });
  },

  // 根据字典类型ID获取非删除字典项列表
  getDictItemByTypeCode: (typeCode) => {
    return fetch(
      `${API_PREFIX["user-service"]}/dict/item/queryByTypeCodeNoDelete?typeCode=${typeCode}`,
      {
        headers: {
          'Authorization': getLoginEntry()
        },
      }
    )
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        // 检查响应数据的结构，如果数据嵌套在某个字段中则提取出来
        if (data && data.data) {
          return data.data;
        } else if (data && data.result) {
          return data.result;
        } else if (data && data.records) {
          return data.records;
        } else if (Array.isArray(data)) {
          return data;
        }
        return [];
      });
  },
    // 根据字典类型ID获取字典项列表
    getDictItemByType: (typeCode) => {
      return fetch(
        `${API_PREFIX["user-service"]}/dict/item/queryByTypeCode?typeCode=${typeCode}`,
        {
          headers: {
            'Authorization': getLoginEntry()
          },
        }
      )
        .then((response) => {
          if (!response.ok) {
            return handleApiError(response);
          }
          return response.json().then(checkResponseData);
        })
        .then((data) => {
          // 检查响应数据的结构，如果数据嵌套在某个字段中则提取出来
          if (data && data.data) {
            return data.data;
          } else if (data && data.result) {
            return data.result;
          } else if (data && data.records) {
            return data.records;
          } else if (Array.isArray(data)) {
            return data;
          }
          return [];
        });
    },

  // 根据ID获取字典类型详情
  getDictTypeById: (id) => {
    return fetch(`${API_PREFIX["user-service"]}/dict/type/${id}`, {
      headers: {
        'Authorization': getLoginEntry()
      },
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        if (data && data.data) {
          return data.data;
        } else if (data && data.result) {
          return data.result;
        } else if (typeof data === "object") {
          return data;
        }
        return null;
      });
  },

  // 添加字典项
  addDictItem: (dictItemData) => {
    return fetch(`${API_PREFIX["user-service"]}/dict/item/add`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify(dictItemData),
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        return data;
      });
  },

  // 添加字典类型
  addDictType: (dictTypeData) => {
    return fetch(`${API_PREFIX["user-service"]}/dict/type/add`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify(dictTypeData),
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        return data;
      });
  },

  // 根据ID获取字典项详情
  getDictItemById: (id) => {
    return fetch(`${API_PREFIX["user-service"]}/dict/item/${id}`, {
      headers: {
        'Authorization': getLoginEntry()
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`网络响应不正常: ${response.status}`);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        if (data && data.data) {
          return data.data;
        } else if (data && data.result) {
          return data.result;
        } else if (typeof data === "object") {
          return data;
        }
        return null;
      })
  },

  // 更新字典项
  updateDictItem: (dictItemData) => {
    return fetch(`${API_PREFIX["user-service"]}/dict/item/update`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify(dictItemData),
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        return data;
      });
  },

  // 更新字典类型
  updateDictType: (dictTypeData) => {
    return fetch(`${API_PREFIX["user-service"]}/dict/type/update`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify(dictTypeData),
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        return data;
      });
  },

  // 禁用字典项
  disableDictItem: (ids) => {
    return fetch(`${API_PREFIX["user-service"]}/dict/item/disable`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify({ ids }),
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        return data;
      });
  },

  // 启用字典项
  enableDictItem: (ids) => {
    return fetch(`${API_PREFIX["user-service"]}/dict/item/enable`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify({ ids }),
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        return data;
      });
  },

  // 删除字典项
  deleteDictItem: (ids) => {
    return fetch(`${API_PREFIX["user-service"]}/dict/item/delete`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify({ ids }),
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        return data;
      });
  },

  // 启用字典类型
  enableDictType: (ids) => {
    return fetch(`${API_PREFIX["user-service"]}/dict/type/enable`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify({ ids }),
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        return data;
      });
  },

  // 禁用字典类型
  disableDictType: (ids) => {
    return fetch(`${API_PREFIX["user-service"]}/dict/type/disable`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify({ ids }),
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        return data;
      });
  },

  // 删除字典类型
  deleteDictType: (ids) => {
    return fetch(`${API_PREFIX["user-service"]}/dict/type/delete`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify({ ids }),
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        return data;
      });
  },

  // 导出字典项
  exportDictItem: () => {
    return fetch(`${API_PREFIX["user-service"]}/dict/item/export`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        'Authorization': getLoginEntry()
      },
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }

        // 直接返回blob，不尝试解析为JSON
        return response.blob();
      })
      .then((data) => {

        // 创建下载链接
        const url = window.URL.createObjectURL(data);
        const a = document.createElement("a");
        a.href = url;
        a.download = "字典项数据.xlsx"; // 文件名可能需要从响应头获取
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        return { success: true };
      });
  },

  // 导出字典项导入模板
  exportDictItemTemplate: () => {
    return fetch(`${API_PREFIX["user-service"]}/dict/item/export/template`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        'Authorization': getLoginEntry()
      },
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.blob();
      })
      .then((data) => {
        // 创建下载链接
        const url = window.URL.createObjectURL(data);
        const a = document.createElement("a");
        a.href = url;
        a.download = "字典项导入模板.xlsx";
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        return { success: true };
      });
  },

  // 导入字典项
  importDictItem: (file) => {
    const formData = new FormData();
    formData.append("file", file);

    return fetch(`${API_PREFIX["user-service"]}/dict/item/import`, {
      method: "POST",
      headers: {
        'Authorization': getLoginEntry()
      },
      body: formData,
    })
      .then((response) => {
        if (!response.ok) {
          return handleApiError(response);
        }
        return response.json().then(checkResponseData);
      })
      .then((data) => {
        return data;
      });
  },
};

// 帮助函数：将各种响应格式转换为树结构
function transformToTree(data) {
  // 如果数据已经是数组，直接返回
  if (Array.isArray(data)) {
    return data;
  }

  // 尝试将对象的值转换为数组
  if (typeof data === "object") {
    const possibleArrays = Object.values(data).filter((value) =>
      Array.isArray(value)
    );
    if (possibleArrays.length > 0) {
      return possibleArrays[0];
    }

    // 如果没有数组，尝试将对象转换为树节点数组
    return Object.entries(data).map(([key, value]) => {
      if (typeof value === "object") {
        return {
          typeName: key,
          scope: "系统级",
          children: transformToTree(value),
        };
      }
      return {
        typeName: key,
        scope: "系统级",
        value: value,
      };
    });
  }

  return [];
}
