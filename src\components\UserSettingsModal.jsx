import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, DatePicker, Upload, message, Row, Col, Button, Tree } from 'antd';
import { PlusOutlined, EditOutlined } from '@ant-design/icons';
import { usrManagementApi } from '../service/userManagement';
import dayjs from 'dayjs';

const UserSettingsModal = ({ open, onClose }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [roleOptions, setRoleOptions] = useState([]);
  const [userPermissions, setUserPermissions] = useState([]);
  const [permissionTree, setPermissionTree] = useState([]);
  const [roleName, setRoleName] = useState('');
  const [avatarUrl, setAvatarUrl] = useState('');
  const [avatarFileId, setAvatarFileId] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [userData, setUserData] = useState(null);
  const [languageOptions] = useState([
    { value: '简体中文', label: '简体中文' },
    { value: 'English (US)', label: 'English (US)' },
    { value: '日本語', label: '日本語' },
    { value: '한국어', label: '한국어' }
  ]);
  const [timezoneOptions] = useState([
    { value: '北京时间 (UTC+8)', label: '北京时间 (UTC+8)' },
    { value: '格林威治标准时间 (UTC+0)', label: '格林威治标准时间 (UTC+0)' },
    { value: '东部标准时间 (UTC-5)', label: '东部标准时间 (UTC-5)' },
    { value: '太平洋标准时间 (UTC-8)', label: '太平洋标准时间 (UTC-8)' },
    { value: '日本标准时间 (UTC+9)', label: '日本标准时间 (UTC+9)' },
    { value: '中欧时间 (UTC+1)', label: '中欧时间 (UTC+1)' }
  ]);
  
  useEffect(() => {
    if (open) {
      // 先清空表单和状态
      form.resetFields();
      setUserPermissions([]);
      setRoleName('');
      setAvatarUrl('');
      setAvatarFileId('');
      setUserData(null);
      setIsEditing(false);
      
      // 然后再获取用户信息
      fetchUserInfo();
      fetchRoleOptions();
    }
  }, [open]);

  useEffect(() => {
    if (roleOptions.length > 0 && form.getFieldValue('roleId')) {
      const role = roleOptions.find(role => role.value === form.getFieldValue('roleId'));
      if (role) {
        setRoleName(role.label);
      }
    }
  }, [roleOptions, form]);

  const fetchUserInfo = async () => {
    try {
      setLoading(true);
      const response = await usrManagementApi.getLoginUserInfo();
      if (response.code === 200) {
        const userData = response.data;
        setUserData(userData);
        
        // 获取用户权限
        const permissionResponse = await usrManagementApi.getPermissionTree({
          permissionId: "",
          roleId: userData.roles?.[0]?.id || ""
        });
        
        if (permissionResponse.code === 200) {
          const permissions = permissionResponse.data;
          const userPermissionNames = [];
          
          // 递归函数来获取权限名称
          const getPermissionNames = (items) => {
            items.forEach(item => {
              if (userData.permissions.includes(item.id)) {
                userPermissionNames.push(item.name);
              }
              if (item.childrenList) {
                getPermissionNames(item.childrenList);
              }
            });
          };
          
          // 转换为树形结构
          const transformToTreeData = (items) => {
            return items.map(item => ({
              title: item.name,
              key: item.id,
              children: item.childrenList ? transformToTreeData(item.childrenList) : null,
              disabled: true,
              checkable: true,
              checked: userData.permissions.includes(item.id)
            }));
          };
          
          getPermissionNames(permissions);
          setUserPermissions(userPermissionNames);
          setPermissionTree(transformToTreeData(permissions));
        }

        // 设置表单数据
        form.setFieldsValue({
          username: userData.user.username,
          phone: userData.user.phoneNumber,
          email: userData.user.email,
          status: userData.user.status,
          accountType: userData.user.accountType,
          roleId: userData.roles?.[0]?.id || '',
          language: userData.userProfile?.language || 'zh-CN',
          timezone: userData.userProfile?.timezone || 'UTC+8',
          createdTime: dayjs(userData.user.createdAt),
          profile: userData.userProfile?.bio,
          address: userData.userProfile?.address
        });
        
        // 设置头像URL（如果有）
        if (userData.userProfile?.avatar) {
          setAvatarFileId(userData.userProfile.avatar);
          
          // 使用预签名URL接口获取头像URL
          const presignedUrlResponse = await usrManagementApi.getPresignedUrl(userData.userProfile.avatar);
            const url = presignedUrlResponse.url;
            setAvatarUrl(url); // 直接使用完整的预签名URL
        }
        
        // 设置角色名称
        if (userData.roles && userData.roles.length > 0) {
          const response = await usrManagementApi.getRoleDropdown();
          const roleNum = response.data.length;
          for(let i = 0; i < roleNum; i++){
            if(userData.roles == response.data[i].id){
              setRoleName(response.data[i].name);
              return;
            }
          }
        }
      } else {
        // message.error(response.message || '获取用户信息失败');
      }
    } catch (error) {
      // console.error('获取用户信息失败:', error);
      // message.error('获取用户信息失败');
      setAvatarUrl('../../../public/profile.jpg');
    } finally {
      setLoading(false);
    }
  };

  const fetchRoleOptions = async (currentRoleId = null) => {
    try {
      const response = await usrManagementApi.getRoleDropdown();
      if (response.code === 200) {
        const options = response.data.map(role => ({
          label: role.name || role.roleName,
          value: role.id
        }));
        setRoleOptions(options);
        
        // 如果有当前角色ID，更新角色名称
        if (currentRoleId) {
          const currentRole = options.find(role => role.value === currentRoleId);
          if (currentRole) {
            setRoleName(currentRole.label);
          }
        }
      } else {
        message.error(response.message || '获取角色列表失败');
      }
    } catch (error) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleModalClose = () => {
    if (isEditing) {
      setIsEditing(false);
      fetchUserInfo();
    }
    onClose();
  };

  const handleSubmit = async () => {
    try {
      if (!isEditing) {
        handleModalClose();
        return;
      }

      const values = await form.validateFields();
      setLoading(true);

      // 准备提交给API的用户数据
      const updateData = {
        userId: userData?.user?.id || "",
        username: values.username,
        realName: userData?.userProfile?.realName || "",
        nickname: userData?.userProfile?.nickname || "",
        email: values.email,
        phoneNumber: values.phone,
        accountType: values.accountType,
        status: values.status,
        tenantId: userData?.tenantId || "",
        gender: userData?.userProfile?.gender || "",
        birthDate: userData?.userProfile?.birthDate || "",
        address: values.address || "",
        position: userData?.userProfile?.position || "",
        bio: values.profile,
        avatar: avatarFileId || "",
        timezone: values.timezone,
        language: values.language,
        orgUnitIds: userData?.orgUnits || [],
        roleIds: userData?.roles || []
      };

      const response = await usrManagementApi.updateUser(updateData);
      if (response.code === 200) {
        message.success('用户信息更新成功');
        setIsEditing(false);
        fetchUserInfo(); // 重新获取用户信息
        onClose(); // 关闭弹窗
      } else {
        message.error(response.message || '更新用户信息失败');
      }
    } catch (error) {
      console.error('更新用户信息失败:', error);
      message.error('更新用户信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarClick = () => {
    if (isEditing) {
      // Find the file input within the Upload component and trigger a click
      const fileInput = document.querySelector('.ant-upload input[type="file"]');
      if (fileInput) {
        fileInput.click();
      }
    }
  };

  return (
    <Modal
      title="用户设置"
      open={open}
      onCancel={handleModalClose}
      onOk={handleSubmit}
      width={720}
      confirmLoading={loading}
      footer={[
        <Button key="edit" type="primary" icon={<EditOutlined />} onClick={handleEdit} disabled={isEditing}>
          编辑
        </Button>,
        <Button key="cancel" onClick={handleModalClose}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          确定
        </Button>
      ]}
    >
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginBottom: '24px' }}>
        <div 
          style={{ position: 'relative', marginBottom: '8px', cursor: isEditing ? 'pointer' : 'default' }}
          onClick={handleAvatarClick}
        >
          {avatarUrl ? (
            <img 
              src={avatarUrl} 
              alt="avatar" 
              style={{ 
                width: '80px', 
                height: '80px', 
                borderRadius: '50%',
                backgroundColor: '#4169E1',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                color: 'white',
                fontSize: '32px'
              }} 
            />
          ) : (
            <div style={{ 
              width: '80px', 
              height: '80px', 
              borderRadius: '50%',
              backgroundColor: '#4169E1',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              color: 'white',
              fontSize: '32px'
            }}>
              {userData?.user?.username?.[0]?.toUpperCase()}
            </div>
          )}
          {isEditing && (
            <div 
              style={{ 
                position: 'absolute', 
                right: '0', 
                bottom: '0', 
                backgroundColor: '#1890ff', 
                borderRadius: '50%', 
                width: '24px', 
                height: '24px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                cursor: 'pointer'
              }}
            >
              <EditOutlined style={{ color: 'white', fontSize: '14px' }} />
            </div>
          )}
        </div>
        
        <Upload
          name="avatar"
          showUploadList={false}
          action=""
          customRequest={({ file, onSuccess, onError }) => {
            if (!isEditing) {
              return;
            }
            
            // 调用文件上传接口
            usrManagementApi.uploadFile(file)
              .then(response => {
                if (response.success) {
                  // 保存fileId用于提交
                  setAvatarFileId(response.fileId);
                  // 获取预签名URL来显示头像
                  usrManagementApi.getPresignedUrl(response.fileId)
                    .then(presignedResponse => {
                        setAvatarUrl(presignedResponse.url || '../../../public/profile.jpg');
                        onSuccess(response);
                        message.success('头像上传成功!');
                    });
                } else {
                  onError(new Error('上传失败'));
                  message.error('头像上传失败');
                  setAvatarUrl('../../../public/profile.jpg');
                }
              })
              .catch(error => {
                onError(error);
                message.error('头像上传失败');

              });
          }}
          beforeUpload={(file) => {
            const isImage = file.type.startsWith('image/');
            if (!isImage) {
              message.error('请上传图片文件!');
              return Upload.LIST_IGNORE;
            }
            return isEditing; // 只在编辑模式下允许上传
          }}
          onChange={(info) => {
            if (info.file.status === 'error') {
              message.error('头像设置失败');
            }
          }}
          disabled={!isEditing}
        >
          <Button icon={<PlusOutlined />} style={{ display: 'none' }}>Upload</Button>
        </Upload>
      </div>

      <Form form={form} layout="vertical">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="用户名称" name="username">
              <Input disabled={!isEditing} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="手机号" name="phone">
              <Input disabled={!isEditing} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="邮箱" name="email">
              <Input disabled={!isEditing} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="用户状态" name="status">
              <Select disabled>
                <Select.Option value="active">活跃</Select.Option>
                <Select.Option value="locked">锁定</Select.Option>
                <Select.Option value="disabled">禁用</Select.Option>
                <Select.Option value="pending">待验证</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="账户类型" name="accountType">
              <Select disabled>
                <Select.Option value="user">普通用户</Select.Option>
                <Select.Option value="admin">管理员</Select.Option>
                <Select.Option value="system">系统用户</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <div className="ant-form-item">
              <div className="ant-form-item-label">
                <label>用户角色</label>
              </div>
              <div className="ant-form-item-control">
                <Input
                  disabled
                  value={roleName}
                />
              </div>
            </div>
          </Col>
          {/* <Col span={12}>
            <Form.Item label="创建时间" name="createdTime">
              <DatePicker disabled showTime format="YYYY-MM-DD HH:mm:ss" />
            </Form.Item>
          </Col> */}

          <Col span={12}>
            <Form.Item label="语言偏好" name="language">
              <Select disabled={!isEditing} options={languageOptions} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="时区" name="timezone">
              <Select disabled={!isEditing} options={timezoneOptions} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="地址" name="address">
              <Input disabled={!isEditing} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="个人简介" name="profile">
              <Input disabled={!isEditing} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="用户权限">
              {permissionTree.length > 0 ? (
                <div style={{ maxHeight: '200px', overflow: 'auto', border: '1px solid #d9d9d9', borderRadius: '2px', padding: '4px' }}>
                  <Tree
                    checkable
                    disabled
                    treeData={permissionTree}
                    defaultExpandAll
                    checkedKeys={userData?.permissions || []}
                  />
                </div>
              ) : (
                <Input.TextArea
                  value={userPermissions.join(', ')}
                  disabled
                  autoSize={{ minRows: 2, maxRows: 6 }}
                />
              )}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default UserSettingsModal;
