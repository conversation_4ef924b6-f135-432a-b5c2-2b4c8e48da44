import React from 'react';
import { Card, Descriptions, Tag, List, Space } from 'antd';
import { RadarChart, Radar, PolarGrid, PolarAngleAxis, PolarRadiusAxis, ResponsiveContainer } from 'recharts';

const RiskAssessment = ({ group }) => {
  if (!group) return null;

  const { rodentRiskAssessment } = group;

  const radarData = [
    { subject: '历史活动', value: 80 },
    { subject: '环境风险', value: 65 },
    { subject: '防护覆盖', value: 90 },
    { subject: '结构隐患', value: 70 },
    { subject: '周边因素', value: 85 }
  ];

  const getRiskColor = (level) => {
    switch (level.toLowerCase()) {
      case '高':
        return 'red';
      case '中':
        return 'orange';
      case '低':
        return 'green';
      default:
        return 'blue';
    }
  };

  return (
    <div className="risk-assessment">
      <Card title="风险评估概况" className="risk-card">
        <Descriptions bordered column={2}>
          <Descriptions.Item label="风险等级">
            <Tag color={getRiskColor(rodentRiskAssessment.riskLevel)}>
              {rodentRiskAssessment.riskLevel}风险
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="风险评分">
            {rodentRiskAssessment.riskScore}分
          </Descriptions.Item>
          <Descriptions.Item label="历史活动">
            {rodentRiskAssessment.historicalActivity}
          </Descriptions.Item>
          <Descriptions.Item label="最近评估">
            {rodentRiskAssessment.lastAssessmentDate}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card title="风险雷达图" style={{ marginTop: '16px' }}>
        <ResponsiveContainer width="100%" height={300}>
          <RadarChart data={radarData}>
            <PolarGrid />
            <PolarAngleAxis dataKey="subject" />
            <PolarRadiusAxis angle={30} domain={[0, 100]} />
            <Radar
              name="风险指标"
              dataKey="value"
              stroke="#ff4d4f"
              fill="#ff4d4f"
              fillOpacity={0.6}
            />
          </RadarChart>
        </ResponsiveContainer>
      </Card>

      <Card title="易发生鼠害的因素" style={{ marginTop: '16px' }}>
        <List
          dataSource={rodentRiskAssessment.vulnerabilityFactors}
          renderItem={(item) => (
            <List.Item>
              <Tag color="red">{item}</Tag>
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export default RiskAssessment;