/* 组织管理页面样式 */

/* 左侧组织架构树样式 */
.org-tree-card {
  height: 1000px; /* 设置一个适当的固定高度 */
  width: 280px;
  overflow-y: auto; /* 当内容超过高度时显示垂直滚动条 */
  position: sticky;
  top: 20px;
  transition: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 确保即使在小屏幕上也保持固定宽度 */
.org-tree-container {
  width: 300px;
  min-width: 300px;
  flex-shrink: 0;
  flex-grow: 0;
  max-height: 1000px; /* 设置最大高度与子元素一致 */
}

/* 右侧表格区域自适应 */
.table-container {
  flex: 1;
  overflow-x: auto;
  max-height: 1000px; /* 设置最大高度 */
  overflow-y: auto; /* 垂直方向内容超出时显示滚动条 */
}

/* 用户表格容器 */
.user-table {
  height: 100%;
  overflow: hidden;
}

/* 避免内容区域溢出 */
.user-management {
  overflow-x: hidden;
}

/* 标签垂直居中对齐样式 */
.filter-label {
  display: inline-flex;
  align-items: center;
  height: 32px; /* 与 antd 输入框高度保持一致 */
  padding-right: 8px;
  margin-bottom: 0;
}

/* Space.Compact 组件内的垂直居中 */
.filter-compact {
  display: flex;
  align-items: center;
}
