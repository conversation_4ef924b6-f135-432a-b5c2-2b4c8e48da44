import { useState, useEffect } from 'react'
import {
  Modal,
  Tag,
  Table,
  Form,
  Input,
  Button,
  Space,
  message,
  Select
} from 'antd'
import { getCameraList, bindDevice ,getCameraDetail} from '../../service/deviceCamera'
import { debounce, values } from 'lodash'
import PropTypes from 'prop-types'
const { Option } = Select
export default function AssociationCamera ({
  cameraModalVisible,
  onCancel,
  deviceId,
  cameraId
}) {
  const [cameraData, setCameraData] = useState([]) // 获取摄像头数据
  const [selectedCamera, setSelectedCamera] = useState(null) // 选中的摄像头
  const [cameraLoading, setCameraLoading] = useState(false) //摄像头loading
  const [searchCameraData, setSearchCameraData] = useState({
    keyword: '',
    size: 5,
    page: 0
  }) // 分页查询摄像头
  const [totalPages, setTotalPages] = useState(0)

  const [form] = Form.useForm()

  // 获取摄像头列表
  const fetchCameraList = async (obj = {}) => {
    try {
      const res = await getCameraList({
        ...searchCameraData,
        ...obj
      })
      setTotalPages(res.totalPages)
      return res.content
    } catch (error) {
      console.error('Failed to fetch camera list:', error)
    }
  }

  // 下拉搜索
  const handleSearch = async value => {
    setCameraLoading(true)
    try {
      const data = await fetchCameraList({ keyword: value, page: 0 })
      setCameraData(data)
    } catch (error) {
      console.error('Failed to search cameras:', error)
    } finally {
      setCameraLoading(false)
    }
  }

  // 滚动加载
  const loadMore = async e => {
    if (cameraLoading) return
    const bottom =
      e.target.scrollHeight === e.target.scrollTop + e.target.clientHeight
    if (bottom && totalPages - 1 > searchCameraData.page) {
      setSearchCameraData({
        ...searchCameraData,
        page: searchCameraData.page + 1
      })
      setCameraLoading(true)
      const newOptions = await fetchCameraList({
        page: searchCameraData.page + 1
      })
      setCameraData(prevOptions => [...prevOptions, ...newOptions])
      setCameraLoading(false)
    }
  }


  const handleOk = async () => {
    try {
      await bindDevice({ cameraId: selectedCamera, deviceId: deviceId })
      onCancel(true)
      message.success("绑定成功")
    } catch (error) {
      message.error(error.message)
    }

     
  }



  const selectChange = (value,node) =>{
    setSelectedCamera(value)
    form.setFieldsValue({
      cameraModel:node.cameraModel,
      cameraName: node.cameraName
    })
  }

  useEffect(() =>{
    (async() =>{
      if (cameraId) {
        // 根据id获取详情
        try {
          const res =await getCameraDetail(cameraId)
          setCameraData([res])
          setSelectedCamera(cameraId)
          form.setFieldsValue({
            cameraId: cameraId,
            cameraModel:res.cameraModel,
            cameraName: res.cameraName
          })
        } catch (error) {
          console.log(error)
        }
        
      }
    })()
  },[cameraId])

  return (
    <Modal
      title='绑定摄像头'
      open={cameraModalVisible}
      okText={'绑定'}
      onOk={() => form.submit()}
      onCancel={() => {
        onCancel()
      }}
      width={500}
    >
      <Form form={form} layout='vertical' onFinish={handleOk}>
        <Form.Item
          name='cameraId'
          label='摄像头'
          rules={[{ required: true, message: '请选择摄像头' }]}
        >
          <Select
            showSearch
            allowClear
            placeholder='请输入摄像头编号'
            onPopupScroll={loadMore}
            onSearch={debounce(handleSearch, 300)}
            loading={cameraLoading}
            style={{ width: '100%' }}
            optionFilterProp='children'
            filterOption={false}
            value={selectedCamera}
            onChange={selectChange}
            onDropdownVisibleChange={async open => {
              if (open) {
                const tempSearch = {
                  keyword: '',
                  size: 50,
                  page: 0
                }
                const res = await fetchCameraList(tempSearch)
                setSearchCameraData(tempSearch)
                setCameraData(res)
                // if (res && res.length > 0 && !selectedCamera) {
                //   setSelectedCamera(res[0].cameraId)
                // }
              }
            }}
          >
            {cameraData?.map(option => (
              <Option key={option.cameraId} value={option.cameraId} cameraName={option.cameraName} cameraModel={option.cameraModel}>
                {option.cameraNo}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item name='cameraName' label='摄像头名称'>
          <Input placeholder='摄像头名称' disabled />
        </Form.Item>
        <Form.Item name='cameraModel' label='摄像头型号'>
          <Input placeholder='摄像头型号' disabled />
        </Form.Item>
        {/* <Form.Item>
          <Space>
            <Button type='primary' htmlType='submit'>
              搜索
            </Button>
            <Button onClick={handleReset}>重置</Button>
          </Space>
        </Form.Item> */}
      </Form>
    </Modal>
  )
}

AssociationCamera.propTypes = {
  cameraModalVisible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  deviceId: PropTypes.string,
  cameraId: PropTypes.string
}