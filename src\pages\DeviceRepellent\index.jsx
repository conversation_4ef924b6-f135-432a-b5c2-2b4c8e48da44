import { useState, useEffect } from "react";
import {
  Layout,
  Tabs,
  Button,
  Input,
  Space,
  Table,
  Tooltip,
  Tree,
  Dropdown,
  Form,
  Select,
  Modal,
  message,
  Tag,
  DatePicker,
  AutoComplete,
  List,
} from "antd";
import {
  DeleteOutlined,
  SettingOutlined,
  GlobalOutlined,
  TeamOutlined,
  EditOutlined,
  EyeOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  PlusOutlined,
  SearchOutlined,
  PoweroffOutlined,
  PlayCircleOutlined,
  StopOutlined,
  HistoryOutlined,
} from "@ant-design/icons";
import {
  getDeviceGroupsByLocation,
  getCustomerGroupsTree,
} from "../../service/deviceGroups";
import "./styles.css";
import {
  getDeviceRepellentList,
  updateDeviceRepellent,
  delManyDeviceRepellentByIds,
  openOrCloseDevice,
  upgradeDeviceMaxVersion,
  simulateRodentControl,
  getDeviceLikeDeviceNo,
  disableCamera,
  getLifeCycle,
} from "../../service/deviceRepellent";
import { dictionaryConfigApi } from "../../service/DictionaryConfig";
import DeviceDetail from "./detail";
import UpdateDevice from "./updateDevice";
import AssociationCamera from "./associationCamera";
import Configuration from "./configuration";
import VersionApply from "./versionApply";
import useSelect from "../../hooks/useSelect";
import CreateTask from "./createTask";
import { useLocation, useNavigate } from "react-router-dom";
import AdvancedSearch from "../../components/advancedSearch";
import { debounce, set } from "lodash";

const { Sider, Content } = Layout;

const DeviceRepellent = () => {
  const [activeTab, setActiveTab] = useState("location");
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [searchForm] = Form.useForm();
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [batchDeleteModalVisible, setBatchDeleteModalVisible] = useState(false); // 批量删除弹窗
  const [viewModalVisible, setViewModalVisible] = useState(false); // 查看设备弹窗
  const [selectedRowsId, setSelectedRowsId] = useState([]);
  const [currentDevice, setCurrentDevice] = useState(null);
  const [cameraModalVisible, setCameraModalVisible] = useState(false); // 绑定摄像头弹窗
  const [isConfigMode, setIsConfigMode] = useState(false); // 是否处于配置模式
  const [autocompleteOptions, setAutocompleteOptions] = useState([]); // 存储自动完成选项
  const [loading, setLoading] = useState(false); // 加载中
  const [searchParams, setSearchParams] = useState({
    page: 0,
    size: 10,
    keyword: "",
    groupType: "", // 0是行政区域，1是设备分组
    groupId: "", // 地理分组
    orgId: "", // 客户分组
    cameraNo: "", // 摄像头编号
    status: "", // 状态 0离线 1在线 2驱鼠中 3是禁用,
    isNotGroup: 1, // 0 是未分组，-1是未绑定 1是其他
  });
  const [deviceData, setDeviceData] = useState([]); // 驱鼠器列表
  const [total, setTotal] = useState(0);
  const [treeData, setTreeData] = useState(null);
  const [versionApplyVisible, setVersionApplyVisible] = useState(false);
  const [advancedSearchVisible, setAdvancedSearchVisible] = useState(false); // 高级搜索弹窗显示隐藏
  const [
    node,
    searchValue,
    autoExpandParent,
    filteredTreeData,
    setAutoExpandParent,
  ] = useSelect(treeData, setExpandedKeys);
  // 在state中添加新状态
  const [createTaskModalVisible, setCreateTaskModalVisible] = useState(false);
  const [dictType, setDictType] = useState([]);
  const [deviceTypeDict, setDeviceTypeDict] = useState([]);
  const [lifecycleModalVisible, setLifecycleModalVisible] = useState(false); // 生命周期弹窗
  const [lifecycleData, setLifecycleData] = useState([]); // 生命周期数据
  const location = useLocation();
  const navigate = useNavigate();

  // 更新搜索条件
  const updateSeachParam = (obj) => {
    setSearchParams((pre) => ({
      ...pre,
      ...obj,
    }));
  };
  // 获取字典类型
  const getDictType = async () => {
    const res = await dictionaryConfigApi.getDictItemByTypeCode("device_type");
    setDictType(res);
  };
  const getDeviceTypeDic = async () => {
    const res = await dictionaryConfigApi.getDictItemByTypeCode(
      "device_version"
    );
    setDeviceTypeDict(res);
  };

  // 获取位置树
  const getLocationTree = async () => {
    setLoading(true)
    try {
      const res = await getDeviceGroupsByLocation(1);
      const key = [];
      const transformNode = (item) => {
        key.push(item.id || item.locationId);
        return {
          title: item.groupName || item.description,
          key: item.id || item.locationId,
          isLocal: item.isLocal,
          hasCameras: true, // 默认所有节点都显示正常颜色
          children:
            item.children && item.children.length > 0
              ? item.children.map((child) => transformNode(child))
              : item.deviceLocationTreeVos &&
                item.deviceLocationTreeVos.length > 0
              ? item.deviceLocationTreeVos.map((child) => transformNode(child))
              : undefined,
        };
      };

      const transformedData = res.map((item) => transformNode(item));
      setTreeData([
        ...transformedData,
        { title: "未分组", key: 0, isLocal: 0, hasCameras: true },
        { title: "未绑定摄像头", key: -1, isLocal: 0, hasCameras: true },
      ]);
      setExpandedKeys(key);
      if (!location.state) {
        setSelectedKeys([key[0]]); // 默认选中第一个
        const tempObj = {
          groupId: key[0],
          groupType: 0,
          orgId: "",
          page: 0,
          isNotGroup: 1,
        };
        updateSeachParam(tempObj);
        fetchDeviceRepellentList(tempObj);
      }
      setLoading(false)
    } catch (error) {
      console.error("Failed to fetch location tree:", error);
    }
  };

  // 获取客户分组树
  const fetchCustomerGroupsTree = async () => {
    setLoading(true)
    try {
      const res = await getCustomerGroupsTree();
      const transformNode = (item) => ({
        title: item.ownershipName,
        key: item.ownershipId,
        hasCameras: true, // 默认所有节点都显示正常颜色
        children:
          item.children && item.children.length > 0
            ? item.children.map((child) => transformNode(child))
            : undefined,
      });
      const transformedData = res.map((item) => transformNode(item));
      setTreeData([
        ...transformedData,
        { title: "未分组", key: 0, isLocal: 0, hasCameras: true },
        { title: "未绑定摄像头", key: -1, isLocal: 0, hasCameras: true },
      ]);

      if (!location.state) {
        setSelectedKeys([transformedData[0]?.key || 0]); // 设置当前选中的
        const tempObj = {
          page: 0,
          orgId: transformedData[0]?.key || 0,
          groupType: "",
          groupId: "",
          isNotGroup: 1,
        };
        updateSeachParam(tempObj);
        fetchDeviceRepellentList(tempObj);
      }
      setLoading(false)
    } catch (error) {
      console.error("Failed to fetch customer groups tree:", error);
    }
  };

  // 获取驱鼠器列表
  const fetchDeviceRepellentList = async (obj = {}) => {
    setLoading(true)
    try {
      const res = await getDeviceRepellentList({
        ...searchParams,
        ...obj,
      });
      setDeviceData(res.content);
      setTotal(res.totalElements);

      // 更新当前选中节点的 hasCameras 状态
      if (treeData && selectedKeys.length > 0) {
        const currentNodeKey = selectedKeys[0];
        if (currentNodeKey !== 0 && currentNodeKey !== -1) {
          // 跳过特殊节点
          const updateNodeHasCameras = (nodes) => {
            return nodes.map((node) => {
              if (node.key === currentNodeKey) {
                return { ...node, hasCameras: res.content.length > 0 };
              }
              if (node.children) {
                return {
                  ...node,
                  children: updateNodeHasCameras(node.children),
                };
              }
              return node;
            });
          };

          setTreeData((prev) => {
            if (!prev) return prev;
            return updateNodeHasCameras([...prev]);
          });
          
        }
      }
      setLoading(false)
    } catch (error) {
      console.error("Failed to fetch camera list:", error);
    }
  };

  // 切换tab
  const onGroupTypeChange = async (key) => {
    if (window.cancelToken) {
      window.cancelToken(); // 取消请求
    }
    setTreeData(null);
    setActiveTab(key);
    if (key === "location") {
      await getLocationTree();
    } else {
      await fetchCustomerGroupsTree();
    }
  };

  // 树节点选中
  const onTreeSelect = (selectedKeys, node) => {
    if (window.cancelToken) {
      window.cancelToken();
    }
    if (selectedKeys.length > 0) {
      setSelectedKeys(selectedKeys);
      const tempObj = {
        [activeTab === "location" ? "groupId" : "orgId"]:
          selectedKeys[0] !== "isNotGroup" ? selectedKeys[0] : "",
        groupType: activeTab === "location" ? node.node.isLocal : "",
        isNotGroup: node.node.key === -1 ? -1 : node.node.key === 0 ? 0 : 1,
        page: 0,
        ...searchForm.getFieldsValue(),
      };
      updateSeachParam(tempObj);
      fetchDeviceRepellentList(tempObj);
    }
  };

  // 处理搜索
  const handleSearch = async (values) => {
    const tempObj = {
      page: 0, // 搜索时重置到第一页
      keyword: values.keyword || "",
      status: values.status ?? "",
      cameraNo: values.cameraNo || "",
    };
    updateSeachParam(tempObj);
    fetchDeviceRepellentList(tempObj);
    setAutocompleteOptions([]); // 清空自动完成选项
  };

  // 处理重置
  const handleReset = () => {
    searchForm.resetFields();
    const tempObj = {
      page: 0,
      size: 10,
      keyword: "",
      status: "",
      cameraNo: "",
    };
    updateSeachParam(tempObj);
    fetchDeviceRepellentList(tempObj);
    setAutocompleteOptions([]); // 清空自动完成选项
  };

  // 处理分页变化
  const handleTableChange = (pagination) => {
    const tempObj = {
      page: pagination.current - 1, // 后端分页从0开始
      size: pagination.pageSize,
    };
    updateSeachParam(tempObj);
    fetchDeviceRepellentList(tempObj);
  };

  // Handle edit device
  const handleEditDevice = (record) => {
    setCurrentDevice(record);
    setEditModalVisible(true);
  };

  // Handle edit modal ok
  const handleEditModalOk = async (values) => {
    try {
      await updateDeviceRepellent({
        ...values,
        deviceId: currentDevice.deviceId,
      });
      setEditModalVisible(false);
      message.success("设备更新成功");
      fetchDeviceRepellentList();
    } catch (error) {
      console.error("Validation failed:", error);
    }
  };

  // Handle edit modal cancel
  const handleEditModalCancel = () => {
    setEditModalVisible(false);
    setCurrentDevice(null);
  };

  // Handle delete device
  const handleDeleteDevice = async (record) => {
    try {
      await delManyDeviceRepellentByIds([record.deviceId]);
      message.success("设备删除成功");
      setDeleteModalVisible(false);
      fetchDeviceRepellentList();
    } catch (error) {
      console.error("Failed to delete device:", error);
      message.error("设备删除失败");
    }
  };

  // Handle batch delete
  const handleBatchDelete = async () => {
    try {
      await delManyDeviceRepellentByIds(selectedRowsId);
      message.success("批量删除成功");
      setBatchDeleteModalVisible(false);
      setSelectedRowsId([]);
      fetchDeviceRepellentList();
    } catch (error) {
      console.error("Failed to batch delete devices:", error);
      message.error("批量删除失败");
    }
  };

  // 处理驱鼠器编号搜索
  const handleDeviceNoSearch = debounce(async (value) => {
    if (value) {
      try {
        const response = await getDeviceLikeDeviceNo(value);
        console.log("response", response);
        if (response) {
          // 根据接口返回的实际数据结构进行处理
          // 接口返回的是字符串数组 ["TEST100", "TEST201", ...]
          setAutocompleteOptions(
            response.map((deviceNo) => ({
              value: deviceNo,
            }))
          );
        }
      } catch (error) {
        console.error("Failed to fetch device numbers:", error);
      }
    } else {
      setAutocompleteOptions([]);
    }
  }, 500);

  // Handle view device
  const handleViewDevice = (record) => {
    setCurrentDevice(record);
    setViewModalVisible(true);
  };

  // 在页面加载时获取传递的参数
  useEffect(() => {
    (async () => {
      if (!treeData) {
        await getLocationTree();
      }

      if (location.state) {
        const { status, cameraNo } = location.state;

        // 根据需求，处理status和cameraNo参数

        // 根据需求：地理分组树不选择任何数据，传入基础参数
        const tempObj = {
          page: 0, // API使用0作为第一页
          size: 10,
          status: status ?? "", // 传入跳转时的状态值
          cameraNo: cameraNo ?? "", // 传入摄像头编号
          // 清空其他分组相关参数，让地理分组树不选择任何数据
          isNotGroup: "",
          keyword: "",
          groupType: "",
          groupId: "",
          orgId: "",
        };

        // 不选中任何树节点
        setSelectedKeys([]);

        // 清空搜索表单，设置相应的值
        searchForm.setFieldsValue({
          keyword: "",
          cameraNo: cameraNo ?? "",
          status: status ?? "",
        });

        updateSeachParam(tempObj);
        fetchDeviceRepellentList(tempObj);
        navigate(".", { replace: true, state: null });
      }
    })();
  }, [location.state]);

  // 在组件挂载时获取字典数据
  useEffect(() => {
    getDictType();
    getDeviceTypeDic();
  }, []);

  useEffect(() => {
    // 清理函数，在组件卸载时取消请求
    return () => {
      if (window.cancelToken) {
        window.cancelToken(); // 取消请求
      }
    };
  }, []);

  // Handle view modal cancel
  const handleViewModalCancel = () => {
    setViewModalVisible(false);
    setCurrentDevice(null);
  };

  // 绑定摄像头
  const handleAssociationCamera = (record) => {
    setCurrentDevice(record);
    setCameraModalVisible(true);
  };

  // Handle configuration
  const handleConfiguration = (record) => {
    setCurrentDevice(record);
    setIsConfigMode(true);
  };

  // Handle exit configuration
  const handleExitConfig = () => {
    setIsConfigMode(false);
    setCurrentDevice(null);
  };

  // Handle device operation (ultrasound, LED, sound)
  const handleDeviceOperation = async (record, type, isOpen) => {
    try {
      await openOrCloseDevice({
        deviceId: record.deviceId,
        type: type,
        isOpen: isOpen,
      });
      message.success(`${isOpen ? "开启" : "关闭"}成功`);
      fetchDeviceRepellentList();
    } catch (error) {
      console.log("Failed to operate device:", error);
      // message.error(error.message)
    }
  };

  const upgrade = async (deviceId) => {
    const res = await upgradeDeviceMaxVersion(deviceId);
    if (res.code === 200) {
      message.success("已加入升级队列");
    }
  };

  // 模拟驱鼠
  const handleSimulateRodentControl = async (record) => {
    console.log(record.deviceId);
    const res = await simulateRodentControl(record.deviceId);
    if (res.code === 200) {
      message.success("模拟驱鼠成功");
    }
  };

  // 查看生命周期
  const handleViewLifecycle = async (record) => {
    try {
      const res = await getLifeCycle(record.deviceId);
      if (res) {
        setLifecycleData(res);
        setLifecycleModalVisible(true);
      }
    } catch (error) {
      console.error("Failed to fetch lifecycle data:", error);
      message.error("获取生命周期数据失败");
    }
  };

  // Table columns configuration
  const columns = [
    {
      title: "驱鼠器编号",
      dataIndex: "deviceNo",
      key: "deviceNo",
      // width: 150,
      ellipsis: true,
      render: (text, record) => (
        <a onClick={() => handleViewDevice(record)}>{text}</a>
      ),
    },
    {
      title: "驱鼠器状态",
      dataIndex: "deviceStatus",
      key: "deviceStatus",
      // width: 150,
      render: (status, record) => {
        const statusMap = {
          0: { text: "离线", color: "#ff4d4f" },
          1: { text: "在线", color: "#52c41a" },
          2: { text: "驱鼠中", color: "#1677ff" },
          3: { text: "禁用", color: "#faad14" },
        };
        const statusInfo = statusMap[status] || {
          text: "未知",
          color: "#d9d9d9",
        };
        return (
          <a
            onClick={() => {
              navigate("/device/monitor", {
                state: {
                  deviceNo: record.deviceNo,
                }
              });
            }}
          >
            <span style={{ color: statusInfo.color }}>{statusInfo.text}</span>
          </a>
        );
      },
    },
    {
      title: "驱鼠器名称",
      dataIndex: "deviceName",
      key: "deviceName",
      // width: 150,
      ellipsis: true,
    },
    {
      title: "摄像头编号",
      dataIndex: "cameraNo",
      key: "cameraNo",
      // width: 120,
      ellipsis: true,
      render: (text, record) => (
        <a
          onClick={() => {
            // 传递搜索参数
            navigate("/device/camera", {
              replace: true,
              state: {
                isNotGroup: searchParams.isNotGroup,
                keyword: record.cameraNo,
                groupType: searchParams.groupType,
                groupId: searchParams.groupId,
                orgId: searchParams.orgId,
              },
            });
          }}
        >
          {text}
        </a>
      ),
    },
    {
      title: "摄像头名称",
      dataIndex: "cameraName",
      key: "cameraName",
      // width: 120,
      ellipsis: true,
    },
    {
      title: "当前固件版本",
      dataIndex: "softwareVersion",
      key: "softwareVersion",
      // width: 120,
      ellipsis: true,
      render: (text, record) => (
        <Space>
          {text}
          <Tooltip title="升级"></Tooltip>
        </Space>
      ),
    },
    {
      title: "驱鼠器类型",
      dataIndex: "deviceType",
      key: "deviceType",
      // width: 120,
      render: (type) => {
        // 从字典中查找当前设备类型对应的项
        const findItem = dictType.find((item) => item.itemCode === type);
        // 如果找到则使用字典项的名称，否则显示未知
        const typeName = findItem ? findItem.itemName : "未知";
        return (
          <Space>
            <Tag color="blue">{typeName}</Tag>
          </Space>
        );
      },
    },
    {
      title: "驱鼠器型号",
      dataIndex: "model",
      ellipsis: false,
      render: (type) => {
        // 从字典中查找当前设备类型对应的项
        const findItem = deviceTypeDict.find((item) => item.itemCode === type);
        // 如果找到则使用字典项的名称，否则显示未知
        const typeName = findItem ? findItem.itemName : "-";
        return <Space>{typeName}</Space>;
      },
    },
    {
      title: "通信协议",
      dataIndex: "communicationProtocol",
      key: "communicationProtocol",
      // width: 100,
      render: (protocol) => {
        const protocolMap = {
          MQTT: { text: "MQTT", color: "#1890ff" },
          HTTP: { text: "HTTP", color: "#52c41a" },
          CoAP: { text: "CoAP", color: "#faad14" },
        };
        const protocolInfo = protocolMap[protocol] || {
          text: "未知",
          color: "#d9d9d9",
        };
        return (
          <Space>
            {/* style={{ color: protocolInfo.color }} */}
            <span>{protocolInfo.text}</span>
          </Space>
        );
      },
    },
    {
      title: "升级时间",
      dataIndex: "upgradeTime",
      ellipsis: true,
      key: "upgradeTime",
    },
    {
      title: "操作",
      key: "action",
      fixed: "right",
      width: 330,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="配置">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => handleConfiguration(record)}
            />
          </Tooltip>

          <Tooltip title="升级">
            <Button
              type="text"
              disabled={!(record.deviceStatus === 1)}
              icon={
                <img
                  src={
                    record.deviceStatus === 1
                      ? "/upload.png"
                      : "/upload_disable.png"
                  }
                  className="icon_img"
                />
              }
              onClick={() => {
                setCurrentDevice(record);
                setCreateTaskModalVisible(true);
              }}
            />
          </Tooltip>

          <Tooltip title="绑定摄像头">
            <Button
              type="text"
              icon={<img src={"/camara.png"} className="icon_img" />}
              onClick={() => handleAssociationCamera(record)}
            />
          </Tooltip>
          <Tooltip title="模拟驱鼠">
            <Button
              type="text"
              icon={
                <img src={"/rodent_infestation.png"} className="icon_img" />
              }
              onClick={() => handleSimulateRodentControl(record)}
            />
          </Tooltip>
          {record.deviceStatus !== 3 ? (
            <Tooltip title="禁用驱鼠器">
              <Button
                type="text"
                icon={
                  <StopOutlined
                    style={{
                      color: '#ff4d4f',
                      fontSize: '16px'
                    }}
                  />
                }
                onClick={() => {
                  disableCamera({
                    deviceId: record.deviceId,
                    isDeactivate: true,
                  }).then(() => {
                    fetchDeviceRepellentList(searchParams);
                    message.success("驱鼠器已禁用");
                  });
                }}
              />
            </Tooltip>
          ) : (
            <Tooltip title="启用驱鼠器">
              <Button
                type="text"
                icon={
                  <PlayCircleOutlined
                    style={{
                      color: '#52c41a',
                      fontSize: '16px'
                    }}
                  />
                }
                onClick={() => {
                  disableCamera({
                    deviceId: record.deviceId,
                    isDeactivate: false,
                  }).then(() => {
                    fetchDeviceRepellentList(searchParams);
                    message.success("驱鼠器已启用");
                  });
                }}
              />
            </Tooltip>
          )}
          <Tooltip title="查看生命周期">
            <Button
              type="text"
              icon={<HistoryOutlined />}
              onClick={() => handleViewLifecycle(record)}
            />
          </Tooltip>

          <Dropdown
            menu={{
              items: [
                {
                  key: "ultrasound",
                  label: !record.ultrasoundState ? "开启超声波" : "关闭超声波",
                  icon: (
                    <img
                      src={record.ultrasoundState ? "/1.png" : "/4.png"}
                      className="icon_img"
                    />
                  ),
                  onClick: () =>
                    handleDeviceOperation(record, "0", !record.ultrasoundState),
                },
                {
                  key: "led",
                  label: !record.ledState ? "开启LED" : "关闭LED",
                  icon: (
                    <img
                      src={record.ledState ? "/2.png" : "/5.png"}
                      className="icon_img"
                    />
                  ),
                  onClick: () =>
                    handleDeviceOperation(record, "1", !record.ledState),
                },
                {
                  key: "sound",
                  label: !record.soundState ? "开启喇叭" : "关闭喇叭",
                  icon: (
                    <img
                      src={record.soundState ? "/3.png" : "/6.png"}
                      className="icon_img"
                    />
                  ),
                  onClick: () =>
                    handleDeviceOperation(record, "2", !record.soundState),
                },
                {
                  key: "edit",
                  label: "修改设备",
                  icon: <EditOutlined />,
                  onClick: () => handleEditDevice(record),
                },
                {
                  key: "view",
                  label: "查看设备",
                  icon: <EyeOutlined />,
                  onClick: () => handleViewDevice(record),
                },
                {
                  key: "delete",
                  label: "删除设备",
                  icon: <DeleteOutlined />,
                  onClick: () => {
                    setCurrentDevice(record);
                    setDeleteModalVisible(true);
                  },
                },
              ],
            }}
          >
            <Tooltip title="更多操作">
              <Button type="text" icon={<EllipsisOutlined />} />
            </Tooltip>
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <Layout
      className="device-repellent-container"
      style={{ minHeight: "100%", width: "100%" }}
    >
      {isConfigMode ? (
        <Content className="device-content">
          <Configuration onCancel={handleExitConfig} device={currentDevice} />
        </Content>
      ) : (
        <>
          <Sider width={300} className="group-sider" >
            <div className="group-header">
              <Tabs
                activeKey={activeTab}
                onChange={onGroupTypeChange}
                items={[
                  {
                    key: "location",
                    label: (
                      <span>
                        <GlobalOutlined />
                        地理分组
                      </span>
                    ),
                  },
                  {
                    key: "customer",
                    label: (
                      <span>
                        <TeamOutlined />
                        客户分组
                      </span>
                    ),
                  },
                ]}
              />
            </div>
            {node()}
            {treeData && (
              <div className="group-tree-scroll">
                <Tree
                  showIcon
                  onExpand={(keys) => {
                    setExpandedKeys(keys);
                    setAutoExpandParent(false); // 用户手动展开后关闭自动展开
                  }}
                  autoExpandParent={autoExpandParent}
                  onSelect={onTreeSelect}
                  selectedKeys={selectedKeys}
                  expandedKeys={expandedKeys}
                  treeData={searchValue ? filteredTreeData : treeData}
                  blockNode
                  loading={loading}
                />
              </div>
            )}
          </Sider>
          <Content className="device-content">
            <div className="toolbar">
              <Space size="middle" className="toolbar-left">
                <Form form={searchForm} layout="inline" onFinish={handleSearch}>
                  <Form.Item name="keyword" label="驱鼠器编号">
                    <AutoComplete
                      placeholder="驱鼠器编号"
                      style={{ width: 200 }}
                      options={autocompleteOptions}
                      onSearch={handleDeviceNoSearch}
                      onSelect={(value) => {
                        searchForm.setFieldsValue({ keyword: value });
                      }}
                      filterOption={false}
                      onChange={(value) => {
                        if (!value) {
                          setAutocompleteOptions([]);
                        }
                      }}
                    />
                  </Form.Item>
                  <Form.Item name="cameraNo" label="摄像头编号">
                    <Input placeholder="摄像头编号" />
                  </Form.Item>
                  <Form.Item name="status" label="驱鼠器状态">
                    <Select
                      placeholder="设备状态"
                      style={{ width: 120 }}
                      options={[
                        { label: "全选", value: "" },
                        { label: "离线", value: 0 },
                        { label: "在线", value: 1 },
                        { label: "驱鼠中", value: 2 },
                        { label: "禁用", value: 3 },
                      ]}
                    />
                  </Form.Item>

                  <Form.Item>
                    <Space>
                      <Button type="primary" htmlType="submit">
                        搜索
                      </Button>
                      <Button onClick={handleReset}>重置</Button>
                    </Space>
                  </Form.Item>
                </Form>
              </Space>
              <Space size="middle" className="toolbar-right">
                {/* <Button
                  type='primary'
                  icon={<SearchOutlined />}
                  onClick={() => setAdvancedSearchVisible(true)}
                >
                  高级搜索
                </Button> */}
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  disabled={selectedRowsId.length === 0}
                  onClick={() => setCreateTaskModalVisible(true)}
                >
                  创建升级任务
                </Button>
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  disabled={selectedRowsId.length === 0}
                  onClick={() => setBatchDeleteModalVisible(true)}
                >
                  批量删除
                </Button>
              </Space>
            </div>
            <Table
              columns={columns}
              dataSource={deviceData}
              loading={loading}
              rowKey="deviceId"
              rowSelection={{
                type: "checkbox",
                selectedRowKeys: selectedRowsId,
                onChange: (selectedRowKeys) => {
                  setSelectedRowsId(selectedRowKeys);
                },
              }}
              className="device-table"
              scroll={{ x: 1500 }}
              pagination={{
                total: total,
                current: searchParams.page + 1,
                pageSize: searchParams.size,
                showTotal: (total) => `共 ${total} 条`,
              }}
              onChange={handleTableChange}
            />
          </Content>
        </>
      )}
      {editModalVisible && (
        <UpdateDevice
          visible={editModalVisible}
          onOk={handleEditModalOk}
          onCancel={handleEditModalCancel}
          currentDevice={currentDevice}
        />
      )}
      <Modal
        title="删除确认"
        open={deleteModalVisible}
        onOk={() => handleDeleteDevice(currentDevice)}
        onCancel={() => {
          setDeleteModalVisible(false);
          setCurrentDevice(null);
        }}
      >
        <p>
          确定要删除该驱鼠器设备{" "}
          {currentDevice?.deviceName ? `"${currentDevice?.deviceName}"` : null}{" "}
          吗？此操作不可恢复。
        </p>
      </Modal>
      {versionApplyVisible && (
        <VersionApply
          visible={versionApplyVisible}
          onCancel={(flag = false) => {
            setVersionApplyVisible(false);
            setCurrentDevice(null);
            if (flag) {
              fetchDeviceRepellentList();
            }
          }}
          currentDevice={currentDevice}
        />
      )}
      {createTaskModalVisible && (
        <CreateTask
          visible={createTaskModalVisible}
          deviceId={selectedRowsId}
          currentDevice={currentDevice}
          onCancel={() => {
            setCreateTaskModalVisible(false);
            setCurrentDevice(null);
            setSelectedRowsId([]);
          }}
        />
      )}
      <Modal
        title="批量删除确认"
        open={batchDeleteModalVisible}
        onOk={handleBatchDelete}
        onCancel={() => {
          setBatchDeleteModalVisible(false);
          setSelectedRowsId([]);
        }}
      >
        <p>
          确定要删除选中的 {selectedRowsId.length}{" "}
          个驱鼠器设备吗？此操作不可恢复。
        </p>
      </Modal>
      <DeviceDetail
        visible={viewModalVisible}
        onCancel={handleViewModalCancel}
        device={currentDevice}
      />
      {cameraModalVisible && (
        <AssociationCamera
          cameraModalVisible={cameraModalVisible}
          onCancel={(flag = false) => {
            setCameraModalVisible(false);
            setCurrentDevice(null);
            if (flag) {
              fetchDeviceRepellentList();
            }
          }}
          deviceId={currentDevice?.deviceId}
          cameraId={currentDevice?.cameraId}
        />
      )}

      {advancedSearchVisible && (
        <AdvancedSearch
          advancedSearchVisible={advancedSearchVisible}
          onCancel={() => {
            setAdvancedSearchVisible(false);
          }}
          data={[
            {
              label: "驱鼠器编号",
              value: "deviceNo",
            },
            {
              label: "驱鼠器名称",
              value: "deviceName",
            },
            {
              label: "摄像头编号",
              value: "cameraNo",
            },
            {
              label: "摄像头名称",
              value: "cameraName",
            },
            {
              label: "驱鼠器型号",
              value: "model",
              options: deviceTypeDict.map((ele) => ({
                label: ele.itemName,
                value: ele.itemCode,
              })),
              children: (setInputValue) => (
                <Select
                  placeholder="请选择驱鼠器型号"
                  style={{ width: "300px" }}
                  onChange={(value) => {
                    setInputValue(value);
                  }}
                  options={deviceTypeDict.map((ele) => ({
                    label: ele.itemName,
                    value: ele.itemCode,
                  }))}
                ></Select>
              ),
            },
            {
              label: "制造商信息",
              value: "manufacturer",
            },
            {
              label: "当前固件版本",
              value: "softwareVersion",
            },
            {
              label: "通信协议",
              value: "communicationProtocol",
            },
            {
              label: "MAC地址",
              value: "macAddress",
            },
            {
              label: "驱鼠器类型",
              value: "deviceType",
              options: dictType.map((ele) => ({
                label: ele.itemName,
                value: ele.itemCode,
              })),
              children: (setInputValue) => (
                <Select
                  placeholder="请选择驱鼠器类型"
                  style={{ width: "300px" }}
                  onChange={(value) => {
                    setInputValue(value);
                  }}
                  options={dictType.map((ele) => ({
                    label: ele.itemName,
                    value: ele.itemCode,
                  }))}
                ></Select>
              ),
            },
            {
              label: "驱鼠器状态",
              value: "status",
              options: [
                {
                  label: "离线",
                  value: 0,
                },
                {
                  label: "在线",
                  value: 1,
                },
                {
                  label: "驱鼠中",
                  value: 2,
                },
                {
                  label: "禁用",
                  value: 3,
                },
              ],
              children: (setInputValue) => (
                <Select
                  placeholder="请选择驱鼠器状态"
                  style={{ width: "300px" }}
                  onChange={(value) => {
                    setInputValue(value);
                  }}
                  options={[
                    {
                      label: "离线",
                      value: 0,
                    },
                    {
                      label: "在线",
                      value: 1,
                    },
                    {
                      label: "驱鼠中",
                      value: 2,
                    },
                    {
                      label: "禁用",
                      value: 3,
                    },
                  ]}
                />
              ),
            },
            {
              label: "注册日期",
              value: "installTime",
              children: (setInputValue) => (
                <DatePicker
                  style={{ width: "300px" }}
                  format={"YYYY-MM-DD"}
                  onChange={(value) => {
                    setInputValue(value ? value.format("YYYY-MM-DD") : "");
                  }}
                />
              ),
            },
            {
              label: "最后在线时间",
              value: "lastReportTime",
              children: (setInputValue) => (
                <DatePicker
                  style={{ width: "300px" }}
                  format={"YYYY-MM-DD HH:mm:ss"}
                  onChange={(value) => {
                    setInputValue(
                      value ? value.format("YYYY-MM-DD HH:mm:ss") : ""
                    );
                  }}
                />
              ),
            },
            {
              label: "固件升级状态",
              value: "firmwareState",
              options: [
                {
                  label: "等待升级",
                  value: 0,
                },
                {
                  label: "升级中",
                  value: 1,
                },
                {
                  label: "升级完成",
                  value: 2,
                },
                {
                  label: "升级失败",
                  value: 3,
                },
              ],
              children: (setInputValue) => (
                <Select
                  placeholder="请选择固件升级状态"
                  style={{ width: "300px" }}
                  onChange={(value) => {
                    setInputValue(value);
                  }}
                  options={[
                    {
                      label: "等待升级",
                      value: 0,
                    },
                    {
                      label: "升级中",
                      value: 1,
                    },
                    {
                      label: "升级完成",
                      value: 2,
                    },
                    {
                      label: "升级失败",
                      value: 3,
                    },
                  ]}
                />
              ),
            },
            {
              label: "超声波状态",
              value: "ultrasoundState",
              options: [
                {
                  label: "关闭",
                  value: false,
                },
                {
                  label: "开启",
                  value: true,
                },
              ],
              children: (setInputValue) => (
                <Select
                  placeholder="请选择超声波状态"
                  style={{ width: "300px" }}
                  onChange={(value) => {
                    setInputValue(value);
                  }}
                  options={[
                    {
                      label: "关闭",
                      value: false,
                    },
                    {
                      label: "开启",
                      value: true,
                    },
                  ]}
                />
              ),
            },
            {
              label: "LED状态",
              value: "ledState",
              options: [
                {
                  label: "关闭",
                  value: false,
                },
                {
                  label: "开启",
                  value: true,
                },
              ],
              children: (setInputValue) => (
                <Select
                  placeholder="请选择LED状态"
                  style={{ width: "300px" }}
                  onChange={(value) => {
                    setInputValue(value);
                  }}
                  options={[
                    {
                      label: "关闭",
                      value: false,
                    },
                    {
                      label: "开启",
                      value: true,
                    },
                  ]}
                />
              ),
            },
            {
              label: "喇叭状态",
              value: "soundState",
              options: [
                {
                  label: "关闭",
                  value: false,
                },
                {
                  label: "开启",
                  value: true,
                },
              ],
              children: (setInputValue) => (
                <Select
                  placeholder="请选择喇叭状态"
                  style={{ width: "300px" }}
                  onChange={(value) => {
                    setInputValue(value);
                  }}
                  options={[
                    {
                      label: "关闭",
                      value: false,
                    },
                    {
                      label: "开启",
                      value: true,
                    },
                  ]}
                />
              ),
            },
            {
              label: "升级时间",
              value: "upgradeTime",
              children: (setInputValue) => (
                <DatePicker
                  style={{ width: "300px" }}
                  format={"YYYY-MM-DD HH:mm:ss"}
                  showTime
                  onChange={(value) => {
                    setInputValue(
                      value ? value.format("YYYY-MM-DD HH:mm:ss") : ""
                    );
                  }}
                />
              ),
            },
          ]}
          getSearchData={(data) => {
            console.log(data);
          }}
        />
      )}
      {/* 生命周期弹窗 */}
      <Modal
        title="设备生命周期"
        open={lifecycleModalVisible}
        onCancel={() => setLifecycleModalVisible(false)}
        footer={null}
        centered
      >
        <div
          style={{ maxHeight: "400px", overflow: "auto", maxWidth: "800px" }}
        >
          <List
            itemLayout="horizontal"
            dataSource={lifecycleData}
            renderItem={(item) => (
              <List.Item>
                <div
                  style={{
                    background: "#fffbe6",
                    padding: "12px",
                    borderRadius: "4px",
                    width: "100%",
                    marginBottom: "8px",
                  }}
                >
                  <div
                    style={{ display: "flex", justifyContent: "space-between" }}
                  >
                    <span>{item.info}</span>
                  </div>
                  <div
                    style={{
                      color: "#999",
                      fontSize: "12px",
                      marginTop: "4px",
                    }}
                  >
                    {item.createdAt}
                  </div>
                </div>
              </List.Item>
            )}
          />
        </div>
      </Modal>
    </Layout>
  );
};

export default DeviceRepellent;
