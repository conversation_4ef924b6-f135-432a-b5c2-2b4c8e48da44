import  { useEffect, useState, useRef } from 'react'
import {
  // Math as CesiumMath,
  Viewer,
  Cartesian3,
  UrlTemplateImageryProvider,
  ScreenSpaceEventType,
} from 'cesium'
import { Card, Radio, Slider, Space, Button } from 'antd'
import { FullscreenOutlined } from '@ant-design/icons'

const GISMap = () => {
  const mapRef = useRef(null)
  const [layer, setLayer] = useState('basic')
  const [time, setTime] = useState(100)
  // 初始化地图
  const initMap = () => {
    mapRef.current = new Viewer('map-container', {
      animation: false,//是否创建动画小器件，左下角仪表，可以修改动画播放倍率、暂停动画等  
      baseLayerPicker: false,//是否显示右上角图层选择器，可以在小部件中选择底图和地形 
      fullscreenButton: false,//是否显示全屏按钮  
      creditContainer: document.createElement("div"), // 隐藏版权信息
      geocoder: false,//是否显示geocoder小器件，右上角查询按钮  
      homeButton: false,//是否显示Home按钮  
      infoBox: false,
      sceneModePicker: false,//是否显示3D/2D选择器  
      selectionIndicator: false,//是否显示选取指示器组件，选择实体时，实体本身会出现一个绿色方块，如果为false，选择实体则不出现  
      timeline: false,//是否显示时间轴  
      navigationHelpButton: false,//是否显示右上角的帮助按钮  
      scene3DOnly: true,//如果设置为true，则所有几何图形以3D模式绘制以节约GPU资源  
      fullscreenElement: document.body,//全屏时渲染的HTML元素,  
      useDefaultRenderLoop: true,//如果需要控制渲染循环，则设为true  
      targetFrameRate: undefined,//使用默认render loop时的帧率  
      showRenderLoopErrors: false,//如果设为true，将在一个HTML面板中显示错误信息  
      automaticallyTrackDataSourceClocks: true,//自动追踪最近添加的数据源的时钟设置  
       // imageryProvider: new UrlTemplateImageryProvider({
      //   url: `${import.meta.env.VITE_API_MAP_RUL}/map/{z}/{x}/{y}.jpg`,
      // }),
      imageryProvider: new UrlTemplateImageryProvider({
        url: 'https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
        style: 'default',
        format: 'image/png',
      }),
  
  })


    // 视角位于成都
    mapRef.current.camera.setView({
      destination: Cartesian3.fromDegrees(104.06637, 30.67118, 10000)
    })


    // 禁用双击事件
    mapRef.current.cesiumWidget.screenSpaceEventHandler.removeInputAction(
      ScreenSpaceEventType.LEFT_DOUBLE_CLICK
    )

    // 鼠标拖动
    mapRef.current.scene.screenSpaceCameraController.enableTranslate = false;
    // 放大缩小
    mapRef.current.scene.screenSpaceCameraController.enableZoom = true;
    // 开启抗锯齿
    mapRef.current.scene.postProcessStages.fxaa.enabled = false
  }

  useEffect(() => {
    if(!mapRef.current){
      initMap()
    }

    return () => {
      mapRef.current = null
    }
 
  }, [])

  const handleLayerChange = e => {
    setLayer(e.target.value)
  }

  // const handleTimeChange = value => {
  //   setTime(value)
  // }

  const handleFullscreen = () => {
    const mapContainer = document.getElementById('map-container')
    mapContainer.requestFullscreen()
    
    // if (!document.fullscreenElement) {
    //   mapContainer.requestFullscreen()
    // } else {
    //   document.exitFullscreen()
    // }
  }

  return (
    <div
      className='map-container'
      style={{ position: 'relative', height: 600 }}
    >
      <div id='map-container' style={{ height: 600 }}></div>
      
      {/* Fullscreen Button */}
      <Button
        type="primary"
        icon={<FullscreenOutlined />}
        onClick={handleFullscreen}
        style={{
          position: 'absolute',
          bottom: '20px',
          right: '20px',
          zIndex: 100,
          background: 'rgba(0,0,0,0.7)',
          border: '1px solid rgba(255,255,255,0.1)',
          backdropFilter: 'blur(10px)',
          borderRadius: '8px'
        }}
      />

      {/* Layer Controls */}
      <div
        className='map-controls-wrapper'
        style={{
          position: 'absolute',
          bottom: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 100,
          width: 'auto',
          display: 'flex',
          justifyContent: 'center'
        }}
      >
        <Card
          className='map-controls'
          style={{
            background: 'rgba(0,0,0,0.7)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.1)',
            borderRadius: '8px'
          }}
        >
          <Space
            direction='vertical'
            size='small'
            style={{ width: '100%', alignItems: 'center' }}
          >
            <Radio.Group
              value={layer}
              onChange={handleLayerChange}
              buttonStyle='solid'
              style={{
                display: 'flex',
                backgroundColor: 'rgba(0,0,0,0.3)',
                padding: '4px',
                borderRadius: '4px'
              }}
            >
              <Radio.Button
                value='basic'
                style={{ flex: 1, textAlign: 'center' }}
              >
                基础地图
              </Radio.Button>
              <Radio.Button
                value='device'
                style={{ flex: 1, textAlign: 'center' }}
              >
                设备分布
              </Radio.Button>
              {/* <Radio.Button
                value='activity'
                style={{ flex: 1, textAlign: 'center' }}
              >
                活动热力
              </Radio.Button>
              <Radio.Button
                value='coverage'
                style={{ flex: 1, textAlign: 'center' }}
              >
                覆盖范围
              </Radio.Button> */}
            </Radio.Group>

            {/* <Slider
              min={0}
              max={100}
              value={time}
              onChange={handleTimeChange}
              tooltip={{
                formatter: value => `${value}%`
              }}
              style={{
                width: '200px',
                margin: '8px 0'
              }}
            /> */}
          </Space>
        </Card>
      </div>
    </div>
  )
}

export default GISMap
