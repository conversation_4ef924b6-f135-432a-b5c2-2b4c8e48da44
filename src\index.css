:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(0, 0, 0, 0.88);
  background-color: #f0f2f5;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*{
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 100vw;
  min-height: 100vh;
  overflow: hidden;
}

#root {
  width: 100vw;
  height: 100vh;
}

.dashboard {
  height: 100%;
  padding: 16px;
  overflow-y: auto;
}

.dashboard .ant-card {
  height: 100%;
}

.icon-wrapper {
  text-align: center;
  margin-top: 16px;
}

.icon-wrapper .anticon {
  font-size: 24px;
}

.logo-container {
  display: flex;
  align-items: center;
  padding: 16px;
  color: white;
}

.logo {
  height: 32px;
  margin-right: 8px;
}

.platform-name {
  margin: 0;
  font-size: 18px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Menu styling */
.ant-menu-inline .ant-menu-item,
.ant-menu-inline .ant-menu-submenu-title {
  margin: 0;
  width: 100%;
}

.ant-menu-inline .ant-menu-item::after {
  display: none;
}

/* Header styling */
.header-container {
  padding: 0 16px !important;
  background: #fff !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-right {
  display: flex;
  align-items: center;
  height: 100%;
}

.search-container {
  display: flex;
  align-items: center;
  height: 100%;
  margin-right: 16px;
}

.header-search {
  margin: auto 0;
}

.header-search .ant-input-affix-wrapper {
  border-radius: 4px;
}

.ant-breadcrumb {
  margin: 0;
}

/* Override Ant Design button styles in header */
.header-container .ant-btn-text {
  color: rgba(0, 0, 0, 0.65);
}

.header-container .ant-btn-text:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

/* Login page styles */
.login-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url('./login_bg.png') no-repeat center center/cover;
}

.login-box {
  background: white;
  padding: 40px;
  background: rgba(255,255,255,0.52);
  border-radius: 20px 20px 20px 20px;
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 20px;
}

.login-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

.login-header div:nth-child(1) {
  color: #2C3E50;
  font-size: 30px;
  font-weight: 500;
  margin: 0;
}

.login-header div:nth-child(2) {
  color: #2C3E50;
  font-size: 24px;
  font-weight: 400;
  margin: 0;
}

/* Button styling */
/* .ant-btn-primary {
  background: linear-gradient(135deg, #28B463 0%, #218C74 100%);
  border: none;
  height: 40px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #218C74 0%, #145A32 100%);
  transform: translateY(-1px);
} */

/* Input styling */
.ant-input-affix-wrapper {
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.ant-input-affix-wrapper:hover,
.ant-input-affix-wrapper:focus {
  border-color: #28B463;
  box-shadow: 0 0 0 2px rgba(40, 180, 99, 0.1);
}

.beian-fixed {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0 auto;
  text-align: center;
  z-index: 999;
  background: transparent;
  pointer-events: auto;
  margin: 20px 0px;
  font-size: 12px;
}

.login-tab-switch {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #FEFEFF;
  border-radius: 10px;
  margin-bottom: 24px;
  margin-left: auto;
  margin-right: auto;
}
.login-tab-switch > div {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  padding: 12px 0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}
.tab-active {
  background: #a4cafe;
  color: #fff;
  box-shadow: 0 2px 8px rgba(164,202,254,0.15);
}
.tab-inactive {
  background: #fff;
  color: #6ba6e7;
}

.login_btn{
  background: #6DA7F2;
  height: 48px;
}


/* 菜单样式修改 */
.layout-div>.ant-layout-sider{
  background: #152259 !important;
}

.layout-div>.ant-layout-sider>.ant-layout-sider-trigger{
background: #152259 !important;
}


.ant-layout-sider-trigger>span{
  background: #fff;
  color:#000000;
  width: 22px; 
  height: 22px;
  border-radius: 50%;
}

.ant-layout-sider-trigger>span>svg{
  margin-left: 4px;
}

.layout-div > .ant-layout-sider .ant-layout-sider-children .ant-menu .ant-menu-submenu div.ant-menu-submenu-title{
   background: #152259 !important;
   border-radius: 0px !important;
}

.layout-div > .ant-layout-sider .ant-layout-sider-children .ant-menu .ant-menu-submenu .ant-menu-sub{
  background: #0f1840 !important;
}