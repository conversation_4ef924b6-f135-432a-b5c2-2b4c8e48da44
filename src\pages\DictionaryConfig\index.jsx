import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Table,
  Tag,
  Breadcrumb,
  Input,
  Select,
  Tree,
  Modal,
  Form,
  Radio,
  message,
  Divider,
  TreeSelect,
  Upload,
  Tooltip,
  Dropdown,
} from "antd";
import {
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  LockOutlined,
  UnlockOutlined,
  ExclamationCircleOutlined,
  EllipsisOutlined,
} from "@ant-design/icons";
import { dictionaryConfigApi } from "../../service/DictionaryConfig";
import "./styles.css";

const { Search } = Input;
const { Option } = Select;
const { confirm } = Modal;

const DictionaryConfig = () => {
  const [selectedDictType, setSelectedDictType] = useState(null);
  const [selectedDictTypeInfo, setSelectedDictTypeInfo] = useState(null);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createDictItemModalVisible, setCreateDictItemModalVisible] =
    useState(false);
  const [editDictItemModalVisible, setEditDictItemModalVisible] =
    useState(false);
  const [editDictTypeModalVisible, setEditDictTypeModalVisible] =
    useState(false);
  const [currentDictItem, setCurrentDictItem] = useState(null);
  const [currentDictType, setCurrentDictType] = useState(null);
  const [dictTypeTreeData, setDictTypeTreeData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [dictItemForm] = Form.useForm();
  const [editDictItemForm] = Form.useForm();
  const [editDictTypeForm] = Form.useForm();
  const [dictItems, setDictItems] = useState([
    {
      key: "1",
      code: "NORMAL",
      name: "正常",
      value: "0",
      status: "enable ",
      orderNum: 1,
      description: "设备正常运行状态",
    },
    {
      key: "2",
      code: "WARN",
      name: "警告",
      value: "1",
      status: "enable ",
      orderNum: 2,
      description: "设备警告状态",
    },
    {
      key: "3",
      code: "ERROR",
      name: "错误",
      value: "2",
      status: "enable ",
      orderNum: 3,
      description: "设备错误状态",
    },
    {
      key: "4",
      code: "FATAL",
      name: "致命",
      value: "3",
      status: "disabled",
      orderNum: 4,
      description: "设备致命错误状态",
    },
  ]);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [uploadFile, setUploadFile] = useState(null);

  // 获取字典类型树数据
  useEffect(() => {
    setLoading(true);
    dictionaryConfigApi
      .getDictTypeTree()
      .then((data) => {
        setDictTypeTreeData(data || []);

        // 自动选中第一个字典类型并加载其字典项数据
        if (data && data.length > 0) {
          const firstNode = data[0];
          const firstTypeId =
            firstNode.id ||
            firstNode.typeCode ||
            firstNode.code ||
            String(Math.random());
          setSelectedDictType(firstTypeId);

          // 调用接口获取该类型的字典项数据
          dictionaryConfigApi
            .getDictTypeById(firstTypeId)
            .then((typeData) => {
              if (!typeData || !typeData.typeCode) {
                message.error("获取字典类型信息失败");
                return;
              }

              // 保存字典类型详情
              setSelectedDictTypeInfo(typeData);

              // 使用typeCode调用接口获取字典项数据
              return dictionaryConfigApi.getDictItemByTypeCode(
                typeData.typeCode
              );
            })
            .then((itemData) => {
              if (itemData) {
                // 确保每个字典项都有一个唯一的key属性
                const itemsWithKeys = itemData.map(item => ({
                  ...item,
                  key: item.id || String(Math.random())
                }));
                setDictItems(itemsWithKeys || []);
              }
            })
            .catch((error) => {
              console.error("获取字典项数据失败:", error);
            })
            .finally(() => {
              setLoading(false);
            });
        }
      })
      .catch((error) => {
        console.error("获取字典类型树失败:", error);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  // Dictionary item columns
  const columns = [
    // {
    //   title: '序号',
    //   dataIndex: 'sortOrder',
    //   key: 'sortOrder',
    //   width: 80
    // },
    {
      title: "字典编码",
      dataIndex: "itemCode",
      align: "center",
      key: "itemCode",
    },
    {
      title: "字典名称",
      dataIndex: "itemName",
      align: "center",
      key: "itemName",
    },
    {
      title: "字典状态",
      dataIndex: "status",
      align: "center",
      key: "status",
      render: (status) => (
        <Tag
          color={
            status === "enable" || status === "enable" ? "success" : "default"
          }
        >
          {status === "enable" || status === "enable" ? "启用" : "禁用"}
        </Tag>
      ),
    },
    {
      title: "字典描述",
      dataIndex: "description",
      align: "center",
      key: "description",
    },
    {
      title: "操作",
      key: "action",
      align: "center",
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              disabled={
                !(record.status === "enable " || record.status === "enable")
              }
              onClick={() => handleEditDictItem(record.id)}
            />
          </Tooltip>
          <Tooltip
            title={
              record.status === "enable " || record.status === "enable"
                ? "禁用"
                : "启用"
            }
          >
            <Button
              type="text"
              icon={
                record.status === "enable " || record.status === "enable" ? (
                  <UnlockOutlined />
                ) : (
                  <LockOutlined />
                )
              }
              onClick={() => {
                // 直接基于当前行状态调用对应API
                if (record.status === "enable " || record.status === "enable") {
                  // 调用禁用接口
                  handleDisableDictItem(record.id);
                } else {
                  // 调用启用接口
                  handleEnableDictItem(record.id);
                }
              }}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              disabled={
                !(record.status === "enable " || record.status === "enable")
              }
              onClick={() => handleDeleteDictItem(record.id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 将API返回的数据转换为Tree组件需要的格式
  const formatTreeData = (data) => {
    if (!data || !Array.isArray(data)) {
      return [];
    }

    return data.map((item) => {
      // 检查是否存在必要的属性
      if (!item.typeName && !item.name) {
        message.error("数据缺少名称属性:", item);
      }

      // 使用typeName属性或fallback到其他可能的名称属性
      const displayName = item.typeName || item.name || item.title || "未命名";

      // 映射scope值到对应的中文名称
      let scopeText = "应用级";
      if (item.scope === "system") {
        scopeText = "系统级";
      } else if (item.scope === "application") {
        scopeText = "应用级";
      } else if (item.scope === "tenant") {
        scopeText = "租户级";
      }

      const TreeNodeTitle = () => {
        const [showButtons, setShowButtons] = useState(false);

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              width: "100%",
              minWidth: "425px",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                overflow: "hidden",
                whiteSpace: "nowrap",
                textOverflow: "ellipsis",
              }}
            >
              <span style={{ marginRight: "8px" }}>{displayName}</span>
              <Tag
                color={scopeText === "系统级" ? "blue" : "green"}
                style={{ flexShrink: 0 }}
              >
                {scopeText}
              </Tag>
            </div>
            {item.editable !== 0 && (
              <div
                style={{
                  display: "flex",
                  marginLeft: "8px",
                  flexShrink: 0,
                  position: "absolute",
                  right: "24px",
                  alignItems: "center",
                }}
              >
                {showButtons && (
                  <>
                    <Tooltip title="编辑">
                      <Button
                        type="text"
                        size="small"
                        icon={<EditOutlined />}
                        disabled={!(item.status === "enable" || item.status === "enable ")}
                        onClick={(e) => {
                          e.stopPropagation();
                          const id = item.id || item.typeCode || item.code;
                          if (id) {
                            handleEditDictType(id);
                          } else {
                            message.error("字典类型ID不存在");
                          }
                        }}
                      />
                    </Tooltip>
                    <Tooltip title={item.status === "enable" || item.status === "enable " ? "禁用" : "启用"}>
                      <Button
                        type="text"
                        size="small"
                        icon={item.status === "enable" || item.status === "enable " ? <UnlockOutlined /> : <LockOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          const id = item.id || item.typeCode || item.code;
                          if (id) {
                            setLoading(true);
                            const apiCall = item.status === "enable" || item.status === "enable " 
                              ? dictionaryConfigApi.disableDictType([id])
                              : dictionaryConfigApi.enableDictType([id]);

                            apiCall
                              .then((result) => {
                                if (result) {
                                  message.success(
                                    item.status === "enable" || item.status === "enable "
                                      ? "字典类型禁用成功！"
                                      : "字典类型启用成功！"
                                  );
                                  return dictionaryConfigApi.getDictTypeTree();
                                }
                                return null;
                              })
                              .then((data) => {
                                if (data) {
                                  setDictTypeTreeData(data);
                                  // 清空右侧字典项数据和选中的字典类型状态
                                  setDictItems([]);
                                  setSelectedDictType(null);
                                  setSelectedDictTypeInfo(null);
                                }
                              })
                              .catch((error) => {
                                console.error(
                                  item.status === "enable" || item.status === "enable "
                                    ? "禁用字典类型失败:"
                                    : "启用字典类型失败:",
                                  error
                                );
                                message.error(
                                  item.status === "enable" || item.status === "enable "
                                    ? "禁用字典类型失败: "
                                    : "启用字典类型失败: " + error.message
                                );
                              })
                              .finally(() => {
                                setLoading(false);
                              });
                          } else {
                            message.error("字典类型ID不存在");
                          }
                        }}
                      />
                    </Tooltip>
                    <Tooltip title="删除">
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        disabled={!(item.status === "enable" || item.status === "enable ")}
                        onClick={(e) => {
                          e.stopPropagation();
                          const id = item.id || item.typeCode || item.code;
                          if (id) {
                            handleDeleteDictType(id);
                          } else {
                            message.error("字典类型ID不存在");
                          }
                        }}
                      />
                    </Tooltip>
                  </>
                )}
                <Button
                  type="text"
                  size="small"
                  icon={<EllipsisOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowButtons(!showButtons);
                  }}
                />
              </div>
            )}
          </div>
        );
      };

      return {
        title: <TreeNodeTitle />,
        key: item.id || item.typeCode || item.code || String(Math.random()),
        children: item.childrenList
          ? formatTreeData(item.childrenList)
          : item.children
          ? formatTreeData(item.children)
          : undefined,
      };
    });
  };

  const handleCreateDictType = (values) => {
    const responsePromise = dictionaryConfigApi.addDictType(values);
    responsePromise
      .then((response) => {
        if (response.code == 200) {
          message.success("字典类型创建成功！");
          // 添加成功后重新获取字典类型树数据
          return dictionaryConfigApi.getDictTypeTree();
        }
      })
      .then((data) => {
        if (data) {
          // 更新树数据状态
          setDictTypeTreeData(data);
          setCreateModalVisible(false);
          form.resetFields();
        }
      })
      .catch((error) => {
        console.error("添加字典类型失败:", error);
      });
  };

  const handleEditDictItem = (id) => {
    // 调用API获取字典项详情
    setLoading(true);
    dictionaryConfigApi
      .getDictItemById(id)
      .then((data) => {
        if (data) {
          // 设置当前编辑的字典项
          setCurrentDictItem(data);
          // 打开编辑弹窗
          setEditDictItemModalVisible(true);
          // 设置表单字段值
          editDictItemForm.setFieldsValue({
            itemCode: data.itemCode,
            itemName: data.itemName,
            itemValue: data.itemValue,
            description: data.description,
            status: data.status,
            version: data.version,
          });
        }
      })
      .catch((error) => {
        console.error("获取字典项详情失败:", error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 处理禁用字典项
  const handleDisableDictItem = (id) => {
    if (!id) {
      message.error("字典项ID不存在");
      return;
    }

    // 调用禁用接口
    setLoading(true);
    dictionaryConfigApi
      .disableDictItem([id])
      .then((result) => {
        if (result) {
          message.success("字典项禁用成功！");

          // 立即更新本地状态以反映图标变化
          setDictItems((prevItems) =>
            prevItems.map((item) =>
              item.id === id ? { ...item, status: "disabled" } : item
            )
          );

          // 重新加载字典项列表
          if (selectedDictType) {
            return dictionaryConfigApi
              .getDictTypeById(selectedDictType)
              .then((typeData) => {
                if (typeData && typeData.typeCode) {
                  return dictionaryConfigApi.getDictItemByTypeCode(
                    typeData.typeCode
                  );
                }
                return null;
              });
          }
          return null;
        }
        return null;
      })
      .then((itemData) => {
        if (itemData) {
          // 确保每个字典项都有一个唯一的key属性
          const itemsWithKeys = itemData.map(item => ({
            ...item,
            key: item.id || String(Math.random())
          }));
          setDictItems(itemsWithKeys);
        }
      })
      .catch((error) => {
        console.error("禁用字典项失败:", error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 处理启用字典项
  const handleEnableDictItem = (id) => {
    if (!id) {
      message.error("字典项ID不存在");
      return;
    }

    // 调用启用接口
    setLoading(true);
    dictionaryConfigApi
      .enableDictItem([id])
      .then((result) => {
        if (result) {
          message.success("字典项启用成功！");

          // 立即更新本地状态以反映图标变化
          setDictItems((prevItems) =>
            prevItems.map((item) =>
              item.id === id ? { ...item, status: "enable" } : item
            )
          );

          // 重新加载字典项列表
          if (selectedDictType) {
            return dictionaryConfigApi
              .getDictTypeById(selectedDictType)
              .then((typeData) => {
                if (typeData && typeData.typeCode) {
                  return dictionaryConfigApi.getDictItemByTypeCode(
                    typeData.typeCode
                  );
                }
                return null;
              });
          }
          return null;
        }
        return null;
      })
      .then((itemData) => {
        if (itemData) {
          // 确保每个字典项都有一个唯一的key属性
          const itemsWithKeys = itemData.map(item => ({
            ...item,
            key: item.id || String(Math.random())
          }));
          setDictItems(itemsWithKeys);
        }
      })
      .catch((error) => {
        console.error("启用字典项失败:", error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 处理删除字典项
  const handleDeleteDictItem = (id) => {
    if (!id) {
      message.error("字典项ID不存在");
      return;
    }

    confirm({
      title: "确认删除",
      icon: <ExclamationCircleOutlined />,
      content: "确定要删除该字典项吗？删除后无法恢复！",
      okText: "是",
      okType: "danger",
      cancelText: "否",
      centered: true,
      onOk() {
        // 调用删除接口
        setLoading(true);
        dictionaryConfigApi
          .deleteDictItem([id])
          .then((result) => {
            if (result) {
              message.success("字典项删除成功！");

              // 重新加载字典项列表
              if (selectedDictType) {
                return dictionaryConfigApi
                  .getDictTypeById(selectedDictType)
                  .then((typeData) => {
                    if (typeData && typeData.typeCode) {
                      return dictionaryConfigApi.getDictItemByTypeCode(
                        typeData.typeCode
                      );
                    }
                    return null;
                  });
              }
              return null;
            }
            return null;
          })
          .then((itemData) => {
            if (itemData) {
              // 确保每个字典项都有一个唯一的key属性
              const itemsWithKeys = itemData.map(item => ({
                ...item,
                key: item.id || String(Math.random())
              }));
              setDictItems(itemsWithKeys);
            }
          })
          .catch((error) => {
            console.error("删除字典项失败:", error);
          })
          .finally(() => {
            setLoading(false);
          });
      },
    });
  };

  // 处理删除字典类型
  const handleDeleteDictType = (id) => {
    if (!id) {
      message.error("字典类型ID不存在");
      return;
    }

    confirm({
      title: "确认删除",
      icon: <ExclamationCircleOutlined />,
      content: "确定要删除该字典类型吗？删除后无法恢复！",
      okText: "是",
      okType: "danger",
      cancelText: "否",
      centered: true,
      onOk() {
        setLoading(true);
        dictionaryConfigApi
          .deleteDictType([id])
          .then((result) => {
            if (result) {
              message.success("字典类型删除成功！");
              // 删除成功后，清空字典项和选中状态
              setDictItems([]);
              setSelectedDictType(null);
              setSelectedDictTypeInfo(null);
              return dictionaryConfigApi.getDictTypeTree();
            }
            return null;
          })
          .then((data) => {
            if (data) {
              setDictTypeTreeData(data);
            }
          })
          .catch((error) => {
            console.error("删除字典类型失败:", error);
            message.error("删除字典类型失败: " + error.message);
          })
          .finally(() => {
            setLoading(false);
          });
      },
    });
  };

  // 处理编辑字典类型
  const handleEditDictType = (id) => {
    // 调用API获取字典类型详情
    setLoading(true);
    dictionaryConfigApi
      .getDictTypeById(id)
      .then((data) => {
        if (data) {
          // 设置当前编辑的字典类型
          setCurrentDictType(data);
          // 打开编辑弹窗
          setEditDictTypeModalVisible(true);
          // 设置表单字段值
          editDictTypeForm.setFieldsValue({
            typeCode: data.typeCode,
            typeName: data.typeName,
            status: data.status,
            scope: data.scope,
            editable: data.editable, // 确保editable正确转换为字符串形式
            description: data.description,
            remark: data.remark,
            parentId: data.parentId, // 添加父级类型默认值
          });
        }
      })
      .catch((error) => {
        console.error("获取字典类型详情失败:", error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className="dictionary-config">
      {/* Header */}
      <div className="page-header">
        <div className="header-left">
          {/* <Breadcrumb
            items={[
              { title: "首页" },
              { title: "系统管理" },
              { title: "字典配置" },
            ]}
          />
          <h2>字典配置</h2> */}
        </div>
        <div className="header-right">
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              添加类型
            </Button>
            <Button
              onClick={() => {
                setLoading(true);
                dictionaryConfigApi
                  .exportDictItemTemplate()
                  .then(() => {
                    message.success("模板下载成功！");
                  })
                  .catch((error) => {
                    console.error("模板下载失败:", error);
                  })
                  .finally(() => {
                    setLoading(false);
                  });
              }}
            >
              下载导入模版
            </Button>
            <Button
              icon={<UploadOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              导入
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={() => {
                setLoading(true);
                dictionaryConfigApi
                  .exportDictItem()
                  .then((response) => {
                    const url = window.URL.createObjectURL(
                      new Blob([response])
                    );
                    const link = document.createElement("a");
                    link.href = url;
                    link.setAttribute("download", "dictionary-items.xlsx");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                    message.success("导出成功！");
                  })
                  .catch((error) => {
                    console.error("导出失败:", error);
                  })
                  .finally(() => {
                    setLoading(false);
                  });
              }}
            >
              导出
            </Button>
            {/* <Button 
              icon={<ReloadOutlined />}
              onClick={() => {
                setLoading(true);
                // 重新获取字典类型树
                dictionaryConfigApi.getDictTypeTree()
                  .then(data => {
                    setDictTypeTreeData(data || []);
                    
                    // 如果有选中的字典类型，则刷新相关数据
                    if (selectedDictType) {
                      // 获取字典类型详情
                      return dictionaryConfigApi.getDictTypeById(selectedDictType)
                        .then(typeData => {
                          if (!typeData || !typeData.typeCode) {
                            message.error('获取字典类型信息失败');
                            return null;
                          }
                          
                          // 使用typeCode获取字典项列表
                          return dictionaryConfigApi.getDictItemByTypeCode(typeData.typeCode)
                            .then(itemData => {
                              setDictItems(itemData || []);
                              // 所有数据刷新成功后显示成功消息
                              message.success('数据刷新成功！');
                              return true;
                            });
                        });
                    } else {
                      // 只刷新了字典类型树，也显示成功消息
                      message.success('数据刷新成功！');
                      return null;
                    }
                  })
                  .catch(error => {
                    console.error('刷新数据失败:', error);
                    message.error('刷新数据失败: ' + error.message);
                  })
                  .finally(() => {
                    setLoading(false);
                  });
              }}
            >
              刷新
            </Button> */}
          </Space>
        </div>
      </div>

      {/* Main Content */}
      <Row gutter={16} className="dict-content" style={{ display: "flex", flexWrap: "nowrap", height: "calc(100vh - 180px)", overflow: "hidden" }}>
        {/* Dictionary Types */}
        <Col style={{ width: "300px", flexShrink: 0,height: "100%" }} className="dict-type-col">
          <Card
            className="dict-types"
            style={{ overflow: "visible", position: "relative", width: "300px", height: "100%" }}
          >
            <div className="dict-types-header">
              <Search
                placeholder="搜索字典类型..."
                allowClear
                enterButton="搜索"
                onSearch={(value) => {
                  setLoading(true);
                  dictionaryConfigApi
                    .getDictTypeTree({ content: value })
                    .then((data) => {
                      setDictTypeTreeData(data || []);
                    })
                    .catch((error) => {
                      console.error("搜索字典类型失败:", error);
                    })
                    .finally(() => {
                      setLoading(false);
                    });
                }}
              />
            </div>
            <div
              style={{
                width: "100%",
                height: "100%",  // 使用视口相对高度
                overflow: "auto",
                position: "relative"
              }}
            >
              <Tree
                showIcon
                defaultExpandAll
                selectedKeys={selectedDictType ? [selectedDictType] : []}
                onSelect={(keys) => {
                  if (keys.length > 0) {
                    const typeId = keys[0];
                    setSelectedDictType(typeId);
                    setLoading(true);

                    // 先获取字典类型详情，再获取字典项数据
                    dictionaryConfigApi
                      .getDictTypeById(typeId)
                      .then((typeData) => {
                        if (!typeData || !typeData.typeCode) {
                          message.error("获取字典类型信息失败");
                          setLoading(false);
                          return;
                        }

                        // 保存字典类型详细信息到状态中
                        setSelectedDictTypeInfo(typeData);

                        // 使用typeCode调用接口获取字典项数据
                        return dictionaryConfigApi
                          .getDictItemByTypeCode(typeData.typeCode)
                          .then((data) => {
                            // 确保每个字典项都有一个唯一的key属性
                            const itemsWithKeys = data ? data.map(item => ({
                              ...item,
                              key: item.id || String(Math.random())
                            })) : [];
                            setDictItems(itemsWithKeys);
                          });
                      })
                      .catch((error) => {
                        console.error("获取字典项数据失败:", error);
                      })
                      .finally(() => {
                        setLoading(false);
                      });
                  }
                }}
                treeData={formatTreeData(dictTypeTreeData)}
                
                loading={loading}
                fieldNames={{
                  title: "title",
                  key: "key",
                  children: "children",
                }}
                style={{ 
                  width: "100%", 
                  position: "relative",
                  overflow: "auto",
                  height: dictTypeTreeData.length > 30 ? "calc(100vh - 300px)" : "auto"
                }}
                className="dict-type-tree"
              />
            </div>
          </Card>
        </Col>

        {/* Dictionary Items */}
        <Col style={{ flex: 1,height: "100%" }}>
          <Card
            title={
              <Space>
                字典项列表
                <Button
                  type="primary"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={() => setCreateDictItemModalVisible(true)}
                >
                  添加字典项
                </Button>
              </Space>
            }
            className="dict-items"
            style={{ height: "100%", display: "flex", flexDirection: "column" }}
          >
            <Table
              columns={columns}
              dataSource={dictItems}
              pagination={false}
              scroll={{
                y: dictItems.length > 10 ? 650 : undefined,
              }}
              loading={loading}
              style={{ flex: 1, overflow: "auto" }}
            />

            <Divider />

            {/* <div className="dict-preview">
              <h4>字典项预览:</h4>
              <Space wrap>
                {dictItems.map(item => (
                  <Tag key={item.key} color={item.status === 'enable ' ? '' : 'default'}>
                    {item.name}
                  </Tag>
                ))}
              </Space>
            </div> */}

            <div className="dict-info">
              <h4>字典类型信息:</h4>
              <Row gutter={[16, 8]}>
                <Col span={12}>
                  类型编码: {selectedDictTypeInfo?.typeCode || "无数据"}
                </Col>
                <Col span={12}>
                  类型名称: {selectedDictTypeInfo?.typeName || "无数据"}
                </Col>
                <Col span={12}>
                  作用域:{" "}
                  {selectedDictTypeInfo?.scope === "system"
                    ? "系统级"
                    : selectedDictTypeInfo?.scope === "application"
                    ? "应用级"
                    : selectedDictTypeInfo?.scope === "tenant"
                    ? "租户级"
                    : "无数据"}
                </Col>
                <Col span={12}>
                  描述: {selectedDictTypeInfo?.description || "无数据"}
                </Col>
                <Col span={12}>
                  创建时间: {selectedDictTypeInfo?.createdAt || "无数据"}
                </Col>
                <Col span={12}>
                  最后更新: {selectedDictTypeInfo?.updatedAt || "无数据"}{" "}
                  {selectedDictTypeInfo?.updateBy
                    ? `(${selectedDictTypeInfo.updateBy})`
                    : ""}
                </Col>
              </Row>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Create Dictionary Type Modal */}
      <Modal
        title="添加字典类型"
        open={createModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        width={600}
        centered={true}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateDictType}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="typeCode"
                label="字典类型编码"
                rules={[
                  { required: true, message: "请输入字典类型编码" },
                  {
                    pattern: /^[^\u4e00-\u9fa5]+$/,
                    message: "字典类型编码不能包含中文字符"
                  }
                ]}
              >
                <Input placeholder="请输入字典类型编码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="typeName"
                label="字典类型名称"
                rules={[{ required: true, message: "请输入字典类型名称" }]}
              >
                <Input placeholder="请输入字典类型名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="字典类型状态"
                initialValue="enable"
                rules={[{ required: true, message: "请选择字典类型状态" }]}
              >
                <Radio.Group>
                  <Radio value="enable">启用</Radio>
                  <Radio value="disabled">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="scope"
                label="字典类型作用域"
                rules={[{ required: true, message: "请选择作用域" }]}
                initialValue="system"
              >
                <Radio.Group>
                  <Radio value="system">系统级</Radio>
                  <Radio value="application">应用级</Radio>
                  <Radio value="tenant">租户级</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="editable" label="是否可以编辑" initialValue={1}>
                <Radio.Group>
                  <Radio value={0}>不可编辑</Radio>
                  <Radio value={1}>可编辑</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="parentId" label="父级类型">
                <TreeSelect
                  placeholder="请选择父级类型"
                  treeData={dictTypeTreeData.map((item) => ({
                    title: item.typeName || item.name || "未命名",
                    value:
                      item.id ||
                      item.typeCode ||
                      item.code ||
                      String(Math.random()),
                    children: item.childrenList
                      ? item.childrenList.map((child) => ({
                          title: child.typeName || child.name || "未命名",
                          value:
                            child.id ||
                            child.typeCode ||
                            child.code ||
                            String(Math.random()),
                          children: child.childrenList
                            ? child.childrenList.map((subChild) => ({
                                title:
                                  subChild.typeName ||
                                  subChild.name ||
                                  "未命名",
                                value:
                                  subChild.id ||
                                  subChild.typeCode ||
                                  subChild.code ||
                                  String(Math.random()),
                              }))
                            : undefined,
                        }))
                      : undefined,
                  }))}
                  allowClear
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="description" label="字典类型描述">
                <Input.TextArea rows={2} placeholder="请输入字典类型描述" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="remark" label="备注">
                <Input.TextArea rows={2} placeholder="请输入备注" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* Create Dictionary Item Modal */}
      <Modal
        title="添加字典项"
        open={createDictItemModalVisible}
        onOk={() => dictItemForm.submit()}
        onCancel={() => {
          setCreateDictItemModalVisible(false);
          dictItemForm.resetFields();
        }}
        width={600}
        centered={true}
      >
        <Form
          form={dictItemForm}
          layout="vertical"
          onFinish={(values) => {
            if (!selectedDictType) {
              message.error("请先选择字典类型");
              return;
            }

            // 获取字典类型信息
            setLoading(true);
            let typeData; // 声明在外部以便在Promise链中访问
            dictionaryConfigApi
              .getDictTypeById(selectedDictType)
              .then((data) => {
                if (!data || !data.typeCode) {
                  message.error("获取字典类型信息失败");
                  return;
                }

                typeData = data; // 保存到外部变量

                // 构建添加字典项的数据
                const dictItemData = {
                  dictTypeCode: typeData.typeCode,
                  itemCode: values.itemCode,
                  itemName: values.itemName,
                  itemValue: values.itemValue,
                  description: values.description,
                  status: values.status,
                  version: values.version,
                };

                // 调用添加字典项接口
                return dictionaryConfigApi.addDictItem(dictItemData);
              })
              .then((result) => {
                if (result.code === 200) {
                  message.success("字典项添加成功！");
                  setCreateDictItemModalVisible(false);
                  dictItemForm.resetFields();

                  // 重新加载字典项列表 - 使用保存的typeData
                  if (typeData && typeData.typeCode) {
                    return dictionaryConfigApi.getDictItemByTypeCode(
                      typeData.typeCode
                    );
                  }
                  return null;
                }
              })
              .then((itemData) => {
                if (itemData) {
                  // 确保每个字典项都有一个唯一的key属性
                  const itemsWithKeys = itemData.map(item => ({
                    ...item,
                    key: item.id || String(Math.random())
                  }));
                  setDictItems(itemsWithKeys);
                }
              })
              .catch((error) => {
                console.error("添加字典项失败:", error);
              })
              .finally(() => {
                setLoading(false);
              });
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="itemCode"
                label="字典编码"
                rules={[
                  { required: true, message: "请输入字典编码" },
                  {
                    pattern: /^[^\u4e00-\u9fa5]+$/,
                    message: "字典编码不能包含中文字符"
                  }
                ]}
              >
                <Input placeholder="请输入字典编码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="itemName"
                label="字典名称"
                rules={[{ required: true, message: "请输入字典名称" }]}
              >
                <Input placeholder="请输入字典名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="itemValue"
                label="字典项"
                rules={[{ required: true, message: "请输入字典项" }]}
              >
                <Input placeholder="请输入字典项" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="status" label="字典状态" initialValue="enable">
                <Radio.Group>
                  <Radio value="enable">启用</Radio>
                  <Radio value="disabled">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="description" label="字典描述">
                <Input.TextArea rows={2} placeholder="请输入字典描述" />
              </Form.Item>
            </Col>
            {/* <Col span={12}>
              <Form.Item name='version' label='版本号'>
                <Input placeholder='请输入版本号' />
              </Form.Item>
            </Col> */}
          </Row>
        </Form>
      </Modal>

      {/* Edit Dictionary Item Modal */}
      <Modal
        title="编辑字典项"
        open={editDictItemModalVisible}
        onOk={() => editDictItemForm.submit()}
        onCancel={() => {
          setEditDictItemModalVisible(false);
          editDictItemForm.resetFields();
        }}
        width={600}
        centered={true}
      >
        <Form
          form={editDictItemForm}
          layout="vertical"
          onFinish={(values) => {
            // 编辑字典项的提交逻辑

            if (!selectedDictType) {
              message.error("字典类型ID不存在");
              return;
            }

            // 获取字典类型信息
            setLoading(true);
            let typeData; // 声明在外部以便在Promise链中访问
            dictionaryConfigApi
              .getDictTypeById(selectedDictType)
              .then((data) => {
                if (!data || !data.typeCode) {
                  message.error("获取字典类型信息失败");
                  return;
                }

                typeData = data; // 保存到外部变量

                // 构建更新字典项的数据
                const updateData = {
                  dictTypeCode: typeData.typeCode,
                  itemCode: values.itemCode,
                  itemName: values.itemName,
                  itemValue: values.itemValue,
                  description: values.description,
                  status: values.status,
                  version: values.version,
                  id: currentDictItem.id,
                };

                // 调用更新字典项接口
                return dictionaryConfigApi.updateDictItem(updateData);
              })
              .then((result) => {
                if (result.code == 200) {
                  message.success("字典项更新成功！");
                  setEditDictItemModalVisible(false);
                  editDictItemForm.resetFields();

                  // 重新加载字典项列表 - 使用保存的typeData
                  if (typeData && typeData.typeCode) {
                    return dictionaryConfigApi.getDictItemByTypeCode(
                      typeData.typeCode
                    );
                  }
                  return null;
                }
                return null;
              })
              .then((itemData) => {
                if (itemData) {
                  // 确保每个字典项都有一个唯一的key属性
                  const itemsWithKeys = itemData.map(item => ({
                    ...item,
                    key: item.id || String(Math.random())
                  }));
                  setDictItems(itemsWithKeys);
                }
              })
              .catch((error) => {
                console.error("更新字典项失败:", error);
              })
              .finally(() => {
                setLoading(false);
              });
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="itemCode"
                label="字典编码"
                rules={[
                  { required: true, message: "请输入字典编码" },
                  {
                    pattern: /^[^\u4e00-\u9fa5]+$/,
                    message: "字典编码不能包含中文字符"
                  }
                ]}
              >
                <Input placeholder="请输入字典编码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="itemName"
                label="字典名称"
                rules={[{ required: true, message: "请输入字典名称" }]}
              >
                <Input placeholder="请输入字典名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="itemValue"
                label="字典项"
                rules={[{ required: true, message: "请输入字典项" }]}
              >
                <Input placeholder="请输入字典项" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="status" label="字典状态">
                <Radio.Group>
                  <Radio value="enable">启用</Radio>
                  <Radio value="disabled">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="description" label="字典描述">
                <Input.TextArea rows={2} placeholder="请输入字典描述" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* Edit Dictionary Type Modal */}
      <Modal
        title="编辑字典类型"
        open={editDictTypeModalVisible}
        onOk={() => editDictTypeForm.submit()}
        onCancel={() => {
          setEditDictTypeModalVisible(false);
          editDictTypeForm.resetFields();
        }}
        width={600}
        centered={true}
      >
        <Form
          form={editDictTypeForm}
          layout="vertical"
          onFinish={(values) => {
            // 编辑字典类型的提交逻辑
            if (!currentDictType || !currentDictType.id) {
              message.error("字典类型ID不存在");
              return;
            }

            // 构建更新数据
            const updateData = {
              ...values,
              id: currentDictType.id,
            };

            // 调用更新字典类型接口
            setLoading(true);
            dictionaryConfigApi
              .updateDictType(updateData)
              .then((result) => {
                if (result.code === 200) {
                  message.success("字典类型更新成功！");
                  setEditDictTypeModalVisible(false);
                  editDictTypeForm.resetFields();

                  // 重新获取字典类型树
                  return dictionaryConfigApi.getDictTypeTree();
                }
                return null;
              })
              .then((data) => {
                if (data) {
                  // 更新树数据状态
                  setDictTypeTreeData(data);
                }
              })
              .catch((error) => {
                console.error("更新字典类型失败:", error);
              })
              .finally(() => {
                setLoading(false);
              });
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="typeCode"
                label="字典类型编码"
                rules={[
                  { required: true, message: "请输入字典类型编码" },
                  {
                    pattern: /^[^\u4e00-\u9fa5]+$/,
                    message: "字典类型编码不能包含中文字符"
                  }
                ]}
              >
                <Input placeholder="请输入字典类型编码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="typeName"
                label="字典类型名称"
                rules={[{ required: true, message: "请输入字典类型名称" }]}
              >
                <Input placeholder="请输入字典类型名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="status" label="字典类型状态" rules={[{ required: true, message: "请选择字典类型状态" }]}>
                <Radio.Group>
                  <Radio value="enable">启用</Radio>
                  <Radio value="disabled">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="scope"
                label="字典类型作用域"
                rules={[{ required: true, message: "请选择作用域" }]}
              >
                <Radio.Group>
                  <Radio value="system">系统级</Radio>
                  <Radio value="application">应用级</Radio>
                  <Radio value="tenant">租户级</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="editable" label="是否可以编辑">
                <Radio.Group >
                  <Radio value={0}>不可编辑</Radio>
                  <Radio value={1}>可编辑</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="parentId" label="父级类型">
                <TreeSelect
                  placeholder="请选择父级类型"
                  treeData={dictTypeTreeData.map((item) => ({
                    title: item.typeName || item.name || "未命名",
                    value:
                      item.id ||
                      item.typeCode ||
                      item.code ||
                      String(Math.random()),
                    children: item.childrenList
                      ? item.childrenList.map((child) => ({
                          title: child.typeName || child.name || "未命名",
                          value:
                            child.id ||
                            child.typeCode ||
                            child.code ||
                            String(Math.random()),
                          children: child.childrenList
                            ? child.childrenList.map((subChild) => ({
                                title:
                                  subChild.typeName ||
                                  subChild.name ||
                                  "未命名",
                                value:
                                  subChild.id ||
                                  subChild.typeCode ||
                                  subChild.code ||
                                  String(Math.random()),
                              }))
                            : undefined,
                        }))
                      : undefined,
                  }))}
                  allowClear
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="description" label="字典类型描述">
                <Input.TextArea rows={2} placeholder="请输入字典类型描述" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="remark" label="备注">
                <Input.TextArea rows={2} placeholder="请输入备注" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* Import Modal */}
      <Modal
        title="导入字典项"
        open={importModalVisible}
        onOk={() => {
          if (!uploadFile) {
            message.error("请选择要导入的文件");
            return;
          }

          setLoading(true);
          // 直接使用文件对象
          dictionaryConfigApi
            .importDictItem(uploadFile)
            .then((result) => {
              if (result) {
                const failRowInfo = result.data.failRowInfo;
                const successRowInfo = result.data.successRowInfo;
                if (successRowInfo.length) {
                  message.success(successRowInfo.join(","));
                }

                if (failRowInfo.length) {
                  message.error(failRowInfo.join(","), 5);
                }
                setImportModalVisible(false);
                setUploadFile(null);

                // 刷新数据
                if (selectedDictType) {
                  return dictionaryConfigApi
                    .getDictTypeById(selectedDictType)
                    .then((typeData) => {
                      if (typeData && typeData.typeCode) {
                        return dictionaryConfigApi.getDictItemByTypeCode(
                          typeData.typeCode
                        );
                      }
                      return null;
                    });
                }
                return null;
              }
            })
            .then((itemData) => {
              if (itemData) {
                // 确保每个字典项都有一个唯一的key属性
                const itemsWithKeys = itemData.map(item => ({
                  ...item,
                  key: item.id || String(Math.random())
                }));
                setDictItems(itemsWithKeys);
              }
            })
            .catch((error) => {
              console.error("导入失败:", error);
            })
            .finally(() => {
              setLoading(false);
            });
        }}
        onCancel={() => {
          setImportModalVisible(false);
          setUploadFile(null);
        }}
        width={400}
        centered={true}
      >
        <Upload
          beforeUpload={() => false}
          onChange={(info) => setUploadFile(info.file)}
          maxCount={1}
          className="upload-area"
        >
          <div
            style={{
              border: "1px dashed #d9d9d9",
              borderRadius: "8px",
              padding: "20px",
              textAlign: "center",
              cursor: "pointer",
            }}
          >
            <p
              style={{
                fontSize: "16px",
                marginBottom: "8px",
              }}
            >
              点击或拖拽文件到此区域上传
            </p>
            <p
              style={{
                color: "#888",
                fontSize: "14px",
                margin: 0,
              }}
            >
              支持单个或批量上传，严禁上传公司数据或其他违禁文件
            </p>
          </div>
        </Upload>
      </Modal>
    </div>
  );
};

export default DictionaryConfig;
