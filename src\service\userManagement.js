import { API_PREFIX } from "./constant"
import { getLoginEntry } from '../utils/loginLogger';
import { message } from 'antd';

// 通用错误处理函数
const handleApiError = (response) => {
  return response.json().then(data => {
    if (response.status === 401) {
      message.error(data?.message);
      setTimeout(() => {
        window.location.href = '/login'
      }, 500);
    } else if (response.status === 400 || response.status === 404  || response.status === 500) {
      message.error(data?.message);
    } else if (response.status === 403) {
      // Handle forbidden
    }
    return Promise.reject(data);
  });
};

// 检查响应数据是否包含错误码
const checkResponseData = (data) => {
  if (data && data.code && data.code !== 200) {
    message.error(data.message || '操作失败');
    return Promise.reject(data);
  }
  return data;
};

// 用户管理相关接口
export const usrManagementApi = {
  // 获取当前登录用户信息
  getLoginUserInfo: () => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/loginUser/info`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 文件上传接口
  uploadFile: (file) => {
    const token = getLoginEntry();
    const formData = new FormData();
    formData.append('file', file);
    return fetch(`${API_PREFIX["user-service"]}/file/upload`, {
      method: 'POST',
      headers: {
        'Authorization': token
      },
      body: formData
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 获取用户统计数据
  getUserStatistics: () => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/statistics`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },
  // 获取用户分页列表
  getUserPage: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/page`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 创建用户
  createUser: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 获取用户详情
  getUserDetail: (userId) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 更新用户信息
  updateUser: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 重置用户密码
  resetPassword: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/reset`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 锁定用户
  lockUsers: (userIds) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/lock`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify({ ids: userIds })
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 解锁用户
  unlockUsers: (userIds) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/unlock`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify({ ids: userIds })
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 删除用户
  deleteUsers: (userIds) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify({ ids: userIds })
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 导出用户
  exportUsers: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/export`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if (response.status === 200) {
        return response.blob();
      }
      return handleApiError(response);
    });
  },

  // 导入用户
  importUsers: (file) => {
    const token = getLoginEntry();
    const formData = new FormData();
    formData.append('file', file);
    return fetch(`${API_PREFIX["user-service"]}/user/import`, {
      method: 'POST',
      headers: {
        'Authorization': token
      },
      body: formData
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 下载导入模版
  downloadImportTemplate: () => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/export/template`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if (response.status === 200) {
        return response.blob();
      }
      return handleApiError(response);
    });
  },

  // 获取租户下拉列表
  getTenantDropdown: (tenantId) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/tenant/dropdown-query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify({ tenantId })
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 获取组织树形数据
  getOrgTree: () => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/org/tree`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 获取角色下拉列表
  getRoleDropdown: () => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/role-permission/role/dropdown`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 获取权限树
  getPermissionTree: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/role-permission/permission/tree`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 获取用户权限列表
  getUserPermissions: (userId) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/permission/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 授予用户权限
  grantUserPermission: (data) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/permission/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(data)
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },

  // 获取预签名URL
  getPresignedUrl: (fileId) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/file/presigned-url/${fileId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if (response.status === 200) {
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    });
  },
};

