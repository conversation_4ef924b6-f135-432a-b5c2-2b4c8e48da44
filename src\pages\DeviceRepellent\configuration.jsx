import { useState, useEffect, useReducer } from 'react'
import {
  Layout,
  Checkbox,
  Form,
  Input,
  Select,
  InputNumber,
  TimePicker,
  Button,
  Card,
  Row,
  Slider,
  Col,
  message,
  Space,
  Switch,
  Modal
} from 'antd'
import './styles.css'
import {
  deviceConfigList,
  andVersionConfig,
  getDeviceConfigByVersion,
  updateConfig
} from './../../service/deviceRepellent'
import dayjs from 'dayjs'
import {RollbackOutlined } from '@ant-design/icons'
import { getAllCustomConfig } from './../../service/strategyConfig'

const { Option } = Select

const Configuration = ({ onCancel, device }) => {
  const [selectData, setSelectData] = useState([])
  const [selectItem, setSelectItem] = useState() // 版本id
  const [customConfig, setCustomConfig] = useState([])
  const [, forceUpdate] = useReducer(x => x + 1, 0)
  const [checked, setChecked] = useState(false)
  const [form] = Form.useForm()

  const onFinish = async values => {
    ;[1, 2, 3, 4, 5, 6].forEach(element => {
      values[`powerMode${element}`] = values.powerMode
      values[`prePower${element}`] = values.prePower
    })

    delete values.powerMode
    delete values.prePower

    const timeFields = [
      'workStartTime',
      'workEndTime',
      'ultrasoundOpenTime',
      'ultrasoundCloseTime',
      'ledOpenTime',
      'ledCloseTime'
    ]

    timeFields.forEach(field => {
      if (values[field]) {
        values[field] = values[field].format('HH:mm')
      }
    })

    const customModelId = values.customModelId
    const isModify = values.isModify || false
    delete values.customModelId
    delete values.isModify
    try {
      await updateConfig(
        selectItem,
        customModelId === '/' ? '0' : customModelId,
        isModify,
        customModelId === '/' ? values : {}
      )
      message.success('操作成功')
      onCancel()
    } catch (error) {
      console.log(error)
      message.error(error.message)
    }
  }

  // 获取版本配置列表
  const fetchDeviceConfigList = async (flag = true) => {
    try {
      const res = await deviceConfigList(device.deviceId)
      let customModelId = null
      const processedData = res.map(item => {
        const title = `模式${item.configVersion}`
        if (item.isApply && flag) {
          setSelectItem(item.id)
          customModelId = item.customModelId
        }

        return {
          label: title,
          value: item.id,
          customModelId: item.customModelId
        }
      })

      setSelectData(processedData)

      if (customModelId) {
        fetchConfigDetail(customModelId)
      }
    } catch (error) {
      console.error(error)
      // message.error('获取配置列表失败');
    }
  }

  // 获取配置详情
  const fetchConfigDetail = async customModelId => {
    try {
      const result = await getDeviceConfigByVersion(customModelId)
      formToValue(result, result.isDeviceAdd ? '/' : customModelId)
    } catch (error) {
      message.error(error.message)
    }
  }

  const formToValue = (data, customModelId) => {
    const timeFields = [
      'workStartTime',
      'workEndTime',
      'ultrasoundOpenTime',
      'ultrasoundCloseTime',
      'ledOpenTime',
      'ledCloseTime'
    ]

    const formData = { ...data, ...(customModelId ? { customModelId } : {}) }

    timeFields.forEach(field => {
      if (formData[field]) {
        formData[field] = dayjs(formData[field], 'HH:mm')
      }
    })

    // 确保 ledPower 和 playFrequency 是数字类型
    if (formData.ledPower !== undefined) {
      formData.ledPower = Number(formData.ledPower)
    }
    if (formData.playFrequency !== undefined) {
      formData.playFrequency = Number(formData.playFrequency)
    }

    form.setFieldsValue(formData)
    // 强制更新以触发 Slider 和 InputNumber 的重新渲染
    forceUpdate()
  }

  // 获取策略模版
  const fetchCustomConfig = async () => {
    try {
      const res = await getAllCustomConfig()
      const result = res.map(item => {
        return {
          label: item.name,
          value: item.id,
          ...item
        }
      })
      setCustomConfig(result)
    } catch (error) {
      console.error(error)
    }
  }

  const handleAddVersion = () => {
    Modal.confirm({
      title: '添加版本',
      content: '您确定确定添加一个版本？',
      onOk: async () => {
        try {
          const res = await andVersionConfig(device.deviceId)
          message.success('添加版本成功')
          await fetchDeviceConfigList(false) // Refresh the list after adding
          setSelectItem(res.id) // 默认选中新增的版本
          fetchConfigDetail(res.customModelId)
        } catch (error) {
          message.error('添加版本失败')
        }
      }
    })
  }

  const versionOnchange = (value, node) => {
    setSelectItem(value)
    fetchConfigDetail(node.customModelId)
  }

  useEffect(() => {
    Promise.all([fetchDeviceConfigList(), fetchCustomConfig()])
  }, [])

  const onFinishFailed = errorInfo => {
    console.error('Form validation failed:', errorInfo)
  }

  const renderChannelFields = channelNumber => {
    return (
      <Card
        title={`超声波通道${channelNumber}设置`}
        key={channelNumber}
        extra={
          channelNumber === 1 ? (
            <Checkbox
              checked={checked}
              onChange={e => {
                setChecked(e.target.checked)
                if (e.target.checked) {
                  const channel1Values = {
                    throttle: form.getFieldValue('throttle1'),
                    frequency: form.getFieldValue('frequency1'),
                    firstFrequency: form.getFieldValue('firstFrequency1'),
                    firstTime: form.getFieldValue('firstTime1'),
                    secondFrequency: form.getFieldValue('secondFrequency1'),
                    secondTime: form.getFieldValue('secondTime1'),
                    thirdFrequency: form.getFieldValue('thirdFrequency1'),
                    thirdTime: form.getFieldValue('thirdTime1'),
                    powerMode: form.getFieldValue('powerMode'),
                    prePower: form.getFieldValue('prePower')
                  }

                  // Set values for channels 2-6
                  for (let i = 2; i <= 6; i++) {
                    form.setFieldsValue({
                      [`throttle${i}`]: channel1Values.throttle,
                      [`frequency${i}`]: channel1Values.frequency,
                      [`firstFrequency${i}`]: channel1Values.firstFrequency,
                      [`firstTime${i}`]: channel1Values.firstTime,
                      [`secondFrequency${i}`]: channel1Values.secondFrequency,
                      [`secondTime${i}`]: channel1Values.secondTime,
                      [`thirdFrequency${i}`]: channel1Values.thirdFrequency,
                      [`thirdTime${i}`]: channel1Values.thirdTime,
                      [`powerMode${i}`]: channel1Values.powerMode,
                      [`prePower${i}`]: channel1Values.prePower
                    })
                  }
                }
              }}
            >
              {'所有超声波通道设置一致'}
            </Checkbox>
          ) : (
            ''
          )
        }
      >
        <Form.Item
          name={`throttle${channelNumber}`}
          label='是否开启'
          valuePropName='checked'
        >
          <Switch disabled={checked && channelNumber !== 1} />
        </Form.Item>

        <Form.Item
          name={`frequency${channelNumber}`}
          label='频率模式'
          rules={[{ required: true, message: '请选择频率模式' }]}
        >
          <Select
            onChange={value => {
              if (value === 1) {
                form.setFieldsValue({
                  [`firstFrequency${channelNumber}`]: undefined,
                  [`firstTime${channelNumber}`]: 30,
                  [`secondFrequency${channelNumber}`]: undefined,
                  [`secondTime${channelNumber}`]: 30,
                  [`thirdFrequency${channelNumber}`]: undefined,
                  [`thirdTime${channelNumber}`]: 30
                })
              } else if (value === 2) {
                form.setFieldsValue({
                  [`secondFrequency${channelNumber}`]: undefined,
                  [`secondTime${channelNumber}`]: 30,
                  [`thirdFrequency${channelNumber}`]: undefined,
                  [`thirdTime${channelNumber}`]: 30
                })
              }
            }}
            style={{ width: '100%' }}
            disabled={checked && channelNumber !== 1}
          >
            <Option value={1}>自动变频模式</Option>
            <Option value={2}>固定频率模式</Option>
            <Option value={3}>固定跳频模式</Option>
          </Select>
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return (
              prev[`frequency${channelNumber}`] !==
              next[`frequency${channelNumber}`]
            )
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`firstFrequency${channelNumber}`}
              label={`频率1`}
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`frequency${channelNumber}`) !== 1,
                  message: '请输入频率1'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                disabled={
                  getFieldValue(`frequency${channelNumber}`) === 1 ||
                  (checked && channelNumber !== 1)
                }
                suffix='KHz'
                min={20}
                max={65}
                step={0.1}
                placeholder='请输入20~65'
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return (
              prev[`frequency${channelNumber}`] !==
              next[`frequency${channelNumber}`]
            )
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`firstTime${channelNumber}`}
              label='持续时长'
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`frequency${channelNumber}`) !== 1,
                  message: '请输入持续时长'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                suffix='s'
                min={30}
                max={300}
                placeholder='请输入30s~300s'
                disabled={
                  getFieldValue(`frequency${channelNumber}`) === 1 ||
                  (checked && channelNumber !== 1)
                }
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return (
              prev[`frequency${channelNumber}`] !==
              next[`frequency${channelNumber}`]
            )
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`secondFrequency${channelNumber}`}
              label='频率2'
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`frequency${channelNumber}`) === 3,
                  message: '请输入频率2'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                suffix='KHz'
                min={20}
                max={65}
                step={0.1}
                placeholder='请输入20~65'
                disabled={
                  getFieldValue(`frequency${channelNumber}`) !== 3 ||
                  (checked && channelNumber !== 1)
                }
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return (
              prev[`frequency${channelNumber}`] !==
              next[`frequency${channelNumber}`]
            )
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`secondTime${channelNumber}`}
              label='持续时长'
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`frequency${channelNumber}`) === 3,
                  message: '请输入持续时长'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                suffix='s'
                min={30}
                max={300}
                placeholder='请输入30s~300s'
                disabled={
                  getFieldValue(`frequency${channelNumber}`) !== 3 ||
                  (checked && channelNumber !== 1)
                }
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return (
              prev[`frequency${channelNumber}`] !==
              next[`frequency${channelNumber}`]
            )
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`thirdFrequency${channelNumber}`}
              label='频率3'
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`frequency${channelNumber}`) === 3,
                  message: '请输入频率3'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                suffix='KHz'
                min={20}
                max={65}
                step={0.1}
                placeholder='请输入20~65'
                disabled={
                  getFieldValue(`frequency${channelNumber}`) !== 3 ||
                  (checked && channelNumber !== 1)
                }
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return (
              prev[`frequency${channelNumber}`] !==
              next[`frequency${channelNumber}`]
            )
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`thirdTime${channelNumber}`}
              label='持续时长'
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`frequency${channelNumber}`) === 3,
                  message: '请输入持续时长'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                suffix='s'
                min={30}
                max={300}
                placeholder='请输入30s~300s'
                disabled={
                  getFieldValue(`frequency${channelNumber}`) !== 3 ||
                  (checked && channelNumber !== 1)
                }
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item
          name={`powerMode`}
          label='强度模式'
          rules={[{ required: true, message: '请选择强度模式' }]}
        >
          <Select
            onChange={value => {
              if (value === 1) {
                form.setFieldsValue({
                  [`prePower`]: undefined
                })
              }
            }}
            style={{ width: '100%' }}
            disabled={checked && channelNumber !== 1}
          >
            <Option value={1}>自动强度模式</Option>
            <Option value={2}>固定强度模式</Option>
          </Select>
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return prev[`powerMode`] !== next[`powerMode`]
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`prePower`}
              label='预设强度'
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`powerMode`) === 2,
                  message: '请输入预设强度'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={10}
                max={100}
                suffix='%'
                placeholder='请输入10~100'
                disabled={
                  getFieldValue(`powerMode`) !== 2 ||
                  (checked && channelNumber !== 1)
                }
              />
            </Form.Item>
          )}
        </Form.Item>
      </Card>
    )
  }

  const renderBasicInfo = () => (
    <Card title='基本信息' style={{ marginBottom: 16 }}>
      <Form.Item name='isModify' hidden label='是否修改'>
        <Input />
      </Form.Item>

      <Row gutter={16}>
        <Col span={6}>
          <Form.Item
            name='customModelId'
            label='策略模版'
            rules={[{ required: true, message: '请选择策略模版' }]}
          >
            <Select
              showSearch
              placeholder='请选择策略模版'
              style={{ width: '100%' }}
              options={customConfig}
              onChange={(value, item) => {
                const formData = { ...item }
                delete formData.label
                delete formData.value
                formToValue(formData)
              }}
              filterOption={(input, option) =>
                (option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            ></Select>
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            name='name'
            label='策略名称'
            rules={[{ required: true, message: '请输入策略名称' }]}
          >
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item name='workStartTime' label='工作开始时间'>
            <TimePicker format='HH:mm' style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item name='workEndTime' label='工作结束时间'>
            <TimePicker format='HH:mm' style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16} style={{ height: 56 }}>
        <Col span={6}>
          <Form.Item
            name='ultrasoundWorkMode'
            label='超声波工作模式'
            required
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <Select
              onChange={() => {
                form.setFieldsValue({
                  ultrasoundDuration: 30,
                  ultrasoundCloseTime: undefined,
                  ultrasoundOpenTime: undefined
                })
              }}
              style={{ width: '100%' }}
            >
              <Option value='always'>持续模式</Option>
              <Option value='trigger'>触发模式</Option>
              <Option value='switch'>开关模式</Option>
              <Option value='unmanned'>无人工作模式</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            shouldUpdate={(prev, next) => {
              return prev.ultrasoundWorkMode !== next.ultrasoundWorkMode
            }}
          >
            {({ getFieldValue }) => (
              <Form.Item
                name='ultrasoundDuration'
                label='持续时间'
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
                rules={[
                  ({ getFieldValue }) => ({
                    required: getFieldValue('ultrasoundWorkMode') === 'trigger',
                    message: '触发模式下请输入持续时间'
                  })
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  suffix='s'
                  min={30}
                  max={300}
                  placeholder='请输入30s~300s'
                  disabled={getFieldValue('ultrasoundWorkMode') !== 'trigger'}
                />
              </Form.Item>
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            shouldUpdate={(prev, next) => {
              return prev.ultrasoundWorkMode !== next.ultrasoundWorkMode
            }}
          >
            {({ getFieldValue }) => (
              <Form.Item
                name='ultrasoundOpenTime'
                label='开始时间'
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
              >
                <TimePicker
                  format='HH:mm'
                  style={{ width: '100%' }}
                  disabled={getFieldValue('ultrasoundWorkMode') !== 'switch'}
                />
              </Form.Item>
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            shouldUpdate={(prev, next) => {
              return prev.ultrasoundWorkMode !== next.ultrasoundWorkMode
            }}
          >
            {({ getFieldValue }) => (
              <Form.Item
                name='ultrasoundCloseTime'
                label='结束时间'
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
              >
                <TimePicker
                  format='HH:mm'
                  style={{ width: '100%' }}
                  disabled={getFieldValue('ultrasoundWorkMode') !== 'switch'}
                />
              </Form.Item>
            )}
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16} style={{ height: 56 }}>
        <Col span={6}>
          <Form.Item name='ledWorkMode' label='灯光工作模式' required>
            <Select
              onChange={() => {
                form.setFieldsValue({
                  ledDuration: 30,
                  ledCloseTime: undefined,
                  ledOpenTime: undefined
                })
              }}
              style={{ width: '100%' }}
            >
              <Option value='always'>持续模式</Option>
              <Option value='trigger'>触发模式</Option>
              <Option value='switch'>开关模式</Option>
              <Option value='unmanned'>无人工作模式</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            shouldUpdate={(prev, next) => {
              return prev.ledWorkMode !== next.ledWorkMode
            }}
          >
            {({ getFieldValue }) => (
              <Form.Item
                name='ledDuration'
                label='持续时间'
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
                rules={[
                  ({ getFieldValue }) => ({
                    required: getFieldValue('ledWorkMode') === 'trigger',
                    message: '触发模式下请输入持续时间'
                  })
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  suffix='s'
                  min={30}
                  max={300}
                  placeholder='请输入30s~300s'
                  disabled={getFieldValue('ledWorkMode') !== 'trigger'}
                />
              </Form.Item>
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            shouldUpdate={(prev, next) => {
              return prev.ledWorkMode !== next.ledWorkMode
            }}
          >
            {({ getFieldValue }) => (
              <Form.Item
                name='ledOpenTime'
                label='开始时间'
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
              >
                <TimePicker
                  format='HH:mm'
                  style={{ width: '100%' }}
                  disabled={getFieldValue('ledWorkMode') !== 'switch'}
                />
              </Form.Item>
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            shouldUpdate={(prev, next) => {
              return prev.ledWorkMode !== next.ledWorkMode
            }}
          >
            {({ getFieldValue }) => (
              <Form.Item
                name='ledCloseTime'
                label='结束时间'
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
              >
                <TimePicker
                  format='HH:mm'
                  style={{ width: '100%' }}
                  disabled={getFieldValue('ledWorkMode') !== 'switch'}
                />
              </Form.Item>
            )}
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item name='soundWorkMode' label='喇叭工作模式' required>
            <Select disabled style={{ width: '100%' }}>
              <Option value='always'>持续模式</Option>
              <Option value='trigger'>触发模式</Option>
              <Option value='switch'>开关模式</Option>
              <Option value='unmanned'>无人工作模式</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
    </Card>
  )

  const renderLEDSettings = () => (
    <Card title='灯光设置' style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name='ledColor'
            label='灯光颜色'
            rules={[{ required: true, message: '请选择灯光颜色' }]}
          >
            <Select style={{ width: '100%' }}>
              <Option value={'1'}>白色</Option>
              <Option value={'2'}>蓝色</Option>
              <Option value={'3'}>蓝白混合</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='ledPower'
            label='灯光强度'
            rules={[{ required: true, message: '请选择灯光强度' }]}
          >
            <div
              style={{
                width: '100%',
                display: 'flex',
                alignItems: 'center',
                gap: '16px'
              }}
            >
              <Slider
                min={0}
                max={100}
                style={{ flex: 1 }}
                tooltip={{ open: true }}
                onChange={value => {
                  form.setFieldsValue({
                    customModelId: '/',
                    isModify: true,
                    ledPower: value
                  })
                  forceUpdate()
                }}
                value={form.getFieldValue('ledPower')}
              />
              <InputNumber
                min={0}
                max={100}
                style={{ width: '50px' }}
                controls={false}
                onChange={value => {
                  form.setFieldsValue({
                    customModelId: '/',
                    isModify: true,
                    ledPower: value
                  })
                  forceUpdate()
                }}
                value={form.getFieldValue('ledPower')}
              />
            </div>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='ledTime'
            label='持续点亮时长'
            rules={[{ required: true, message: '请输入持续点亮时长' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              suffix='s'
              min={0.1}
              max={1}
              step={0.01}
              placeholder='请输入0.1s~1s'
            />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='ledFrequency'
            label='间隔时长'
            rules={[{ required: true, message: '请输入1s~10s' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              suffix='s'
              max={10}
              min={1}
              step={0.1}
              placeholder='请输入1s~10s'
            />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  )

  const renderSpeakerSettings = () => (
    <Card title='可闻声设置' style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name='playFrequency'
            label='播放强度'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            rules={[{ required: true, message: '请选择播放强度' }]}
          >
            <div
              style={{
                width: '100%',
                display: 'flex',
                alignItems: 'center',
                gap: '16px'
              }}
            >
              <Slider
                min={0}
                max={100}
                style={{ flex: 1 }}
                value={form.getFieldValue('playFrequency')}
                onChange={value => {
                  form.setFieldsValue({
                    customModelId: '/',
                    isModify: true,
                    playFrequency: value
                  })
                  forceUpdate()
                }}
                tooltip={{ open: true }}
              />
              <InputNumber
                min={0}
                max={100}
                style={{ width: '50px' }}
                controls={false}
                value={form.getFieldValue('playFrequency')}
                onChange={value => {
                  form.setFieldsValue({
                    customModelId: '/',
                    isModify: true,
                    playFrequency: value
                  })
                  forceUpdate()
                }}
              />
            </div>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='soundSleepTime'
            label='可闻声间隔时间'
            rules={[{ required: true, message: '请输入可闻声间隔时间' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              suffix='s'
              min={30}
              max={600}
              placeholder='请输入30~600'
            />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='soundRepetitions'
            label='重复播放次数'
            rules={[{ required: true, message: '请输入重复播放次数' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              placeholder='请输入>=1'
            />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            name='fileName'
            label='播放文件'
            rules={[{ required: true, message: '请选择播放文件' }]}
          >
            <Input style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  )

  return (
    <Layout className='configuration-container' style={{ minHeight: '100%' }}>
      <div
        style={{
          paddingBottom: 16,
          display: 'flex',
          justifyContent: 'space-between',
          borderBottom: '1px solid #f0f0f0'
        }}
      >
        <Space>
          <span style={{ lineHeight: '40px', fontWeight: 900 }}>{device.deviceName}配置策略-驱鼠器编号({device.deviceNo})</span>
          <Select
            showSearch
            placeholder='请选择策略版本'
            style={{ width: 200 }}
            filterOption={false}
            value={selectItem}
            options={selectData}
            onChange={versionOnchange}
            dropdownRender={menu => <>{menu}</>}
          ></Select>
        </Space>
        <RollbackOutlined
          onClick={onCancel}
          style={{ fontSize: '20px', cursor: 'pointer' }}
        />
      </div>
      <Form
        form={form}
        onFinishFailed={onFinishFailed}
        layout='horizontal'
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        onValuesChange={value => {
          const keys = Object.keys(value)
          console.log(keys)
          console.log(!keys.includes('customModelId'))

          // 只有当变化的字段不是customModelId时才更新customModelId
          if (!keys.includes('customModelId')) {
            form.setFieldsValue({
              customModelId: '/',
              isModify: true
            })
          } else if (!keys.includes('isModify')) {
            // 如果只有customModelId变化，只更新isModify
            form.setFieldValue('isModify', true)
          }

          if (checked) {
            const val = value[keys[0]]
            const tempArray1 = [
              'throttle1',
              'frequency1',
              'firstFrequency1',
              'firstTime1',
              'secondFrequency1',
              'secondTime1',
              'thirdFrequency1',
              'thirdTime1'
            ]
            if (tempArray1.includes(keys[0])) {
              for (let i = 2; i <= 6; i++) {
                form.setFieldsValue({
                  [`${keys[0].slice(0, -1)}${i}`]: val
                })
              }
            }
          }
          // if(value.customModelId)
        }}
        onFinish={onFinish}
        initialValues={{
          ultrasoundDuration: 30,
          ultrasoundWorkMode: 'always',
          ledWorkMode: 'always',
          soundWorkMode: 'trigger',
          ledDuration: 30,
          ledPower: 50,
          ledFrequency: 1,
          ledTime: 1,
          playFrequency: 50,
          soundSleepTime: 30,
          powerMode: 1,
          thirdTime1: 30,
          firstTime1: 30,
          secondTime1: 30,
          thirdTime2: 30,
          firstTime2: 30,
          secondTime2: 30,
          thirdTime3: 30,
          firstTime3: 30,
          secondTime3: 30,
          thirdTime4: 30,
          firstTime4: 30,
          secondTime4: 30,
          thirdTime5: 30,
          firstTime5: 30,
          secondTime5: 30,
          thirdTime6: 30,
          firstTime6: 30,
          secondTime6: 30,
          frequency1: 1,
          frequency2: 1,
          frequency3: 1,
          frequency4: 1,
          frequency5: 1,
          frequency6: 1
        }}
      >
        {renderBasicInfo()}
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(4, 1fr)',
            gap: '16px',
            marginBottom: '16px'
          }}
        >
          <div>{renderChannelFields(1)}</div>
          <div>{renderChannelFields(2)}</div>
          <div>{renderChannelFields(3)}</div>
          <div>{renderChannelFields(4)}</div>
          <div>{renderChannelFields(5)}</div>
          <div>{renderChannelFields(6)}</div>
          <div>{renderLEDSettings()}</div>
          <div>{renderSpeakerSettings()}</div>
        </div>
        <Form.Item
          style={{ marginTop: 16, textAlign: 'right' }}
          wrapperCol={{ span: 24 }}
        >
          <Button onClick={onCancel} style={{ marginRight: 10 }}>
            取消
          </Button>
          <Button type='primary' htmlType='submit'>
            提交
          </Button>
        </Form.Item>
      </Form>
    </Layout>
  )
}

export default Configuration
