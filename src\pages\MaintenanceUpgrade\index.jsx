import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Table,
  Tag,
  Breadcrumb,
  Input,
  Select,
  Statistic,
  Modal,
  Form,
  Radio,
  Tooltip,
  message,
  TreeSelect,
  Drawer,
  Divider,
  Progress,
  Descriptions
} from 'antd'
import {
  PlusOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  CloudUploadOutlined,
  SearchOutlined,
  EyeOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import {
  getUpgradeTaskList,
  getTaskStatistics,
  startTask,
  pauseTask,
  createTaskByLocation,
  getTaskDetail
} from './../../service/maintenanceUpgrade'
import { getFirmwarePackageList } from './../../service/upgradePackages'
import { getDeviceGroupsByLocation } from '../../service/deviceGroups'
import './styles.css'
import { debounce } from 'lodash'
import AdvancedSearch from '../../components/advancedSearch'
import dayjs from 'dayjs'

const { Option } = Select

const MaintenanceUpgrade = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [confirmModalVisible, setConfirmModalVisible] = useState(false)
  const [pauseModalVisible, setPauseModalVisible] = useState(false)
  const [currentTask, setCurrentTask] = useState(null)
  const [locationTreeData, setLocationTreeData] = useState([]) // 位置树数据
  const [form] = Form.useForm()
  const [searchForm] = Form.useForm()
  const [searchParams, setSearchParams] = useState({
    page: 0,
    size: 10,
    priority: '', // 优先级 0 低 1 中 2 高
    status: '' // 状态 0 已创建 1 已暂停 2 执行中 3 已完成  4 失败 5 取消
    // name: "", // 任务名称
    // startTime: [], // 开始时间范围
  })
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState([])
  const [total, setTotal] = useState(0)
  const [statisticsData, setStatisticsData] = useState({})
  const [selectloading, setSelectLoading] = useState(false)
  const [firmwareData, setFirmwareData] = useState([])
  const [searchFirmwareData, setSearchFirmwareData] = useState({
    packageName: '',
    packageType: 'FIRMWARE',
    status: 'APPROVED',
    size: 50,
    page: 0
  }) // 分页查询摄像头
  const [totalPages, setTotalPages] = useState(0)
  const [treeSelect, setTreeSelect] = useState(null)
  const [advancedSearchVisible, setAdvancedSearchVisible] = useState(false) // 高级搜索弹窗显示隐藏
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const [taskDetail, setTaskDetail] = useState(null)

  // Mock data for statistics
  const statistics = [
    {
      title: '执行中',
      value: statisticsData?.executingCount,
      icon: (
        <PlayCircleOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
      )
    },
    // {
    //   title: '计划中',
    //   value: statisticsData?.planningCount,
    //   icon: (
    //     <ClockCircleOutlined style={{ fontSize: '24px', color: '#faad14' }} />
    //   )
    // },
    {
      title: '已完成',
      value: statisticsData?.completedCount,
      icon: (
        <CheckCircleOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
      )
    },
    // {
    //   title: '已失败',
    //   value: statisticsData?.failedCount,
    //   icon: (
    //     <ExclamationCircleOutlined
    //       style={{ fontSize: '24px', color: '#ff4d4f' }}
    //     />
    //   )
    // },
    {
      title: '成功率',
      value: statisticsData?.successRate?.toFixed(2),
      suffix: '%',
      icon: (
        <CloudUploadOutlined
          style={{
            fontSize: '24px',
            color:
              statisticsData?.successRate >= 90
                ? '#52c41a' // green for >= 90%
                : statisticsData?.successRate >= 60
                  ? '#faad14' // orange for 60-90%
                  : '#ff4d4f' // red for < 60%
          }}
        />
      )
    }
  ]

  const handleDetailClick = async (record) => {
    try {
      const taskId = record.id
      const res = await getTaskDetail(taskId)
      setTaskDetail(res)
      setIsDetailModalOpen(true)
    } catch (error) {
      message.error('获取任务详情失败')
    }
  }

  // Table columns configuration
  const columns = [
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName'
    },
    {
      title: '固件名称',
      dataIndex: 'firmwareName',
      key: 'firmwareName'
    },
    {
      title: '总设备数',
      dataIndex: 'totalCount',
      key: 'totalCount'
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: priority => (
        <Tag
          color={priority === 2 ? 'red' : priority === 1 ? 'orange' : 'blue'}
        >
          {priority === 2 ? '高' : priority === 1 ? '中' : '低'}
        </Tag>
      )
    },
    {
      title: '升级情况',
      dataIndex: 'status',
      key: 'status',
      render: status => {
        const statusMap = {
          0: { text: '已创建', color: 'blue' },
          1: { text: '已暂停', color: 'orange' },
          2: { text: '执行中', color: 'green' },
          3: { text: '已完成', color: 'success' }
          // 4: { text: '失败', color: 'error' },
          // 5: { text: '取消', color: 'default' }
        }
        const statusInfo = statusMap[status] || {
          text: '未知',
          color: 'default'
        }
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
      }
    },
    {
      title: '成功数',
      dataIndex: 'successCount',
      key: 'successCount'
    },
    {
      title: '失败数',
      dataIndex: 'failCount',
      key: 'failCount'
    },
    // {
    //   title: '成功率',
    //   dataIndex: 'successRate',
    //   key: 'successRate'
    // },
    {
      title: '进度（%）',
      dataIndex: 'progress',
      key: 'progress'
    },
    {
      title: '升级开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (text) => {
        if (!text) return '';
        if (Array.isArray(text)) {
          const [year, month, day, hour, minute, second] = text;
          return dayjs(new Date(year, month - 1, day, hour, minute, second)).format('YYYY-MM-DD HH:mm:ss');
        }
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
      }
    },
    {
      title: '升级结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      render: (text) => {
        if (!text) return '';
        if (Array.isArray(text)) {
          const [year, month, day, hour, minute, second] = text;
          return dayjs(new Date(year, month - 1, day, hour, minute, second)).format('YYYY-MM-DD HH:mm:ss');
        }
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Tooltip title='详情'>
            <Button
              type="link"
              onClick={() => handleDetailClick(record)}
            >
              <EyeOutlined />
            </Button>
          </Tooltip>
          {/* <Tooltip title='重试'>
            <Button
              type="link"
              onClick={() => handleRetryTask(record)}>
              <ReloadOutlined />
            </Button>
          </Tooltip> */}
        </Space>

      )
    }
  ]

  // 获取任务列表
  const fetchTaskList = async (params = {}) => {
    setLoading(true)
    try {
      const response = await getUpgradeTaskList({
        ...searchParams,
        ...params
      })
      setData(response.content)
      setTotal(response.totalElements)
    } catch (error) {
      message.error(error)
    } finally {
      setLoading(false)
    }
  }

  // 获取任务统计数据
  const fetchTaskStatistics = async () => {
    try {
      const res = await getTaskStatistics()
      setStatisticsData(res)
    } catch (error) {
      console.log(error)
    }
  }

  // 搜索处理
  const handleSearch = values => {
    const params = {
      ...values,
      page: 0, // 重置页码
      size: 10
    }
    setSearchParams(params)
    fetchTaskList(params)
  }

  // 重置处理
  const handleReset = () => {
    searchForm.resetFields()
    const params = {
      page: 0,
      size: 10,
      priority: '',
      status: ''
      // name: "",
      // startTime: [],
    }
    setSearchParams(params)
    fetchTaskList(params)
  }

  // 分页处理
  const handleTableChange = pagination => {
    const params = {
      ...searchParams,
      page: pagination.current - 1,
      size: pagination.pageSize
    }
    setSearchParams(params)
    fetchTaskList(params)
  }

  // 初始化加载
  useEffect(() => {
    fetchTaskStatistics()
    fetchTaskList()
    // 清理函数，在组件卸载时取消请求
    return () => {
      if (window.cancelToken) {
        window.cancelToken() // 取消请求
      }
    }
  }, [])

  const handleCreateTask = async values => {
    delete values.version
    delete values.description
    try {
      await createTaskByLocation({
        ...values,
        groupType: treeSelect.isLocal,
        groupId: values.groupId,
      })
      message.success('升级任务创建成功！')
      setCreateModalVisible(false)
      form.resetFields()
      fetchTaskList()
    } catch (error) {
      console.log(error.message)
    }
  }

  // 启动任务
  const handleStartTask = async task => {
    try {
      await startTask(task.id)
      message.success('任务启动成功')
      fetchTaskList() // 刷新任务列表
    } catch (error) {
      message.error('任务启动失败：' + error.message)
    }
  }

  // 显示确认对话框
  const showConfirmModal = task => {
    setCurrentTask(task)
    setConfirmModalVisible(true)
  }

  // 确认启动任务
  const handleConfirmStart = () => {
    if (currentTask) {
      handleStartTask(currentTask)
      setConfirmModalVisible(false)
      setCurrentTask(null)
    }
  }

  // 重试任务
  const handleRetryTask = async (task) => {
    console.log('task', task)
    try {
      await createTaskByLocation({
        taskName: task.taskName,
        firmwareId: task.firmwareId,
        groupId: task.groupId,
        groupType: task.groupType,
        priority: task.priority
      })
      message.success('任务重新创建成功！')
      fetchTaskList()
    } catch (error) {
      message.error('重新创建任务失败：' + error.message)
    }
  }

  // 暂停任务
  const handlePauseTask = async task => {
    try {
      await pauseTask(task.id)
      message.success('任务暂停成功')
      fetchTaskList() // 刷新任务列表
    } catch (error) {
      message.error('任务暂停失败：' + error.message)
    }
  }

  // 显示暂停确认对话框
  const showPauseModal = task => {
    setCurrentTask(task)
    setPauseModalVisible(true)
  }

  // 确认暂停任务
  const handleConfirmPause = () => {
    if (currentTask) {
      handlePauseTask(currentTask)
      setPauseModalVisible(false)
      setCurrentTask(null)
    }
  }

  const fetchFirmwareList = async (obj = {}) => {
    try {
      const res = await getFirmwarePackageList({
        ...searchFirmwareData,
        ...obj
      })
      setTotalPages(res.totalPages)
      return res.content
    } catch (error) {
      console.error(error)
    }
  }

  // 下拉搜索
  const selectSearch = async value => {
    setSelectLoading(true)
    try {
      const data = await fetchFirmwareList({ packageName: value, page: 0 })
      setFirmwareData(data)
    } catch (error) {
      console.error('Failed to search cameras:', error)
    } finally {
      setSelectLoading(false)
    }
  }

  // 滚动加载
  const loadMore = async e => {
    if (selectloading) return
    const bottom =
      e.target.scrollHeight === e.target.scrollTop + e.target.clientHeight
    if (bottom && totalPages - 1 > searchFirmwareData.page) {
      setSearchFirmwareData({
        ...searchFirmwareData,
        page: searchFirmwareData.page + 1
      })
      setSelectLoading(true)
      const newOptions = await fetchFirmwareList({
        page: searchFirmwareData.page + 1
      })
      setFirmwareData(prevOptions => [...prevOptions, ...newOptions])
      setSelectLoading(false)
    }
  }

  // 获取位置树
  const getLocationTree = async () => {
    try {
      const res = await getDeviceGroupsByLocation(1)
      const transformNode = item => {
        return {
          title: item.groupName || item.description,
          key: item.id || item.locationId,
          value: item.id || item.locationId,
          isLocal: item.isLocal,
          children:
            item.children && item.children.length > 0
              ? item.children.map(child => transformNode(child))
              : item.deviceLocationTreeVos &&
                item.deviceLocationTreeVos.length > 0
                ? item.deviceLocationTreeVos.map(child => transformNode(child))
                : undefined
        }
      }

      const transformedData = res.map(item => transformNode(item))
      setLocationTreeData([
        ...transformedData,
        { title: '未分组', value: '0', isLocal: 0 }
      ])
    } catch (error) {
      console.error('Failed to fetch location tree:', error)
    }
  }

  return (
    <div className='maintenance-upgrade'>
      {/* Filter Bar */}
      <Card className='maintenance-upgrade-filter-bar'>
        <div className='maintenance-upgrade-search'>
          <Form form={searchForm} layout='inline' onFinish={handleSearch}>
            <Form.Item name='status' label='执行状态'>
              <Select style={{ width: 200 }} placeholder='请选择执行状态'>
                <Option value={2}>执行中</Option>
                <Option value={3}>已完成</Option>
              </Select>
            </Form.Item>
            <Form.Item name='priority' label='优先级'>
              <Select style={{ width: 200 }} placeholder='请选择优先级'>
                <Option value={0}>低</Option>
                <Option value={1}>中</Option>
                <Option value={2}>高</Option>
              </Select>
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type='primary' htmlType='submit'>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Form.Item>
          </Form>
          <Space size='middle' className='search-right'>
            {/* <Button
              type='primary'
              icon={<SearchOutlined />}
              onClick={() => setAdvancedSearchVisible(true)}
            >
              高级搜索
            </Button> */}
            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={async () => {
                await getLocationTree()
                const res = await fetchFirmwareList()
                setFirmwareData(res)
                setCreateModalVisible(true)
                form.setFieldsValue({
                  taskName: '升级任务' + (total + 1),
                  firmwareId: res[0]?.packageId,
                  priority: 1,
                  version: res[0]?.version,
                  description: res[0]?.description,
                })
              }}
            >
              创建升级任务
            </Button>
          </Space>
        </div>
      </Card>

      {/* Statistics */}
      <Row gutter={[16, 16]} className='statistics-cards'>
        {statistics.map((stat, index) => (
          <Col span={Math.floor(24 / statistics.length)} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                suffix={stat.suffix}
                prefix={stat.icon}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Task List */}
      <Card className='task-list'>
        <Table
          columns={columns}
          dataSource={data}
          // rowSelection={{
          //   selectedRowKeys,
          //   onChange: setSelectedRowKeys
          // }}
          rowKey={'id'}
          pagination={{
            total,
            pageSize: searchParams.size,
            current: searchParams.page + 1,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: total => `共 ${total} 条记录`
          }}
          onChange={handleTableChange}
          loading={loading}
        />
      </Card>

      {/* Create Task Modal */}
      <Modal
        title='创建升级任务'
        okText='创建'
        open={createModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setCreateModalVisible(false)
          form.resetFields()
        }}
        width={500}
      >
        <Form form={form} layout='vertical' onFinish={handleCreateTask}>
          <Form.Item
            name='taskName'
            label='任务名称'
            rules={[{ required: true }]}
          >
            <Input placeholder='请输入任务名称' />
          </Form.Item>

          <Form.Item
            name='firmwareId'
            label='固件'
            rules={[{ required: true }]}
          >
            <Select
              showSearch
              allowClear
              placeholder='请数固件名称搜索'
              onPopupScroll={loadMore}
              onSelect={(_, item) => {
                form.setFieldsValue({
                  version: item.version,
                  description: item.description,
                })
              }}
              loading={selectloading}
              onSearch={debounce(selectSearch, 300)}
              style={{ width: '100%' }}
              optionFilterProp='children'
              filterOption={false}
              onDropdownVisibleChange={async open => {
                if (open) {
                  const res = await fetchFirmwareList()
                  setFirmwareData(res)
                }
              }}
            >
              {firmwareData?.map(option => (
                <Option
                  key={option.packageId}
                  value={option.packageId}
                  version={option.version}
                  description={option.description}
                >
                  {option.packageName}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name='groupId' label='区域' rules={[{ required: true }]}>
            <TreeSelect
              showSearch
              multiple
              treeData={locationTreeData}
              onSelect={(id, node) => {
                setTreeSelect(node)
              }}
              placeholder='请选择区域'
              treeNodeFilterProp='title'
              treeDefaultExpandAll
              allowClear
              treeNodeLabelProp='title'
            />
          </Form.Item>

          <Form.Item
            name='priority'
            label='优先级'
            rules={[{ required: true }]}
          >
            <Radio.Group>
              <Radio.Button value={0}>低</Radio.Button>
              <Radio.Button value={1}>中</Radio.Button>
              <Radio.Button value={2}>高</Radio.Button>
            </Radio.Group>
          </Form.Item>

          <Form.Item name='version' label='固件版本'>
            <Input disabled />
          </Form.Item>
          <Form.Item name='description' label='升级包描述'>
            <Input.TextArea rows={4} disabled />
          </Form.Item>
          {/* <Form.Item name='description' label='任务说明'>
            <Input.TextArea rows={4} placeholder='请输入任务说明' />
          </Form.Item> */}
        </Form>
      </Modal>

      {/* Start Task Confirmation Modal */}
      <Modal
        title='确认启动任务'
        open={confirmModalVisible}
        onOk={handleConfirmStart}
        onCancel={() => {
          setConfirmModalVisible(false)
          setCurrentTask(null)
        }}
        okText='确定'
        cancelText='取消'
      >
        <p>确定要启动任务 "{currentTask?.taskName}" 吗？</p>
      </Modal>

      {/* Pause Task Confirmation Modal */}
      <Modal
        title='确认暂停任务'
        open={pauseModalVisible}
        onOk={handleConfirmPause}
        onCancel={() => {
          setPauseModalVisible(false)
          setCurrentTask(null)
        }}
        okText='确定'
        cancelText='取消'
      >
        <p>确定要暂停任务 &quot;{currentTask?.taskName}&quot; 吗？</p>
      </Modal>

      {advancedSearchVisible && (
        <AdvancedSearch
          advancedSearchVisible={advancedSearchVisible}
          onCancel={() => {
            setAdvancedSearchVisible(false)
          }}
          data={[
            {
              label: '任务名称',
              value: 'taskName'
            },
            {
              label: '固件名称',
              value: 'firmwareName'
            },
            {
              label: '总设备数',
              value: 'totalCount'
            },
            {
              label: '优先级',
              value: 'priority',
              options: [
                { label: '低', value: 0 },
                { label: '中', value: 1 },
                { label: '高', value: 2 }
              ],
              children: (setInputValue) => (
                <Select
                  placeholder='请选择优先级'
                  style={{ width: '300px' }}
                  onChange={value => {
                    setInputValue(value)
                  }}
                  options={[
                    { label: '低', value: 0 },
                    { label: '中', value: 1 },
                    { label: '高', value: 2 }
                  ]}
                />
              )
            },
            {
              label: '升级情况',
              value: 'status',
              options: [
                { label: '已创建', value: 0 },
                { label: '已暂停', value: 1 },
                { label: '执行中', value: 2 },
                { label: '已完成', value: 3 }
              ],
              children: (setInputValue) => (
                <Select
                  placeholder='请选择升级情况'
                  style={{ width: '300px' }}
                  onChange={value => {
                    setInputValue(value)
                  }}
                  options={[
                    { label: '已创建', value: 0 },
                    { label: '已暂停', value: 1 },
                    { label: '执行中', value: 2 },
                    { label: '已完成', value: 3 }
                  ]}
                />
              )
            },
            {
              label: '成功数',
              value: 'successCount'
            },
            {
              label: '失败数',
              value: 'failCount'
            },
            {
              label: '进度',
              value: 'progress'
            }
          ]}
          getSearchData={data => {
            console.log(data)
            // TODO: 处理高级搜索数据
          }}
        />
      )}
      {/* 任务详情抽屉 */}
      <Drawer
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <EyeOutlined style={{ color: '#1890ff', fontSize: '18px' }} />
            <span style={{ fontSize: '16px', fontWeight: '600' }}>升级任务详情</span>
          </div>
        }
        open={isDetailModalOpen}
        onClose={() => setIsDetailModalOpen(false)}
        width={650}
        placement="right"
        extra={
          <Button
            type="primary"
            onClick={() => setIsDetailModalOpen(false)}
            style={{ borderRadius: '6px' }}
          >
            关闭
          </Button>
        }
        styles={{
          body: {
            padding: '24px',
            backgroundColor: '#f8f9fa'
          }
        }}
      >
        {taskDetail ? (
          <div style={{ backgroundColor: '#fff', borderRadius: '12px', padding: '24px' }}>
            {/* 任务基本信息 */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{
                color: '#262626',
                fontSize: '16px',
                fontWeight: '600',
                marginBottom: '16px',
                borderBottom: '2px solid #1890ff',
                paddingBottom: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <CloudUploadOutlined style={{ color: '#1890ff' }} />
                任务基本信息
              </h3>
              <Descriptions column={1} size="middle" labelStyle={{ fontWeight: '500', color: '#595959', width: '120px' }}>
                <Descriptions.Item label="任务名称">
                  <span style={{ color: '#262626', fontWeight: '500', fontSize: '15px' }}>
                    {taskDetail.taskName}
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="固件名称">
                  <span style={{ color: '#262626' }}>
                    {taskDetail.firmwareName}
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="总设备数">
                  <Tag color="blue" style={{ borderRadius: '4px', fontSize: '14px' }}>
                    {taskDetail.totalCount} 台
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="优先级">
                  <Tag
                    color={taskDetail.priority === 2 ? 'red' : taskDetail.priority === 1 ? 'orange' : 'blue'}
                    style={{ borderRadius: '4px', fontSize: '14px' }}
                  >
                    {taskDetail.priority === 2 ? '🔴 高' : taskDetail.priority === 1 ? '🟡 中' : '🔵 低'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="升级状态">
                  <Tag
                    color={taskDetail.status === 2 ? 'green' : taskDetail.status === 3 ? 'success' : taskDetail.status === 1 ? 'orange' : 'blue'}
                    style={{ borderRadius: '4px', fontSize: '14px' }}
                  >
                    {taskDetail.status === 2 ? '⚡ 执行中' : taskDetail.status === 3 ? '✅ 已完成' : taskDetail.status === 1 ? '⏸️ 已暂停' : '📋 已创建'}
                  </Tag>
                </Descriptions.Item>
              </Descriptions>
            </div>

            <Divider style={{ margin: '24px 0' }} />

            {/* 执行进度 */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{
                color: '#262626',
                fontSize: '16px',
                fontWeight: '600',
                marginBottom: '16px',
                borderBottom: '2px solid #52c41a',
                paddingBottom: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
                执行进度
              </h3>

              {/* 进度条 */}
              <div style={{ marginBottom: '20px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <span style={{ fontWeight: '500', color: '#595959' }}>整体进度</span>
                  <span style={{ fontWeight: '600', color: '#262626' }}>{taskDetail.progress || 0}%</span>
                </div>
                <Progress
                  percent={taskDetail.progress || 0}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                  trailColor="#f0f0f0"
                  strokeWidth={8}
                  style={{ marginBottom: '16px' }}
                />
              </div>

              {/* 统计数据 */}
              <Row gutter={16}>
                <Col span={8}>
                  <div style={{
                    textAlign: 'center',
                    padding: '16px',
                    backgroundColor: '#f6ffed',
                    border: '1px solid #b7eb8f',
                    borderRadius: '8px'
                  }}>
                    <div style={{ fontSize: '24px', fontWeight: '600', color: '#52c41a', marginBottom: '4px' }}>
                      {taskDetail.successCount}
                    </div>
                    <div style={{ color: '#52c41a', fontSize: '14px' }}>成功数</div>
                  </div>
                </Col>
                <Col span={8}>
                  <div style={{
                    textAlign: 'center',
                    padding: '16px',
                    backgroundColor: '#fff2e8',
                    border: '1px solid #ffbb96',
                    borderRadius: '8px'
                  }}>
                    <div style={{ fontSize: '24px', fontWeight: '600', color: '#fa8c16', marginBottom: '4px' }}>
                      {taskDetail.failCount}
                    </div>
                    <div style={{ color: '#fa8c16', fontSize: '14px' }}>失败数</div>
                  </div>
                </Col>
                <Col span={8}>
                  <div style={{
                    textAlign: 'center',
                    padding: '16px',
                    backgroundColor: '#f0f5ff',
                    border: '1px solid #adc6ff',
                    borderRadius: '8px'
                  }}>
                    <div style={{ fontSize: '24px', fontWeight: '600', color: '#1890ff', marginBottom: '4px' }}>
                      {taskDetail.totalCount - taskDetail.successCount - taskDetail.failCount}
                    </div>
                    <div style={{ color: '#1890ff', fontSize: '14px' }}>待处理</div>
                  </div>
                </Col>
              </Row>
            </div>

            <Divider style={{ margin: '24px 0' }} />

            {/* 时间信息 */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{
                color: '#262626',
                fontSize: '16px',
                fontWeight: '600',
                marginBottom: '16px',
                borderBottom: '2px solid #722ed1',
                paddingBottom: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <ClockCircleOutlined style={{ color: '#722ed1' }} />
                时间信息
              </h3>
              <Descriptions column={1} size="middle" labelStyle={{ fontWeight: '500', color: '#595959', width: '120px' }}>
                <Descriptions.Item label="开始时间">
                  <span style={{ color: '#262626' }}>
                    {taskDetail.startTime ? dayjs(taskDetail.startTime).format('YYYY-MM-DD HH:mm:ss') : '未开始'}
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="结束时间">
                  <span style={{ color: '#262626' }}>
                    {taskDetail.endTime ? dayjs(taskDetail.endTime).format('YYYY-MM-DD HH:mm:ss') : '未结束'}
                  </span>
                </Descriptions.Item>
              </Descriptions>
            </div>

            {/* 描述信息 */}
            {taskDetail.description && (
              <>
                <Divider style={{ margin: '24px 0' }} />
                <div>
                  <h3 style={{
                    color: '#262626',
                    fontSize: '16px',
                    fontWeight: '600',
                    marginBottom: '16px',
                    borderBottom: '2px solid #fa8c16',
                    paddingBottom: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}>
                    <ExclamationCircleOutlined style={{ color: '#fa8c16' }} />
                    升级包描述
                  </h3>
                  <div style={{
                    backgroundColor: '#fff7e6',
                    border: '1px solid #ffd591',
                    borderRadius: '8px',
                    padding: '16px',
                    lineHeight: '1.6',
                    color: '#262626'
                  }}>
                    {taskDetail.description}
                  </div>
                </div>
              </>
            )}
          </div>
        ) : (
          <div style={{
            textAlign: 'center',
            padding: '64px 0',
            backgroundColor: '#fff',
            borderRadius: '12px'
          }}>
            <div style={{ fontSize: '16px', color: '#999' }}>加载中...</div>
          </div>
        )}
      </Drawer>
    </div>

  )
}

export default MaintenanceUpgrade
