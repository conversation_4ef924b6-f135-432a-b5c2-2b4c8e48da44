import fetch from './fetch';

import {API_PREFIX} from "./constant"


// 获取位置树 租户id
export const getDeviceGroupsByLocation = (tenantId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/locations/tree/${tenantId}`,
    method: 'GET'
  });
};


// 获取位置详情 位置id
export const getLocationDetail = (locationId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/locations/${locationId}`,
    method: 'GET'
  });
};


// 获取行政分组树 全国
export const getAdministrativeGroupsTree = () => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/device-groups/tree`,
    method: 'GET'
  });
};

// 获取设备位置树
export const getDeviceLocationTree = (groupId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/locations/getLocationAllTree?groupId=${groupId}`,
    method: 'GET'
  });
};

// 获取设备位置
export const addDeviceLocation = (data) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/locations`,
    method: 'post',
    data
  });
};

// 删除设备位置
export const delDeviceLocation = (locationId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/locations/${locationId}`,
    method: 'DELETE'
  });
};

// 更新设备位置
export const updateDeviceLocation = (locationId, data) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/locations/${locationId}`,
    method: 'PUT',
    data
  });
};


// 获取客户分组树
export const getCustomerGroupsTree = () => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/ownerships/getAllOwnership`,
    method: 'GET'
  });
};


// 获取客户详情 客户id
export const getCustomerDetail = (ownershipId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/ownerships/${ownershipId}`,
    method: 'GET'
  });
};

// 删除客户
export const delCustomerGroup = (ownershipId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/ownerships/${ownershipId}`,
    method: 'DELETE'
  });
};

// 更新客户
export const updateCustomerGroup = (ownershipId, data) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/ownerships/${ownershipId}`,
    method: 'PUT',
    data
  });
};

// 创建客户分组
export const createCustomerGroup = (data) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/ownerships`,
    method: 'POST',
    data
  });
}