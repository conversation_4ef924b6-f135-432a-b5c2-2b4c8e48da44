import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Table,
  Breadcrumb,
  Input,
  Select,
  Modal,
  Form,
  Radio,
  Tooltip,
  Statistic,
  message
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  UserOutlined,
  TeamOutlined,
  LockOutlined,
  UnlockOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import './styles.css';
import { tenantManagementApi } from '../../service/tenantManagement';

const { Search } = Input;
const { Option } = Select;

// 初始化空数据
const initialStatisticsData = {
  totalNum: 0,
  monthAddNum: 0,
  activeNum: 0,
  todayLogInNum: 0,
  lockedNum: 0,
  pendingNum: 0,
  adminNum: 0,
  systemNum: 0
};

const TenantList = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    status: 'all',
    accountType: 'all',
    tenantId: 'all',
    content: '',
    tenantCode: '',
    tenantType: 'all'
  });
  const [resetPasswordModalVisible, setResetPasswordModalVisible] = useState(false);
  const [resetPasswordForm] = Form.useForm();
  const [selectedUser, setSelectedUser] = useState(null);
  const [statisticsData, setStatisticsData] = useState(initialStatisticsData);
  const [parentTenants, setParentTenants] = useState([]);

  // 获取统计数据
  const fetchStatistics = async () => {
    // 直接使用模拟数据
    setStatisticsData(initialStatisticsData);
  };

  // 获取租户列表数据
  const fetchUserList = async (params = {}) => {
    setLoading(true);
    try {
      const response = await tenantManagementApi.getTenantPage({
        pageNum: params.pageNum || params.current || 1,
        pageSize: params.pageSize || 10,
        tenantCode: params.tenantCode || '',
        tenantName: params.tenantName || '',
        tenantType: params.tenantType || '',
        status: params.status || ''
      });

      if (response.code === 200) {
        // 转换数据格式
        const tenantList = response.data.content.map(item => ({
          userId: item.id,
          tenantName: item.tenantName,
          tenantType: item.tenantType,
          status: item.status,
          tier: item.tier,
          phone: item.contactInfo?.phone,
          paymentStatus: item.subscription?.paymentStatus,
          createdAt: item.createdAt
        }));

        setUsers(tenantList);
        setPagination({
          ...pagination,
          total: response.data.totalElements,
          current: response.data.number + 1,
          pageSize: response.data.size
        });
      }
    } catch (error) {
      console.error('获取租户列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取上级租户下拉列表
  const fetchParentTenants = async () => {
    try {
      const response = await tenantManagementApi.getTenantDropdown();
      if (response.code === 200) {
        setParentTenants(response.data || []);
      }
    } catch (error) {
      console.error('获取上级租户列表失败:', error);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    fetchUserList();
    fetchStatistics();
    fetchParentTenants();
  }, []);

  // 处理表格变化
  const handleTableChange = (newPagination, filters, sorter) => {
    fetchUserList({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
      ...filters
    });
  };

  // Mock data for statistics
  const statistics = [
    {
      title: '总租户',
      value: statisticsData.totalNum,
      subTitle: '本月新增',
      subValue: statisticsData.monthAddNum,
      icon: <TeamOutlined style={{ fontSize: 24, color: '#1890ff' }} />
    },
    {
      title: '活跃租户',
      value: statisticsData.activeNum,
      subTitle: '今日登录',
      subValue: statisticsData.todayLogInNum,
      icon: <UserOutlined style={{ fontSize: 24, color: '#52c41a' }} />
    },
    {
      title: '异常账户',
      value: statisticsData.lockedNum,
      subTitle: '待验证',
      subValue: statisticsData.pendingNum,
      icon: <LockOutlined style={{ fontSize: 24, color: '#faad14' }} />
    },
    {
      title: '管理员',
      value: statisticsData.adminNum,
      subTitle: '系统租户',
      subValue: statisticsData.systemNum,
      icon: <SettingOutlined style={{ fontSize: 24, color: '#722ed1' }} />
    }
  ];

  // 处理禁用/启用状态切换
  const handleLockStatusChange = async (record) => {
    setLoading(true);
    try {
      const response = record.status === 'disabled'
        ? await tenantManagementApi.enableTenants([record.userId])
        : await tenantManagementApi.disableTenants([record.userId]);

      if (response.code === 200) {
        setLoading(false);
        message.success(record.status === 'disabled' ? '租户已启用' : '租户已禁用');
        // 刷新租户列表
        fetchUserList({
          pageNum: pagination.current,
          pageSize: pagination.pageSize
        });
        setSelectedRowKeys([]);
        // 刷新统计数据
        fetchStatistics();
      }
       else {
        message.error(response.message || (record.status === 'disabled' ? '启用租户失败' : '禁用租户失败'));
      }
    } catch (error) {
      console.error(record.status === 'disabled' ? '启用租户失败:' : '禁用租户失败:', error);
      message.error(record.status === 'disabled' ? '启用租户失败' : '禁用租户失败');
    }finally{
      setLoading(false)
    }
  };

  // 处理编辑按钮点击
  const handleEdit = async (record) => {
    if (record.status === 'disabled') {
      message.warning('禁用状态的租户不能编辑');
      return;
    }
    try {
      const response = await tenantManagementApi.getTenantDetail(record.userId);
      if (response.code === 200) {
        const tenantData = response.data;
        // 设置表单数据
        editForm.setFieldsValue({
          ...tenantData,
          deviceLimit: tenantData.quotaLimits?.deviceLimit,
          paymentStatus: tenantData.subscription?.paymentStatus,
          billingCycle: tenantData.subscription?.billingCycle,
          autoRenew: tenantData.subscription?.autoRenew,
          phone: tenantData.contactInfo?.phone,
          email: tenantData.contactInfo?.email,
          birthDate: tenantData.birthDate ? dayjs(tenantData.birthDate) : null
        });
        setEditModalVisible(true);
        setCurrentUserId(tenantData.id);
      }
    } catch (error) {
      console.error('获取租户详情失败:', error);
    }
  };

  // 处理编辑表单提交
  const handleEditUser = async (values) => {
    try {
      // 构造请求参数
      const params = {
        tenantCode: values.tenantCode,
        tenantName: values.tenantName,
        tenantType: values.tenantType,
        status: values.status,
        tier: values.tier,
        industry: values.industry,
        contactInfo: {
          primaryContact: "",
          phone: values.phone,
          email: values.email,
          address: ""
        },
        subscription: {
          planId: "",
          startDate: "",
          endDate: "",
          autoRenew: values.autoRenew,
          paymentStatus: values.paymentStatus,
          billingCycle: values.billingCycle
        },
        quotaLimits: {
          deviceLimit: values.deviceLimit || 0,
          userLimit: 0,
          apiCallLimit: 0,
          storageLimit: 0,
          dataRetentionDays: 0
        },
        customization: {
          logoUrl: "",
          primaryColor: "",
          secondaryColor: "",
          welcomeMessage: ""
        },
        parentTenantId: values.parentTenantId,
        domainName: "",
        defaultLanguage: "",
        timezone: "",
        trialExpiryDate: "",
        isTrial: values.isTrial,
        isSystem: 0,
        notes: "",
        id: currentUserId
      };

      const response = await tenantManagementApi.updateTenant(params);

      if (response.code === 200) {
        message.success('租户信息更新成功！');
        setEditModalVisible(false);
        setCurrentUserId(null);
        editForm.resetFields();
        // 刷新租户列表
        fetchUserList({
          pageNum: pagination.current,
          pageSize: pagination.pageSize
        });
        // 刷新统计数据
        fetchStatistics();
      }
    } catch (error) {
      console.error('更新租户信息失败:', error);
    }
  };

  // 处理重置密码
  const handleResetPassword = async (values) => {
    try {
      const params = {
        userId: selectedUser.userId,
        password: values.password
      };

      const response = await tenantManagementApi.resetPassword(params);

      if (response.code === 200) {
        message.success('密码重置成功！');
        setResetPasswordModalVisible(false);
        resetPasswordForm.resetFields();
      }
    } catch (error) {
      console.error('密码重置失败:', error);
    }
  };

  // 打开重置密码弹窗
  const showResetPasswordModal = async (record) => {
    if (record.status === 'disabled') {
      message.warning('禁用状态的租户不能重置密码');
      return;
    }
    try {
      const response = await tenantManagementApi.getUserDetail(record.userId);
      if (response.code === 200) {
        const userData = response.data;
        setSelectedUser(record);
        resetPasswordForm.setFieldsValue({
          username: userData.username
        });
        setResetPasswordModalVisible(true);
      }
    } catch (error) {
      console.error('获取租户信息失败:', error);
    }
  };

  // 处理删除租户
  const handleDeleteUser = async (userId) => {
    // 检查租户是否处于禁用状态
    const user = users.find(u => u.userId === userId);
    if (user && user.status === 'disabled') {
      message.warning('禁用状态的租户不能删除');
      return;
    }

    setLoading(true);

    try {
      // 调用API删除租户
      const response = await tenantManagementApi.deleteTenants([userId]);
      
      if (response.code === 200) {
        // 刷新租户列表
        fetchUserList({
          pageNum: pagination.current,
          pageSize: pagination.pageSize
        });
        // 刷新统计数据
        fetchStatistics();
        setSelectedRowKeys([]);
        message.success('租户删除成功');
      }
    } catch (error) {
      console.error('删除租户失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // Table columns
  const columns = [
    {
      title: '租户名称',
      dataIndex: 'tenantName',
      key: 'tenantName',
      align: 'center',
      render: (text) => <span>{text || '-'}</span>
    },
    {
      title: '租户类型',
      dataIndex: 'tenantType',
      key: 'tenantType',
      align: 'center',
      render: (text) => {
        const typeMap = {
          enterprise: '企业',
          government: '政府',
          education: '教育',
          medical: '医疗',
          other: '其他'
        };
        return <span>{text ? typeMap[text] || text : '-'}</span>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (text) => {
        const statusMap = {
          active: '活跃',
          suspended: '暂停',
          disabled: '禁用'
        };
        return <span>{text ? statusMap[text] || text : '-'}</span>;
      }
    },
    {
      title: '服务等级',
      dataIndex: 'tier',
      key: 'tier',
      align: 'center',
      render: (text) => {
        const tierMap = {
          basic: '基础版',
          standard: '标准版',
          professional: '专业版',
          enterprise: '企业版'
        };
        return <span>{text && text !== 'unknown' ? tierMap[text] || text : '-'}</span>;
      }
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone',
      align: 'center',
      render: (text) => (
        <span>{text && text !== 'unknown' ? text : '-'}</span>
      )
    },
    {
      title: '支付状态',
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      align: 'center',
      render: (text) => {
        const paymentStatusMap = {
          paid: '已支付',
          unpaid: '未支付'
        };
        return <span>{text ? paymentStatusMap[text] || text : '-'}</span>;
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      align: 'center',
      render: (text) => <span>{text || '-'}</span>
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Tooltip title={record.status === 'disabled' ? "禁用状态不能编辑" : "编辑"}>
            <Button type="text" icon={<EditOutlined />} onClick={() => handleEdit(record)} disabled={record.status === 'disabled'} />
          </Tooltip>
          {record.status === 'disabled' ? (
            <Tooltip title="启用">
              <Button type="text" icon={<LockOutlined />} onClick={() => handleLockStatusChange(record)} />
            </Tooltip>
          ) : (
            <Tooltip title="禁用">
              <Button type="text" icon={<UnlockOutlined />} onClick={() => handleLockStatusChange(record)} />
            </Tooltip>
          )}
          <Tooltip title={record.status === 'disabled' ? "禁用状态不能删除" : "删除"}>
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              disabled={record.status === 'disabled'}
              onClick={() => {
                Modal.confirm({
                  title: '删除确认',
                  content: `确定要删除租户 "${record.tenantName}" 吗？`,
                  okText: '确定',
                  cancelText: '取消',
                  centered: true,
                  onOk: () => handleDeleteUser(record.userId)
                });
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  const handleCreateUser = async (values) => {
    setLoading(true);

    try {
      // 构造请求参数
      const params = {
        tenantCode: values.tenantCode,
        tenantName: values.tenantName,
        tenantType: values.tenantType,
        status: values.status,
        tier: values.tier,
        industry: values.industry,
        contactInfo: {
          primaryContact: "",
          phone: values.phone,
          email: values.email,
          address: ""
        },
        subscription: {
          planId: "",
          startDate: "",
          endDate: "",
          autoRenew: values.autoRenew,
          paymentStatus: values.paymentStatus,
          billingCycle: values.billingCycle
        },
        quotaLimits: {
          deviceLimit: values.deviceLimit || 0,
          userLimit: 0,
          apiCallLimit: 0,
          storageLimit: 0,
          dataRetentionDays: 0
        },
        customization: {
          logoUrl: "",
          primaryColor: "",
          secondaryColor: "",
          welcomeMessage: ""
        },
        parentTenantId: values.parentTenantId,
        domainName: "",
        defaultLanguage: "",
        timezone: "",
        trialExpiryDate: "",
        isTrial: values.isTrial,
        isSystem: 0,
        notes: ""
      };

      // 调用API添加租户
      const response = await tenantManagementApi.addTenant(params);

      if (response.code === 200) {
        message.success('租户创建成功！');
        setCreateModalVisible(false);
        form.resetFields();

        // 刷新租户列表
        fetchUserList({
          pageNum: pagination.current,
          pageSize: pagination.pageSize
        });

        // 更新统计数据
        setStatisticsData(prev => ({
          ...prev,
          totalNum: prev.totalNum + 1,
          monthAddNum: prev.monthAddNum + 1,
          activeNum: values.status === 'active' ? prev.activeNum + 1 : prev.activeNum
        }));
      }
    } catch (error) {
      console.error('租户创建失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理刷新按钮点击
  const handleRefresh = () => {
    // 清空所有筛选条件
    setFilters({
      status: 'all',
      accountType: 'all',
      tenantId: 'all',
      content: '',
      tenantCode: '',
      tenantType: 'all'
    });

    // 调用API获取租户列表
    fetchUserList({
      current: 1,
      pageSize: pagination.pageSize
    });
  };

  // 处理搜索按钮点击
  const handleSearch = () => {
    // 执行搜索
    fetchUserList({
      pageNum: 1,
      pageSize: pagination.pageSize,
      tenantCode: filters.tenantCode,
      tenantName: filters.content,
      tenantType: filters.tenantType === 'all' ? '' : filters.tenantType,
      status: filters.status === 'all' ? '' : filters.status
    });
  };


  return (
    <div className="user-management">

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} className="statistics-cards" style={{ display: 'none' }}>
        {statistics.map((stat, index) => (
          <Col span={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
              />
              <div className="sub-statistic">
                <span>{stat.subTitle}: </span>
                <span className="sub-value">{stat.subValue}</span>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Filter Bar */}
      <Card className="filter-bar">
        <Space wrap>
          <label>租户类型:</label>
          <Select
            placeholder="租户类型"
            style={{ width: 150 }}
            value={filters.tenantType || 'all'}
            onChange={(value) => setFilters(prev => ({ ...prev, tenantType: value }))}
          >
            <Option value="all">全部类型</Option>
            <Option value="enterprise">企业</Option>
            <Option value="government">政府</Option>
            <Option value="education">教育</Option>
            <Option value="medical">医疗</Option>
            <Option value="other">其他</Option>
          </Select>

          <label>租户状态:</label>
          <Select
            placeholder="租户状态"
            style={{ width: 150 }}
            value={filters.status || 'all'}
            onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
          >
            <Option value="all">全部状态</Option>
            <Option value="active">活跃</Option>
            <Option value="suspended">暂停</Option>
            <Option value="disabled">禁用</Option>
          </Select>
          
          <label>租户代码:</label>
          <Input
            placeholder="搜索租户代码"
            style={{ width: 150 }}
            allowClear
            value={filters.tenantCode}
            onChange={(e) => setFilters(prev => ({ ...prev, tenantCode: e.target.value }))}
          />

          <label>搜索:</label>
          <Input
            placeholder="搜索租户名"
            style={{ width: 250 }}
            allowClear
            value={filters.content}
            onChange={(e) => setFilters(prev => ({ ...prev, content: e.target.value }))}
          />
          <Button type='primary' icon={<SearchOutlined />} onClick={handleSearch}>搜索</Button>
          <Button onClick={() => {
            // 重置所有筛选条件
            setFilters({
              status: 'all',
              accountType: 'all',
              tenantId: 'all',
              content: '',
              tenantCode: '',
              tenantType: 'all'
            });
            // 执行搜索操作 - 调用API获取数据
            fetchUserList({
              pageNum: 1,
              pageSize: pagination.pageSize,
              tenantCode: '',
              tenantName: '',
              tenantType: '',
              status: ''
            });
          }}>重置</Button>
          <Button
            icon={<LockOutlined />}
            onClick={() => {
              if (selectedRowKeys.length === 0) {
                message.warning('请选择要禁用的租户');
                return;
              }

              // 调用API批量禁用租户
              tenantManagementApi.disableTenants(selectedRowKeys)
                .then(response => {
                  if (response.code === 200) {
                    message.success('租户禁用成功');
                    // 刷新数据
                    fetchUserList({
                      pageNum: pagination.current,
                      pageSize: pagination.pageSize
                    });
                    // 刷新统计数据
                    fetchStatistics();
                    // 清空选择
                    setSelectedRowKeys([]);
                  }
                })
                .catch(error => {
                  console.error('禁用租户失败:', error);
                });
            }}
          >
            禁用
          </Button>
          <Button
            icon={<UnlockOutlined />}
            onClick={() => {
              if (selectedRowKeys.length === 0) {
                message.warning('请选择要启用的租户');
                return;
              }

              // 调用API批量启用租户
              tenantManagementApi.enableTenants(selectedRowKeys)
                .then(response => {
                  if (response.code === 200) {
                    message.success('租户启用成功');
                    // 刷新数据
                    fetchUserList({
                      pageNum: pagination.current,
                      pageSize: pagination.pageSize
                    });
                    // 刷新统计数据
                    fetchStatistics();
                    // 清空选择
                    setSelectedRowKeys([]);
                  }
                })
                .catch(error => {
                  console.error('启用租户失败:', error);
                });
            }}
          >
            启用
          </Button>
          <Button
            danger
            icon={<DeleteOutlined />}
            disabled={selectedRowKeys.length === 0}
            onClick={() => {
              // 检查选中的租户中是否有禁用状态的租户
              const disabledTenants = users.filter(
                tenant => selectedRowKeys.includes(tenant.userId) && tenant.status === 'disabled'
              );
              
              if (disabledTenants.length > 0) {
                message.warning('禁用状态的租户不能删除');
                return;
              }
              
              Modal.confirm({
                title: '删除确认',
                content: `确定要删除选中的 ${selectedRowKeys.length} 个租户吗？`,
                okText: '确定',
                cancelText: '取消',
                centered: true,
                onOk: async () => {
                  setLoading(true);

                  try {
                    // 调用API批量删除租户
                    const response = await tenantManagementApi.deleteTenants(selectedRowKeys);
                    
                    if (response.code === 200) {
                      message.success('租户删除成功');
                      // 刷新租户列表
                      fetchUserList({
                        pageNum: pagination.current,
                        pageSize: pagination.pageSize
                      });
                      // 刷新统计数据
                      fetchStatistics();
                      // 清空选择
                      setSelectedRowKeys([]);
                    }
                  } catch (error) {
                    console.error('删除租户失败:', error);
                  } finally {
                    setLoading(false);
                  }
                }
              });
            }}
          >
            批量删除
          </Button>
          <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              添加租户
            </Button>
        </Space>
      </Card>

      {/* User Table */}
      <Card className="user-table">
        <Table
          columns={columns}
          dataSource={users}
          rowKey="userId"
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          loading={loading}
          pagination={{
            ...pagination,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          onChange={handleTableChange}
          columnTitle={{ style: { textAlign: 'center' } }}
          scroll={{ y: users.length > 10 ? 650 : undefined }}
        />
      </Card>

      {/* Create User Modal */}
      <Modal
        title="添加租户"
        open={createModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        width={720}
        centered
        confirmLoading={loading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateUser}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="tenantCode"
                label="租户代码"
                rules={[{ required: true, message: '请输入租户代码' }]}
              >
                <Input placeholder="请输入租户代码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="tenantName"
                label="租户名称"
                rules={[{ required: true, message: '请输入租户名称' }]}
              >
                <Input placeholder="请输入租户名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="tenantType"
                label="租户类型"
                rules={[{ required: true, message: '请选择租户类型' }]}
              >
                <Select placeholder="请选择租户类型">
                  <Option value="enterprise">企业</Option>
                  <Option value="government">政府</Option>
                  <Option value="education">教育</Option>
                  <Option value="medical">医疗</Option>
                  <Option value="other">其他</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="deviceLimit"
                label="设备数量限制"
                rules={[{ required: true, message: '请输入设备数量限制' }]}
              >
                <Input type="number" placeholder="请输入设备数量限制" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="租户状态"
                rules={[{ required: true, message: '请选择租户状态' }]}
              >
                <Radio.Group>
                  <Radio value="active">活跃</Radio>
                  <Radio value="suspended">暂停</Radio>
                  <Radio value="disabled">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="tier"
                label="服务等级"
                rules={[{ required: true, message: '请选择服务等级' }]}
              >
                <Radio.Group>
                  <Radio value="basic">基础版</Radio>
                  <Radio value="standard">标准版</Radio>
                  <Radio value="professional">专业版</Radio>
                  <Radio value="enterprise">企业版</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="isTrial"
                label="是否试用租户"
                rules={[{ required: true, message: '请选择是否为试用租户' }]}
              >
                <Radio.Group>
                  <Radio value={1}>是</Radio>
                  <Radio value={0}>否</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="paymentStatus"
                label="支付状态"
                rules={[{ required: true, message: '请选择支付状态' }]}
              >
                <Radio.Group>
                  <Radio value="paid">已支付</Radio>
                  <Radio value="unpaid">未支付</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="billingCycle"
                label="计费周期"
              >
                <Radio.Group>
                  <Radio value="monthly">月</Radio>
                  <Radio value="quarterly">季</Radio>
                  <Radio value="yearly">年</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="autoRenew"
                label="是否自动续订"
              >
                <Radio.Group>
                  <Radio value={true}>是</Radio>
                  <Radio value={false}>否</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="parentTenantId"
                label="上级租户"
              >
                <Select placeholder="请选择上级租户" allowClear>
                  <Option value="">无上级租户</Option>
                  {parentTenants.filter(tenant => tenant.id !== currentUserId).map(tenant => (
                    <Option key={tenant.id} value={tenant.id}>{tenant.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="industry"
                label="所属行业"
              >
                <Input placeholder="请输入所属行业" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="联系电话"
                rules={[
                  { pattern: /^1\d{10}$/, message: '请输入有效的手机号' }
                ]}
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="联系邮箱"
                rules={[
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input placeholder="请输入联系邮箱" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 编辑租户模态框 */}
      <Modal
        title="编辑租户"
        open={editModalVisible}
        onOk={() => editForm.submit()}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
        }}
        width={760}
        centered
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEditUser}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="tenantCode"
                label="租户代码"
                rules={[{ required: true, message: '请输入租户代码' }]}
              >
                <Input placeholder="请输入租户代码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="tenantName"
                label="租户名称"
                rules={[{ required: true, message: '请输入租户名称' }]}
              >
                <Input placeholder="请输入租户名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="tenantType"
                label="租户类型"
                rules={[{ required: true, message: '请选择租户类型' }]}
              >
                <Select placeholder="请选择租户类型">
                  <Option value="enterprise">企业</Option>
                  <Option value="government">政府</Option>
                  <Option value="education">教育</Option>
                  <Option value="medical">医疗</Option>
                  <Option value="other">其他</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="deviceLimit"
                label="设备数量限制"
                rules={[{ required: true, message: '请输入设备数量限制' }]}
              >
                <Input type="number" placeholder="请输入设备数量限制" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="租户状态"
                rules={[{ required: true, message: '请选择租户状态' }]}
              >
                <Radio.Group>
                  <Radio value="active">活跃</Radio>
                  <Radio value="suspended">暂停</Radio>
                  <Radio value="disabled">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="tier"
                label="服务等级"
                rules={[{ required: true, message: '请选择服务等级' }]}
              >
                <Radio.Group>
                  <Radio value="basic">基础版</Radio>
                  <Radio value="standard">标准版</Radio>
                  <Radio value="professional">专业版</Radio>
                  <Radio value="enterprise">企业版</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="isTrial"
                label="是否试用租户"
                rules={[{ required: true, message: '请选择是否为试用租户' }]}
              >
                <Radio.Group>
                  <Radio value={1}>是</Radio>
                  <Radio value={0}>否</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="paymentStatus"
                label="支付状态"
                rules={[{ required: true, message: '请选择支付状态' }]}
              >
                <Radio.Group>
                  <Radio value="paid">已支付</Radio>
                  <Radio value="unpaid">未支付</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="billingCycle"
                label="计费周期"
              >
                <Radio.Group>
                  <Radio value="monthly">月</Radio>
                  <Radio value="quarterly">季</Radio>
                  <Radio value="yearly">年</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="autoRenew"
                label="是否自动续订"
              >
                <Radio.Group>
                  <Radio value={true}>是</Radio>
                  <Radio value={false}>否</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="parentTenantId"
                label="上级租户"
              >
                <Select placeholder="请选择上级租户" allowClear>
                  <Option value="">无上级租户</Option>
                  {parentTenants.filter(tenant => tenant.id !== currentUserId).map(tenant => (
                    <Option key={tenant.id} value={tenant.id}>{tenant.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="industry"
                label="所属行业"
              >
                <Input placeholder="请输入所属行业" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="联系电话"
                rules={[
                  { pattern: /^1\d{10}$/, message: '请输入有效的手机号' }
                ]}
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="联系邮箱"
                rules={[
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input placeholder="请输入联系邮箱" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default TenantList;