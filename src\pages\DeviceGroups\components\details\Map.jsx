import React, { useEffect } from 'react';
import { Card } from 'antd';
import AMapLoader from '@amap/amap-jsapi-loader';

const Map = ({ group }) => {
  useEffect(() => {
    AMapLoader.load({
      key: 'your-amap-key',
      version: '2.0',
    }).then((AMap) => {
      const map = new AMap.Map('container', {
        zoom: 11,
        center: [120.15, 30.28],
      });
    });
  }, []);

  return (
    <Card title="位置覆盖">
      <div id="container" style={{ height: '400px' }}></div>
    </Card>
  );
};

export default Map;