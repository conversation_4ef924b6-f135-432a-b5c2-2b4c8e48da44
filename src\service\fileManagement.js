import { getLoginEntry } from '../utils/loginLogger';
// API基础URL - 可以根据实际部署环境手动配置
import { API_PREFIX } from "./constant";
import { message } from 'antd';

const handleApiError = (response) => {
  return response.json().then(data => {
    if (response.status === 401) {
      message.error(data?.message);
      setTimeout(() => {
        window.location.href = '/login'
      }, 500);
    } else if (response.status === 400 || response.status === 404 || response.status === 500) {
      message.error(data?.message);
    } else if (response.status === 403) {
      // Handle forbidden
    }
    return Promise.reject(data);
  });
};

export const fileManagementApi = {
  getFileList: async (params) => {
    // 从localStorage获取最新的登录数据
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/file/page`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      },
      body: JSON.stringify({
        pageNum: params.page,
        pageSize: params.pageSize,
        filename: params.filename || '',
        contentType: params.contentType || ''
      })
    }).then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },

  batchUploadFiles: async (files) => {
    const token = getLoginEntry();
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });
    return fetch(`${API_PREFIX["user-service"]}/file/batch-upload`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Authorization': token 
      },
      body: formData
    }).then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },

  downloadFile: async (fileId) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/file/download/${fileId}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      }
    }).then(response => {
      if(response.status === 200){
        return response.blob()
      }
      return handleApiError(response);
    })
  },

  getFileDetail: async (fileId) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/file/${fileId}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      }
    }).then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },

  getPresignedUrl: async (fileId) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/file/presigned-url/${fileId}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    }).then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },

  deleteFile: async (fileId) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/file/delete/${fileId}`, {
      method: 'PUT',
      credentials: 'include',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    }).then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  }
};

