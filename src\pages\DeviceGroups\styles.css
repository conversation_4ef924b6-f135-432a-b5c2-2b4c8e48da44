.device-groups-container {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.function-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* margin-bottom: 16px; */
}

.device-group-tree-scroll{
  height: calc(100% - 80px);
  overflow-y: auto;
}

.device-groups-breadcrumb{
  width: 500px;
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}

.breadcrumb.ant-breadcrumb {
  width: 450px;
  margin-bottom: 16px;
}

.main-content {
  flex: 1;
  background: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.tree-sider {
  background: #fff;
  border-right: 1px solid #f0f0f0;
  padding: 16px;
  border-radius: 8px;
}

.detail-content {
  padding: 24px;
  background: #fff;
}

.device-groups-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.node-title {
  flex: 1;
}

.tree-sider .ant-tree-title .device-groups-tree-node .ant-space:not(:nth-child(1)){
  opacity: 0;
} 

.tree-sider .ant-tree-title:hover .device-groups-tree-node .ant-space:not(:nth-child(1)){
  opacity: 1;
  transition: opacity 0.3s ease;
}

.empty-detail {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .tree-sider {
    width: 100% !important;
    max-width: 100% !important;
  }

  .detail-content {
    padding: 16px;
  }

  .function-area {
    flex-direction: column;
    gap: 16px;
  }

  .operation-area {
    width: 100%;
  }
}