.device-repellent-container {
  height: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.group-sider {
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

/* .group-tree-scroll {
  height: calc(100% - 150px);
  overflow-y: auto;
} */

.group-header {
  padding: 16px 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.device-content {
  width: 100%;
  padding: 16px;
  background: #fff;
  overflow: auto;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.device-table {
  margin-top: 16px;
}

.expanded-row {
  padding: 16px;
  background: #fafafa;
}

.device-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.device-details p {
  margin: 0;
}

.device-repellent-node-title>.ant-space-item:nth-child(2){
  opacity: 0;
}
.device-repellent-node-title:hover > .ant-space-item:nth-child(2){
  opacity:  1;
  transition: opacity 0.3s ease;
}

.device-repellent-node-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.device-repellent-title {
  flex: 1;
}

.device-repellent-tree .ant-tree-treenode{
  width: 99% !important;
}

.customer-tree .ant-tree-switcher{
  display: none;
}

.customer-tree .ant-tree-node-content-wrapper{
  padding-left: 15px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .group-sider {
    display: none;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 16px;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
  }
}

.icon_img{
  width: 14px;
  height: 14px;
}

/* Configuration styles */
.configuration-container {
  background: #fff;
}

.config-sider {
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

.config-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.config-tree{
  width: 98%;
}
.config-tree .ant-tree-switcher{
  display: none;
}

.config-content {
  height: calc(100% - 64px);
  background: #f0f2f5;
  overflow: auto;
}


