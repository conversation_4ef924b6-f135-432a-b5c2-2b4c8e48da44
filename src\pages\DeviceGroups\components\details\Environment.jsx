import { Card, Descriptions, Space } from 'antd';

const Environment = ({ group }) => {
  if (!group) return null;

  const  LocationType ={
    "indoor":"室内",
    "outdoor":"室外",
    "semi":"半室外"
  }

  return (
    <div className="environment-info">
      <Card title="基础信息" className="info-card">
        <Descriptions bordered column={2}>
          <Descriptions.Item label="完整地址" span={2}>
            {group?.address}
          </Descriptions.Item>
          <Descriptions.Item label="位置类型">
            {LocationType[group?.locationType] || '无'}
          </Descriptions.Item>
          <Descriptions.Item label="经纬度">
            {group?.geoLocation || '无'}
          </Descriptions.Item>
          <Descriptions.Item label="环境信息">
            {group?.environmentInfo || '无'}
          </Descriptions.Item>
          <Descriptions.Item label="安装信息">
            {group?.installationInfo || '无'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card title="设备覆盖" className="info-card" style={{ marginTop: '16px' }}>
        <Descriptions bordered column={2}>
          <Descriptions.Item label="设备总数">
            {group?.actualDeviceNum}
          </Descriptions.Item>
          <Descriptions.Item label="设备在线数">
            <Space>
              {group?.onlineDeviceNum}
            </Space>
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </div>
  );
};

export default Environment;