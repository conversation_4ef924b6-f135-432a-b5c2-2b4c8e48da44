import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Table,
  Tag,
  Breadcrumb,
  Input,
  Select,
  Upload,
  Rate,
  Badge,
  Tooltip,
  Modal,
  Form,
  Radio,
  Tabs,
  message,
  Slider,
} from "antd";
import {
  UploadOutlined,
  DownloadOutlined,
  ReloadOutlined,
  SearchOutlined,
  PlayCircleOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  PlusOutlined,
  SoundOutlined,
  PauseCircleOutlined,
  CloseCircleOutlined,
} from "@ant-design/icons";
import {
  getRatAudiosList,
  createRatAudiosPackage,
  delRatAudio,
  updateRatAudio,
  downloadRatAudio,
} from "./../../service/maintenanceAudio";
import "./styles.css";
import { getAudioType } from "./../../service/dictionary";
import { dictionaryConfigApi } from "./../../service/DictionaryConfig";
import AdvancedSearch from "../../components/advancedSearch";
const { Option } = Select;

const MaintenanceAudio = () => {
  // const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [audioToDelete, setAudioToDelete] = useState(null);
  const [audioToEdit, setAudioToEdit] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [audioList, setAudioList] = useState([]);
  const [applicableSceneOptions, setApplicableSceneOptions] = useState([]);
  const categoryRef = useRef("");
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 搜索参数状态
  const [searchParams, setSearchParams] = useState({
    page: 0,
    size: 10,
    audioType: "",
    category: "",
  });

  // 音频数据状态
  const [audioFiles, setAudioFiles] = useState([]);
  const [total, setTotal] = useState(0);

  const [audioPlayerVisible, setAudioPlayerVisible] = useState(false);
  const [currentAudio, setCurrentAudio] = useState(null);
  // const audioRef = useRef(null);
  const [audioUrl,setAudioUrl] = useState(null)

  const [advancedSearchVisible, setAdvancedSearchVisible] = useState(false); // 高级搜索弹窗显示隐藏

  // 获取音频列表数据
  const fetchAudioList = async (params) => {
    try {
      setLoading(true);
      const response = await getRatAudiosList(params);
      setAudioFiles(response.content || []);
      setTotal(response.totalElements || 0);
    } catch (error) {
      console.error("获取音频列表失败:", error);
      // message.error('获取音频列表失败')
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAudioData();
    fetchApplicableSceneOptions();
     // 清理函数，在组件卸载时取消请求
     return () => {
      if (window.cancelToken) {
        window.cancelToken() // 取消请求
      }
    }
  }, []);

  // 获取适用场景选项列表
  const fetchApplicableSceneOptions = async () => {
    try {
      const res = await dictionaryConfigApi.getDictItemByType("applicable_scene");
      if (res && res.length) {
        setApplicableSceneOptions(res);
      }
    } catch (err) {
      console.error("获取适用场景选项失败:", err);
    }
  };

  // 获取音频分类类型列表
  const fetchAudioData = async () => {
    try {
      let res = await getAudioType();
      if (res.length) {
        setAudioList(res);
      }
    } catch (err) {
      console.log(err);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchAudioList(searchParams);
  }, [searchParams]);

  // 更新搜索参数
  const updateSearchParams = (obj) => {
    setSearchParams((prev) => ({
      ...prev,
      ...obj,
    }));
  };

  // 处理搜索
  const handleSearch = async (values) => {
    const tempObj = {
      page: 0,
      audioType: values.audioType || "",
      category: values.category || "",
    };
    updateSearchParams(tempObj);
  };

  // 处理重置
  const handleReset = () => {
    searchForm.resetFields();
    const tempObj = {
      page: 0,
      audioType: "",
      category: "",
    };
    updateSearchParams(tempObj);
  };

  // 处理分页变化
  const handleTableChange = (pagination) => {
    const tempObj = {
      page: pagination.current - 1,
      size: pagination.pageSize,
    };
    updateSearchParams(tempObj);
  };

  // 处理编辑音频
  const handleEditAudio = (record) => {
    setAudioToEdit(record);
    setIsEditMode(true);
    setCreateModalVisible(true);
    categoryRef.current = record.category;

    // 处理适用场景的单一值或多个值
    let applicableSceneValues = record.applicableScene;
    if (applicableSceneValues && !Array.isArray(applicableSceneValues)) {
      applicableSceneValues = applicableSceneValues.split(',');
    }

    // 设置表单值，保留原始值用于提交
    form.setFieldsValue({
      audioName: record.audioName,
      audioType: record.audioType,
      applicableScene: applicableSceneValues,
      category: audioList.some((item) => item.itemValue === record.category)
        ? record.category
        : "-",
      lockVersion: record.lockVersion,
      description: record.description,
    });
  };

  // 处理下载音频
  const handleDownloadAudio = async (record) => {
    try {
      const blob = await downloadRatAudio(record.audioId);
      const downloadUrl = window.URL.createObjectURL(blob)

      // 创建临时锚点元素触发下载
      const link = document.createElement("a");
      link.href = downloadUrl;
      // 添加文件后缀
      const fileName = record.audioName || "audio-file";
      const fileExtension = record.audioType?.toLowerCase() || "";
      link.download = `${fileName}.${fileExtension}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      message.success("下载成功");
    } catch (error) {
      message.error("获取下载链接失败：" + (error.message || "未知错误"));
      console.error("获取下载链接失败:", error);
    }
  };

  // 处理创建/更新音频
  const handleCreateAudio = async (values) => {
    try {
      let audioData = JSON.parse(JSON.stringify(values));
      const formData = new FormData();
      formData.append("audioName", values.audioName);
      formData.append("audioType", values.audioType);
      
      // 处理多选的适用场景，转换为逗号分隔的字符串
      const applicableSceneValue = Array.isArray(values.applicableScene) 
        ? values.applicableScene.join(',') 
        : values.applicableScene;
      
      formData.append("applicableScene", applicableSceneValue);
      audioData.applicableScene = applicableSceneValue;
      
      if (values.category === "-") {
        formData.append("category", categoryRef.current);
        audioData.category = categoryRef.current;
      } else {
        formData.append("category", values.category);
      }

      if (values.description) {
        formData.append("description", values.description);
      }

      if (values.file && values.file.fileList[0]) {
        formData.append("file", values.file.fileList[0].originFileObj);
      } else if (!isEditMode) {
        message.error("请上传音频文件");
        return;
      }

      if (isEditMode) {
        // 更新现有音频
        await updateRatAudio(audioToEdit.audioId, audioData);
        message.success("音频更新成功！");
      } else {
        // 创建新音频
        await createRatAudiosPackage(formData);
        message.success("音频创建成功！");
      }

      setCreateModalVisible(false);
      form.resetFields();
      setIsEditMode(false);
      setAudioToEdit(null);
      // 刷新列表
      fetchAudioList(searchParams);
    } catch (error) {
      // message.error(
      //   isEditMode
      //     ? '更新音频失败'  + (error.message || '未知错误')
      //     : '创建音频失败：' + (error.message || '未知错误')
      // )
      console.error(isEditMode ? "更新音频失败:" : "创建音频失败:", error);
    }
  };

  // 处理删除音频
  const handleDeleteAudio = async () => {
    if (!audioToDelete) return;

    try {
      await delRatAudio(audioToDelete.audioId);
      message.success("音频删除成功！");
      setDeleteModalVisible(false);
      setAudioToDelete(null);
      // 刷新列表
      fetchAudioList(searchParams);
    } catch (error) {
      message.error("删除音频失败：" + (error.message || "未知错误"));
      console.error("删除音频失败:", error);
    }
  };

  // Handle audio playback
  const handlePlayAudio = async (record) => {
    try {
      const blob = await downloadRatAudio(record.audioId);
      const url = window.URL.createObjectURL(blob);
      setAudioUrl(url);
      setCurrentAudio(record);
      setAudioPlayerVisible(true);
    } catch (error) {
      console.error("获取音频失败:", error);
    }
  };

  const handleCloseAudio = () => {
    setAudioPlayerVisible(false);
    setCurrentAudio(null);
  };


  // Table columns configuration
  const columns = [
    {
      title: "音频名称",
      dataIndex: "audioName",
      key: "audioName",
      ellipsis: true,
      render: (text, record) => (
        <Space>
          <Button 
            type="text"
            icon={<SoundOutlined />} 
            onClick={() => handlePlayAudio(record)}
            style={{ color: '#1890ff' }}
          />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: "音频类型",
      dataIndex: "audioType",
      key: "audioType",
      ellipsis: true,
      render: (type) => <Tag color="blue">{type}</Tag>,
    },
    {
      title: "适用场景",
      dataIndex: "applicableScene",
      key: "applicableScene",
      ellipsis: false,
      render: (scene) => {
        if (!scene) return '-';
        // 处理场景数据可能是字符串或数组的情况
        const sceneArray = Array.isArray(scene) ? scene : scene.split(',');
        return (
          <span>
            {sceneArray.map((sceneItem, index) => {
              const option = applicableSceneOptions.find(item => item.itemCode === sceneItem);
              return (
                <Tag color="blue" key={index} style={{ marginBottom: '4px' }}>
                  {option ? option.itemName : '-' }
                </Tag>
              );
            })}
          </span>
        );
      },
    },
    {
      title: "文件大小",
      dataIndex: "fileSize",
      key: "fileSize",
      ellipsis: true,
      render: (size) => `${(size / 1024 / 1024).toFixed(2)} MB`,
    },
    {
      title: "音频时长",
      dataIndex: "duration",
      key: "duration",
      ellipsis: true,
      render: (duration) => `${duration ?? "-"} 秒`,
    },
    {
      title: "所属分类",
      dataIndex: "category",
      key: "category",
      ellipsis: true,
      render: (category) => {
        let text = "";

        for (let i = 0; i < audioList.length; i++) {
          const item = audioList[i];
          if (item.itemValue === category) {
            text = item.itemName;
          }
        }

        return <Tag>{text}</Tag>;
      },
    },
    {
      title: "音频描述",
      dataIndex: "description",
      key: "description",
      ellipsis: true,
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditAudio(record)}
            />
          </Tooltip>
          <Tooltip title="下载">
            <Button
              type="text"
              icon={<DownloadOutlined />}
              onClick={() => handleDownloadAudio(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                setAudioToDelete(record);
                setDeleteModalVisible(true);
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="maintenance-audio">

      {/* Filter Bar */}
      <Card className="maintenance-audio-filter-bar">
        <div style={{display: "flex", justifyContent: 'space-between'}}>
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          initialValues={{
            audioType: "",
            category: "",
          }}
        >
          <Form.Item name="audioType" label="音频类型" hidden>
            <Select style={{ width: 200 }} placeholder="全部类型">
              <Option value="">全部类型</Option>
              <Option value="WAV">WAV</Option>
              <Option value="MP3">MP3</Option>
            </Select>
          </Form.Item>
          <Form.Item name="category" label="所属分类">
            <Select style={{ width: 200 }} placeholder="所属分类">
              <Option value="">全部分类</Option>
              {audioList.map((item) => (
                <Option value={item.itemValue} key={item.id}>{item.itemName}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SearchOutlined />}
              >
                搜索
              </Button>
              <Button onClick={handleReset} icon={<ReloadOutlined />}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
        <Space>
            {/* <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={() => setAdvancedSearchVisible(true)}
            >
              高级搜索
            </Button> */}
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              创建
            </Button>
          </Space>
        </div>
      </Card>

      {/* Audio List */}
      <Card className="audio-list">
        <Table
          columns={columns}
          dataSource={audioFiles}
          rowKey={"audioId"}
          scroll={{ x: 1000 }}
          // rowSelection={{
          //   selectedRowKeys,
          //   onChange: setSelectedRowKeys
          // }}
          pagination={{
            total: total,
            pageSize: searchParams.size,
            current: searchParams.page + 1,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
          loading={loading}
        />
      </Card>

      {/* Create/Edit Audio Modal */}
      <Modal
        title={isEditMode ? "编辑音频" : "创建音频"}
        open={createModalVisible}
        okText={isEditMode ? "编辑" : "创建"}
        onOk={() => form.submit()}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
          setIsEditMode(false);
          setAudioToEdit(null);
        }}
        width={720}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateAudio} initialValues={{
          audioType: "MP3"
        }}>
          <Form.Item
            name="audioName"
            label="音频名称"
            rules={[{ required: true, message: "请输入音频名称" }]}
          >
            <Input placeholder="请输入音频名称" />
          </Form.Item>

          <Form.Item
            name="audioType"
            label="音频类型"
            hidden
            rules={[{ required: true, message: "请选择音频类型" }]}
          >
            <Select>
              <Option value="WAV">WAV</Option>
              <Option value="MP3">MP3</Option>
            </Select>
          </Form.Item>
          <Form.Item name="lockVersion" label="乐观锁" hidden={true}>
            <Input />
          </Form.Item>

          <Form.Item
            name="applicableScene"
            label="适用场景"
            rules={[{ required: true, message: "请选择适用场景" }]}
          >
            <Select 
              placeholder="请选择适用场景" 
              mode="multiple"
              maxTagCount={3}
              maxTagTextLength={10}
              optionFilterProp="children"
            >
              {applicableSceneOptions.map((item) => (
                <Option value={item.itemCode} key={item.id}>
                  {item.itemName}
                </Option>
              ))}
              {isEditMode && form.getFieldValue('applicableScene')?.map(scene => {
                const exists = applicableSceneOptions.some(option => option.itemCode === scene);
                if (!exists) {
                  return (
                    <Option value={scene} key={scene}>
                      -
                    </Option>
                  );
                }
                return null;
              })}
            </Select>
          </Form.Item>

          <Form.Item
            name="category"
            label="所属分类"
            rules={[{ required: true, message: "请选择所属分类" }]}
          >
            <Select>
              {audioList.map((item) => (
                <Option value={item.itemValue} key={item.id}>{item.itemName}</Option>
              ))}
            </Select>
          </Form.Item>

          {!isEditMode && (
            <Form.Item
              name="file"
              label="音频文件"
              rules={[{ required: !isEditMode, message: "请上传音频文件" }]}
            >
              <Upload.Dragger
                accept=".mp3"
                beforeUpload={(file) => {
                  const isLt1M = file.size / 1024 / 1024 < 1;
                  if (!isLt1M) {
                    message.error('文件大小不能超过1MB!');
                    form.setFieldsValue({
                      file: undefined,
                    });
                    return Upload.LIST_IGNORE;
                  }
                  return false;
                }}
                onRemove={() => {
                  form.setFieldsValue({
                    file: undefined,
                  });
                }}
              >
                <p className="ant-upload-drag-icon">
                  <UploadOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">支持 MP3 格式文件，文件大小不超过1MB</p>
              </Upload.Dragger>
            </Form.Item>
          )}

          <Form.Item name="description" label="音频描述">
            <Input.TextArea rows={4} placeholder="请输入音频描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        title="确认删除"
        open={deleteModalVisible}
        onOk={handleDeleteAudio}
        onCancel={() => {
          setDeleteModalVisible(false);
          setAudioToDelete(null);
        }}
      >
        <p>
          确定要删除音频 &quot;{audioToDelete?.audioName}&quot;
          吗？此操作不可恢复。
        </p>
      </Modal>

      {/* Audio Player Modal */}
      <Modal
        title="音频播放器"
        open={audioPlayerVisible}
        onCancel={handleCloseAudio}
        footer={null}
        width={400}
      >
        {currentAudio && (
          <div style={{ textAlign: 'center' }}>
            <h3>{currentAudio.audioName}</h3>
            <audio
              // ref={audioRef}
              src={audioUrl}
              // onTimeUpdate={handleTimeUpdate}
              // onLoadedMetadata={handleLoadedMetadata}
              // onEnded={() => setIsPlaying(false)}
              controls
              autoPlay
              controlsList="nodownload" // 屏蔽下载按钮
            />
          </div>
        )}
      </Modal>

      {/* Advanced Search Modal */}
      {advancedSearchVisible && (
        <AdvancedSearch
          advancedSearchVisible={advancedSearchVisible}
          onCancel={() => {
            setAdvancedSearchVisible(false);
          }}
          data={[
            {
              label: '音频名称',
              value: 'audioName'
            },
            {
              label: '音频类型',
              value: 'audioType',
              options: [
                // { label: 'WAV', value: 'WAV' },
                { label: 'MP3', value: 'MP3' }
              ],
              children: (setInputValue) => (
                <Select
                  placeholder='请选择音频类型'
                  style={{ width: '300px' }}
                  onChange={value => {
                    setInputValue(value)
                  }}
                  options={[
                    // { label: 'WAV', value: 'WAV' },
                    { label: 'MP3', value: 'MP3' }
                  ]}
                />
              )
            },
            {
              label: '适用场景',
              value: 'applicableScene',
              options: applicableSceneOptions.map(item => ({
                label: item.itemName,
                value: item.itemCode
              })),
              children: (setInputValue) => (
                <Select
                  placeholder='请选择适用场景'
                  style={{ width: '300px' }}
                  onChange={value => {
                    setInputValue(value)
                  }}
                  options={applicableSceneOptions.map(item => ({
                    label: item.itemName,
                    value: item.itemCode
                  }))}
                />
              )
            },
            {
              label: '文件大小',
              value: 'fileSize'
            },
            {
              label: '音频时长',
              value: 'duration'
            },
            {
              label: '所属分类',
              value: 'category',
              options: audioList.map(item => ({
                label: item.itemName,
                value: item.itemValue
              })),
              children: (setInputValue) => (
                <Select
                  placeholder='请选择所属分类'
                  style={{ width: '300px' }}
                  onChange={value => {
                    setInputValue(value)
                  }}
                  options={audioList.map(item => ({
                    label: item.itemName,
                    value: item.itemValue
                  }))}
                />
              )
            },
            {
              label: '音频描述',
              value: 'description'
            }
          ]}
          getSearchData={data => {
            console.log(data);
            // TODO: 处理高级搜索数据
            setAdvancedSearchVisible(false);
          }}
        />
      )}
    </div>
  );
};

export default MaintenanceAudio;
