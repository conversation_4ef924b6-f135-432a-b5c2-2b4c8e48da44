# version: "3"
services:
  service-nginx:
    image: nginx:1.26
    container_name: "service-nginx"
    restart: ${RESTART_POLICY}
    volumes:
      - ./context/vcloud-web/www:/usr/share/nginx/html
      - ./context/vcloud-web/logs:/var/log/nginx
      - ./context/vcloud-web/nginx.conf:/etc/nginx/nginx.conf
      - ./context/vcloud-web/cert:/etc/nginx/cert
      - ./context/vcloud-web/config:/etc/nginx/conf.d
    network_mode: host
    environment:
      - TZ=Asia/Shanghai
      - NGINX_HOST=seadee.com.cn
