.role-permission {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  overflow: hidden;
}

.page-header {
  margin-bottom: 24px;
}

.header-left {
  margin-bottom: 16px;
}

.header-left h2 {
  margin: 8px 0 0;
  font-size: 24px;
  font-weight: 500;
}

.filter-bar {
  margin-bottom: 24px;
}

.role-list {
  margin-bottom: 24px;
  overflow: auto;
}

.permission-card {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: auto;
  max-height: 550px;
}

.permission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
}

.permission-tree {
  flex: 1;
  overflow: auto;
}

/* 让树节点水平展示 */
.permission-card .ant-tree .ant-tree-treenode {
  display: flex !important;
  padding-left: 0 !important;
}

/* 强制子树列表水平展示 */
.permission-card .ant-tree .ant-tree-list,
.permission-card .ant-tree .ant-tree-list-holder,
.permission-card .ant-tree .ant-tree-list-holder-inner {
  display: flex !important;
  flex-wrap: wrap !important;
}

/* 隐藏树形控件的展开/折叠图标 */
.permission-card .ant-tree .ant-tree-switcher {
  display: none;
}

/* 为节点内容添加左边距，代替展开图标的位置 */
.permission-card .ant-tree .ant-tree-node-content-wrapper {
  margin-left: 24px;
}

/* Modal styles */
.ant-modal-body .ant-form-item {
  margin-bottom: 24px;
}

.ant-modal-body .ant-radio-group {
  width: 100%;
  display: flex;
  gap: 8px;
}

.ant-modal-body .ant-radio-button-wrapper {
  flex: 1;
  text-align: center;
}

/* 权限复选框列表样式 */
.permission-checkbox-list {
  display: flex;
  flex-direction: column;
  /* gap: 24px; */
  padding: 16px;
  overflow: auto;
  flex: 1;
}

.parent-permission {
  /* margin-bottom: 8px; */
  padding-bottom: 10px;
}

.parent-permission .ant-checkbox-wrapper {
  font-weight: 500;
  font-size: 16px;
}

.children-permissions {
  padding-left: 24px;
}

.child-permission {
  margin-bottom: 8px;
}

.grandchild-permissions {
  margin-top: 8px;
  padding-left: 24px;
}

.permission-title {
  margin-right: 8px;
}

/* 加载状态样式 */
.permission-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive styles */
@media (max-width: 768px) {
  .role-permission {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .permission-header {
    flex-direction: column;
    gap: 16px;
  }

  .ant-modal {
    max-width: 100vw;
    margin: 8px;
  }
}

/* 添加以下CSS规则使权限树的子节点水平排列 */
.permission-tree .ant-tree-list-holder-inner {
  display: flex;
  flex-wrap: wrap;
}

.permission-tree .ant-tree-treenode {
  width: auto;
  margin-right: 20px;
}

.permission-tree .ant-tree-node-content-wrapper {
  display: flex;
  align-items: center;
}

/* 添加权限下拉样式 */
.permission-dropdown-content {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-height: 400px;
  overflow-y: auto;
}

.ant-dropdown-menu-item {
  padding: 5px 12px;
}

.permission-dropdown-content .custom-tree {
  padding: 5px;
}

.permission-dropdown-content .custom-tree-node {
  margin-bottom: 4px;
}

.permission-dropdown-content .node-content {
  padding: 4px 0 !important;
}

/* 增加子节点显示样式 */
.permission-dropdown-content .node-children .ant-col {
  min-width: 180px;
  margin-bottom: 5px;
}

.permission-dropdown-content .node-children .ant-checkbox-wrapper {
  width: 100%;
  white-space: normal;
  word-break: break-word;
}

.permission-dropdown-content .ant-checkbox-wrapper {
  align-items: flex-start;
}

.permission-dropdown-content .permission-title {
  font-weight: 500;
}