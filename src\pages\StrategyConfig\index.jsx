import { useState, useEffect } from "react";
import {
  Layout,
  Tabs,
  Button,
  Input,
  Space,
  Table,
  Tag,
  Tooltip,
  Tree,
  Modal,
  Form,
  Select,
  DatePicker,
  Row,
  Col,
  TreeSelect,
  message,
} from "antd";
import dayjs from "dayjs";
import {
  EditOutlined,
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined,
  CloseOutlined,
  SettingOutlined,
  RollbackOutlined
} from "@ant-design/icons";
import {
  getCustomModelList,
  delManyCustomModelByIds,
  // getCustomModelDetail,
  regionDeviceSetting,
  getTree
} from "../../service/strategyConfig";
import { getStrategyType } from "./../../service/dictionary";
import AddUpdataStrategy from "./addUpdataStrategy";
import DetailStrategy from "./detailStrategy";
import "./styles.css";

const { Sider, Content } = Layout;

const StrategyConfig = () => {
  const [strategyConfigData, setStrategyConfigData] = useState([]); // 策略配置列表数据
  const [selectedKeys, setSelectedKeys] = useState([]); // 选中的节点
  const [treeData, setTreeData] = useState([]);
  const [searchParams, setSearchParams] = useState({
    page: 0,
    size: 10,
    typeCode: "",
    keyword: "",
  });
  const [total, setTotal] = useState(0); // 总条数
  const [searchForm] = Form.useForm(); // 搜索表单实例
  const [selectedRowKeys, setSelectedRowKeys] = useState([]); // 选中的行keys
  const [showAddStrategy, setShowAddStrategy] = useState(false); // 控制是否显示AddStrategy组件
  const [showDetailStrategy, setShowDetailStrategy] = useState(false); // 控制是否显示DetailStrategy组件
  const [currentStrategyData, setCurrentStrategyData] = useState(null); // 当前查看的策略数据
  const [isEditMode, setIsEditMode] = useState(false); // 控制是否处于编辑模式
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [issueForm] = Form.useForm(); // 下发配置表单实例
  const [geographyTree,setGeographyTree] = useState(null)
  const [loading,setLoading] = useState(false)

  const getLocationTree = async() =>{
    const res = await getTree(1);
    setGeographyTree(res)
  }

  // 获取策略配置类型列表
  const fetchCustomModelTypeList = async () => {
    setLoading(true)
    try {
      const res = await getStrategyType();
      if (res.length) {
        const transformNode = (item) => ({
          title: item.itemName,
          key: item.itemCode,
        });
        const transformedData = res.map((item) => transformNode(item));
        setTreeData(transformedData);
        //默认选中第一个
        setSelectedKeys([transformedData[0].key]);
        if (transformedData.length > 0) {
          const tempObj = {
            typeCode: transformedData[0].key,
          };
          updateSeachParam(tempObj);
          fetchCustomModelList(tempObj);
        }
      }
      setLoading(false)
    } catch (error) {
      console.error("Failed to fetch customer groups tree:", error);
    }
  };

  // 获取策略配置列表
  const fetchCustomModelList = async (obj = {}) => {
    setLoading(true)
    try {
      const res = await getCustomModelList({
        ...searchParams,
        ...obj,
      });
      setStrategyConfigData(res.content);
      setTotal(res.totalElements);
      setLoading(false)
    } catch (error) {
      console.error("Failed to fetch camera list:", error);
    }
  };

  //更新搜索条件
  const updateSeachParam = (obj) => {
    setSearchParams((pre) => ({
      ...pre,
      ...obj,
    }));
  };

  useEffect(() => {
    getLocationTree();
    fetchCustomModelTypeList();
    // 清理函数，在组件卸载时取消请求
    return () => {
      if (window.cancelToken) {
        window.cancelToken() // 取消请求
      }
    }
  }, []);

  //下发配置
  const handleIssuanceStrategy = async(record) =>{
    setCurrentStrategyData(record);
    setIsModalOpen(true);
  }

  // 处理Modal确认
  const handleOk = () => {
    setLoading(true)
    issueForm.validateFields().then(async (values) => {
      try {
        const params = {
          customModelId:currentStrategyData?.id,
          location:values.location,
          type:values.locationType
        }
        // 调用API进行下发配置
        await regionDeviceSetting(params);
        message.success('下发配置成功');
        setIsModalOpen(false);
        issueForm.resetFields();
        // 刷新列表
        fetchCustomModelList();
      } catch (error) {
        console.error('下发配置失败:', error);
        message.error('下发配置失败: ' + (error.message || '未知错误'));
      }
    }).catch(info => {
      console.log('表单验证失败:', info);
    });
  };

  // 处理Modal取消
  const handleCancel = () => {
    setIsModalOpen(false);
    issueForm.resetFields();
  };

  // Table columns configuration
  const columns = [
    {
      title: "策略名称",
      dataIndex: "name",
      key: "cameraNo",
    },
    {
      title: "超声波工作模式",
      dataIndex: "ultrasoundWorkMode",
      key: "ultrasoundWorkMode",
      render: (ultrasoundWorkMode) => {
        const UltrasoundWorkMode = {
          always: "持续模式",
          trigger: "触发模式",
          switch: "开关模式",
          unmanned: "无人工作模式",
        };
        return UltrasoundWorkMode[ultrasoundWorkMode];
      },
    },
    {
      title: "LED工作模式",
      dataIndex: "ledWorkMode",
      key: "ledWorkMode",
      render: (ledWorkMode) => {
        const LedWorkMode = {
          always: "持续模式",
          trigger: "触发模式",
          switch: "开关模式",
          unmanned: "无人工作模式",
        };
        return LedWorkMode[ledWorkMode];
      },
    },
    {
      title: "喇叭工作模式",
      dataIndex: "soundWorkMode",
      key: "soundWorkMode",
      render: (soundWorkMode) => {
        const SoundWorkMode = {
          always: "持续模式",
          trigger: "触发模式",
          switch: "开关模式",
          unmanned: "无人工作模式",
        };
        return SoundWorkMode[soundWorkMode];
      },
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditStrategy(record)}
            />
          </Tooltip>
          <Tooltip title="查看">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewStrategy(record)}
            />
          </Tooltip>
          <Tooltip title="下发配置">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => handleIssuanceStrategy(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteStrategy(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 处理搜索
  const handleSearch = async (values) => {
    const tempObj = {
      page: 0, // 搜索时重置到第一页
      keyword: values.keyword || "",
    };
    updateSeachParam(tempObj);
    await fetchCustomModelList(tempObj);
  };

  // 处理重置
  const handleReset = () => {
    searchForm.resetFields();
    const tempObj = {
      page: 0,
      keyword: "",
    };
    updateSeachParam(tempObj);
    fetchCustomModelList(tempObj);
  };

  // 树节点选中
  const onTreeSelect = (selectedKeys) => {
    if (selectedKeys.length > 0) {
      setSelectedKeys(selectedKeys);
      const tempObj = {
        page: 0, // 切换类型时重置到第一页
        typeCode: selectedKeys[0],
        ...searchForm.getFieldsValue()
      };
      updateSeachParam(tempObj);
      fetchCustomModelList(tempObj);
    }
  };

  // 处理分页变化
  const handleTableChange = (pagination) => {
    const tempObj = {
      page: pagination.current - 1, // 后端分页从0开始
      size: pagination.pageSize,
    };
    updateSeachParam(tempObj);
    fetchCustomModelList(tempObj);
  };

  // 处理新增
  const handleAdd = () => {
    setShowAddStrategy(true);
  };

  // 处理编辑
  const handleEditStrategy = (record) => {
    setIsEditMode(true);
    setCurrentStrategyData(record);
    setShowAddStrategy(true);
  };

  // 处理查看
  const handleViewStrategy = async (record) => {
    try {
      // const res = await getCustomModelDetail(record.id)

      // Process time fields if they exist
      const processedRecord = { ...record };
      const timeFields = [
        "workStartTime",
        "workEndTime",
        "ultrasoundOpenTime",
        "ultrasoundCloseTime",
        "ledOpenTime",
        "ledCloseTime",
      ];

      timeFields.forEach((field) => {
        if (processedRecord[field]) {
          processedRecord[field] = dayjs(processedRecord[field], "HH:mm");
        }
      });

      setCurrentStrategyData(processedRecord);
      setShowDetailStrategy(true);
    } catch (error) {
      console.error("Failed to fetch strategy detail:", error);
    }
  };

  // 处理返回列表
  const handleBackToList = async (isUpdate = false) => {
    setShowAddStrategy(false);
    setShowDetailStrategy(false);
    setIsEditMode(false);
    setCurrentStrategyData(null);
    if (isUpdate) {
      await fetchCustomModelList();
    }
  };

  // 处理删除
  const handleDeleteStrategy = (record) => {
    setRecordToDelete(record);
    setDeleteModalVisible(true);
  };

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length > 0) {
      setRecordToDelete({ id: selectedRowKeys });
      setDeleteModalVisible(true);
    }
  };

  // 确认删除
  const confirmDelete = async () => {
    try {
      const ids = Array.isArray(recordToDelete.id)
        ? recordToDelete.id
        : [recordToDelete.id];
      await delManyCustomModelByIds(ids);
      message.success("删除成功");
      setDeleteModalVisible(false);
      setRecordToDelete(null);
      setSelectedRowKeys([]); // 清空选中状态
      // 刷新列表
      fetchCustomModelList();
    } catch (error) {
      console.error("Failed to delete strategy:", error);
      message.error("删除失败");
    }
  };

  return (
    <Layout className="strategy-config-container">
      {!showAddStrategy && !showDetailStrategy ? (
        <>
          <Sider width={300} className="strategy-sider">
            <div className="strategy-header">
              <Tabs
                activeKey="strategyType"
                items={[
                  {
                    key: "strategyType",
                    label: <span>策略类型</span>,
                  },
                ]}
              />
            </div>
            <div className="strategy-tree-scroll">
              <Tree
                showIcon
                loading={loading}
                className="startegy-tree"
                onSelect={onTreeSelect}
                selectedKeys={selectedKeys}
                treeData={treeData}
                blockNode
              />
            </div>

          </Sider>
          <Content className="strategy-content">
            <div className="search-filter">
              <Space size="middle" className="search-left">
                <Form form={searchForm} layout="inline" onFinish={handleSearch}>
                  <Form.Item name="keyword" label="策略名称">
                    <Input
                      placeholder="请输入策略名称"
                      style={{ width: 200 }}
                      allowClear
                    />
                  </Form.Item>
                  <Form.Item>
                    <Space>
                      <Button type="primary" htmlType="submit">
                        搜索
                      </Button>
                      <Button onClick={handleReset}>重置</Button>
                    </Space>
                  </Form.Item>
                </Form>
              </Space>
              <Space size="middle" className="search-right">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                >
                  新增
                </Button>
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleBatchDelete}
                  disabled={selectedRowKeys.length === 0}
                >
                  批量删除
                </Button>
              </Space>
            </div>
            <Table
              columns={columns}
              loading={loading}
              dataSource={strategyConfigData}
              className="strategy-table"
              rowKey="id"
              pagination={{
                total: total,
                current: searchParams.page + 1,
                pageSize: searchParams.size,
                showTotal: (total) => `共 ${total} 条`,
              }}
              onChange={handleTableChange}
              rowSelection={{
                type: "checkbox",
                selectedRowKeys: selectedRowKeys,
                onChange: (newSelectedRowKeys) => {
                  setSelectedRowKeys(newSelectedRowKeys);
                },
              }}
              scroll={{ x: "max-content" }}
              tableLayout="auto"
            />
          </Content>
          <Modal
            title="确认删除"
            open={deleteModalVisible}
            onOk={confirmDelete}
            onCancel={() => {
              setDeleteModalVisible(false);
              setSelectedRowKeys([]);
              setRecordToDelete(null);
            }}
          >
            <p>
              确定要删除
              {Array.isArray(recordToDelete?.id)
                ? `选中的${selectedRowKeys.length}个`
                : `名称为 "${recordToDelete?.name}" 的`}
              策略吗？ 此操作不可恢复。
            </p>
          </Modal>
        </>
      ) : showAddStrategy ? (
        <div className="add-strategy-content">
          <div
            style={{
              marginBottom: 16,
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <span style={{ fontSize: 16, fontWeight: 600 }}>{`${isEditMode ? `编辑策略-${currentStrategyData?.name}` : "新增策略"
              }`}</span>
            <CloseOutlined
              onClick={handleBackToList}
              style={{ fontSize: "20px", cursor: "pointer" }}
            />
          </div>
          <AddUpdataStrategy
            handleBackToList={handleBackToList}
            configType={treeData}
            isEditMode={isEditMode}
            strategyData={currentStrategyData}
          />
        </div>
      ) : (
        <div className="add-strategy-content">
          <div
            style={{
              marginBottom: 16,
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <span style={{ fontSize: 16, fontWeight: 600 }}>查看策略</span>
            <RollbackOutlined
              onClick={handleBackToList}
              style={{ fontSize: "20px", cursor: "pointer" }}
            />
          </div>
          <DetailStrategy
            handleBackToList={handleBackToList}
            configType={treeData}
            strategyData={currentStrategyData}
          />
        </div>
      )}
      <Modal
        title="下发配置"
        closable={{ 'aria-label': 'Custom Close Button' }}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        centered
      >
        <Form
          form={issueForm}
          layout="vertical"
          initialValues={{
            strategyName: currentStrategyData?.name,
            locationType: "0" // 默认选择行政区域
          }}
        >
          <Form.Item
            label="配置策略"
            name="strategyName"
          >
            <Input disabled defaultValue={currentStrategyData?.name} />
          </Form.Item>
          <Form.Item
            label="地理位置"
            name="location"
            rules={[{ required: true, message: '请选择地理位置' }]}
          >
            <TreeSelect
              placeholder="请选择地理位置"
              treeData={geographyTree}
              fieldNames={{ label: 'groupName', value: 'id', children: 'children' }}
              treeDefaultExpandAll
              allowClear
            />
          </Form.Item>
          <Form.Item
            label="位置类型"
            name="locationType"
            rules={[{ required: true, message: '请选择位置类型' }]}
          >
            <Select placeholder="请选择位置类型">
              <Select.Option value="0">行政区域</Select.Option>
              <Select.Option value="1">分组位置</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Layout>
  );
};

export default StrategyConfig;
