import fetch from './fetch';
import {API_PREFIX} from "./constant"
import { method } from 'lodash';

// 分页获取驱鼠器列表
export const getDeviceRepellentList = (params) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/devices`,
    method: 'GET',
    params
  })
}

// 更新驱鼠器
export const updateDeviceRepellent = (data) => {
    return fetch({
      url: `${API_PREFIX["device-service"]}/devices`,
      method: "PUT",
      data
    })
}

// 批量删除
export const delManyDeviceRepellentByIds = (ids) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/devices/deleteByIds`,
    method: "post",
    data: ids
  })
}

// 获取设备配置列表
export const deviceConfigList = (deviceId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/devices/getAllDeviceConfig/${deviceId}`,
    method:"get"
  })
}


// 获取驱鼠器列表
export const getDeviceListByCameraId = (cameraId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/devices/camera/${cameraId}`,
    method: 'GET',
  })
}

// 
export const openOrCloseDevice = (params) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/devices/openUlOrLedOrSound`,
    method: "get",
    params
  })
}


// 新增版本配置
export const andVersionConfig = (deviceId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/devices/addDeviceConfig`,
    method: "get",
    params:{
      deviceId
    }
  })
}


// 应用版本
export const applyVersionConfig = (deviceConfigId,deviceId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/devices/applyDeviceConfig`,
    method: "get",
    params:{
      deviceConfigId,
      deviceId
    }
  })
}


// 根据版本获取配置
export const getDeviceConfigByVersion = (customModelId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/devices/getDeviceConfig/${customModelId}`,
    method: "get",
  })
}


// 修改配置
export const updateConfig = (deviceConfigId,customModelId,isModify=false,data={}) => {
  const url = customModelId 
    ? `${API_PREFIX["device-service"]}/devices/updateDeviceConfig/${deviceConfigId}/${customModelId}/${isModify}`
    : `${API_PREFIX["device-service"]}/devices/updateDeviceConfig/${deviceConfigId}`;
    
  return fetch({
    url,
    method: "post",
    data
  })
}



// 批量删除
export const oneDeviceUpgrade = (data) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/devices/oneDeviceUpgrade`,
    method: "post",
    data
  })
}

//更新到最新版本
export const upgradeDeviceMaxVersion = (deviceId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/devices/upgradeDeviceMaxVersion`,
    method: 'GET',
    params:{
      deviceId
    }
  });
}

//模拟驱鼠
export const simulateRodentControl = (deviceId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/devices/simulateDrive`,
    method:'get',
    params:{
      deviceId
    }
  })
}

//驱鼠器编号模糊查询
export const getDeviceLikeDeviceNo = (deviceNo) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/devices/getDeviceLikeDeviceNo`,
    method:'get',
    params:{
      deviceNo
    }
  })
}

// 禁用摄像头
export const disableCamera = (params) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/devices/deactivateDevice`,
    method:'get',
    params:params
  })
}

//获取生命周期
export const getLifeCycle = (deviceId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/device-lifecycle/device/${deviceId}`,
    method:'get'
  })
}
