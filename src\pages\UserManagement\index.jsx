import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Table,
  Breadcrumb,
  Input,
  Select,
  Modal,
  Form,
  Radio,
  Upload,
  Tooltip,
  Statistic,
  message,
  DatePicker,
  TreeSelect,
  Checkbox,
  Dropdown,
  Divider,
  Tag
} from "antd";
import {
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
  SearchOutlined,
  UserOutlined,
  TeamOutlined,
  LockOutlined,
  UnlockOutlined,
  EditOutlined,
  DeleteOutlined,
  KeyOutlined,
  SettingOutlined,
  InboxOutlined,
  SafetyCertificateOutlined,
  DownOutlined,
  CaretRightOutlined,
  CaretDownOutlined
} from "@ant-design/icons";
import dayjs from "dayjs";
import { usrManagementApi } from "../../service/userManagement";
import "./styles.css";
import editPermissionImg from "../../img/编辑权限.png";

const { Search } = Input;
const { Option } = Select;

// 添加自定义树节点组件
const CustomTreeNode = ({ node, level = 0, checkedKeys, halfCheckedKeys, onNodeCheck }) => {
  const isChecked = checkedKeys.includes(node.value);
  const isHalfChecked = halfCheckedKeys.includes(node.value);
  const hasChildren = node.children && node.children.length > 0;
  const paddingLeft = level * 24;
  const [expanded, setExpanded] = useState(true);

  const toggleExpand = (e) => {
    e.stopPropagation();
    setExpanded(!expanded);
  };

  return (
    <div className="custom-tree-node">
      <div 
        className="node-content" 
        style={{ 
          display: 'flex',
          alignItems: 'center',
          padding: '8px 0',
        }}
      >
        {hasChildren && (
          <span 
            onClick={toggleExpand} 
            style={{ 
              marginRight: '8px', 
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            {expanded ? <CaretDownOutlined /> : <CaretRightOutlined />}
          </span>
        )}
        <Checkbox 
          checked={isChecked}
          indeterminate={isHalfChecked}
          onChange={(e) => onNodeCheck(node, e.target.checked)}
        >
          <Space>
            <span className="permission-title">{node.title}</span>
            {node.type && (
              <Tag color={level === 0 ? "blue" : level === 1 ? "cyan" : "green"}>
                {node.type}
              </Tag>
            )}
            {node.description && (
              <span style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: level === 0 ? '14px' : '12px' }}>
                {node.description}
              </span>
            )}
          </Space>
        </Checkbox>
      </div>
      
      {hasChildren && expanded && (
        <div 
          className="node-children" 
          style={{ 
            marginLeft: '40px',
            marginTop: '4px',
            paddingLeft: '16px',
            position: 'relative'
          }}
        >
          <Row gutter={[8, 8]} style={{ display: 'flex', flexWrap: 'wrap' }}>
            {node.children.map(child => (
              <Col 
                key={child.value} 
                span={child.children && child.children.length > 0 ? 24 : 3}
              >
                <CustomTreeNode 
                  node={child}
                  level={level + 1}
                  checkedKeys={checkedKeys}
                  halfCheckedKeys={halfCheckedKeys}
                  onNodeCheck={onNodeCheck}
                />
              </Col>
            ))}
          </Row>
        </div>
      )}
    </div>
  );
};

// 添加自定义树组件
const CustomTree = ({ treeData, checkedKeys, halfCheckedKeys, onCheck }) => {
  return (
    <div className="custom-tree">
      {treeData.map(node => (
        <CustomTreeNode 
          key={node.value}
          node={node}
          checkedKeys={checkedKeys}
          halfCheckedKeys={halfCheckedKeys}
          onNodeCheck={onCheck}
        />
      ))}
    </div>
  );
};

const UserManagement = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [filters, setFilters] = useState({
    status: "all",
    accountType: "all",
    tenantId: "all",
    content: "",
  });
  const [resetPasswordModalVisible, setResetPasswordModalVisible] = useState(false);
  const [resetPasswordForm] = Form.useForm();
  const [selectedUser, setSelectedUser] = useState(null);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importFile, setImportFile] = useState(null);
  const [importLoading, setImportLoading] = useState(false);
  const [statisticsData, setStatisticsData] = useState({});
  const [tenantOptions, setTenantOptions] = useState([]);
  const [orgTreeData, setOrgTreeData] = useState([]);
  const [roleOptions, setRoleOptions] = useState([]);
  const [permissionModalVisible, setPermissionModalVisible] = useState(false);
  const [permissionForm] = Form.useForm();
  const [permissionTreeData, setPermissionTreeData] = useState([]);
  const [selectedUserData, setSelectedUserData] = useState(null);
  const [checkedPermissions, setCheckedPermissions] = useState([]);
  const [halfCheckedKeys, setHalfCheckedKeys] = useState([]);
  const [permissionDropdownVisible, setPermissionDropdownVisible] = useState(false);
  const [selectedPermissionTags, setSelectedPermissionTags] = useState([]);

  // 获取租户下拉列表
  const fetchTenantOptions = async (tenantId) => {
    try {
      const response = await usrManagementApi.getTenantDropdown(tenantId);
      if (response.code === 200) {
        setTenantOptions(response.data || []);
      }
    } catch (error) {
      console.error("获取租户列表失败:", error);
    }
  };

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      const response = await usrManagementApi.getUserStatistics();
      if (response.code === 200) {
        setStatisticsData(response.data);
      }
    } catch (error) {
      console.error("获取统计数据失败:", error);
    }
  };

  // 获取用户列表数据
  const fetchUserList = async (params = {}) => {
    setLoading(true);
    try {
      const response = await usrManagementApi.getUserPage({
        pageNum: params.current || 1,
        pageSize: params.pageSize || 10,
        status: params.status || "",
        accountType: params.accountType || "",
        tenantId: params.tenantId || "",
        content: params.content || "",
      });

      if (response.code === 200) {
        setUsers(response.data.content || []);
        setPagination({
          ...pagination,
          total: response.data.totalElements,
          current: response.data.number + 1,
          pageSize: response.data.size,
        });
      }
    } catch (error) {
      console.error("获取用户列表失败:", error);
    } finally {
      setLoading(false);
    }
  };

  // 获取组织树数据
  const fetchOrgTree = async () => {
    try {
      const response = await usrManagementApi.getOrgTree();
      if (response.code === 200) {
        setOrgTreeData(response.data || []);
      }
    } catch (error) {
      console.error("获取组织树失败:", error);
    }
  };

  // 获取角色下拉列表
  const fetchRoleOptions = async () => {
    try {
      const response = await usrManagementApi.getRoleDropdown();
      if (response.code === 200) {
        setRoleOptions(response.data || []);
      }
    } catch (error) {
      console.error("获取角色列表失败:", error);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    fetchUserList();
    fetchStatistics();
    fetchTenantOptions();
    fetchOrgTree();
    fetchRoleOptions();
  }, []);

  // 处理表格变化
  const handleTableChange = (newPagination, filters) => {
    fetchUserList({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
      ...filters,
    });
  };

  // Mock data for statistics
  const statistics = [
    {
      title: "总用户",
      value: statisticsData.totalNum,
      subTitle: "本月新增：",
      subValue: statisticsData.monthAddNum,
      icon: <TeamOutlined style={{ fontSize: 24, color: "#1890ff" }} />,
    },
    {
      title: "活跃用户",
      value: statisticsData.activeNum,
      subTitle: "今日登录：",
      subValue: statisticsData.todayLogInNum,
      icon: <UserOutlined style={{ fontSize: 24, color: "#52c41a" }} />,
    },
    {
      title: "异常账户",
      value: statisticsData.lockedNum,
      subTitle: "禁用：",
      subValue: statisticsData.disabledNum,
      icon: <LockOutlined style={{ fontSize: 24, color: "#faad14" }} />,
    },
    {
      title: "管理员",
      value: statisticsData.adminNum,
      subTitle: "系统用户：",
      subValue: statisticsData.systemNum,
      icon: <SettingOutlined style={{ fontSize: 24, color: "#722ed1" }} />,
    },
  ];

  // 处理锁定/解锁状态切换
  const handleLockStatusChange = async (record) => {
    if (record.email === "<EMAIL>") {
      message.warning("该用户不可锁定/解锁");
      return;
    }
    try {
      const response =
        record.status === "locked"
          ? await usrManagementApi.unlockUsers([record.userId])
          : await usrManagementApi.lockUsers([record.userId]);

      if (response.code === 200) {
        message.success(
          record.status === "locked" ? "用户已解锁" : "用户已锁定"
        );
        // 刷新用户列表
        fetchUserList({
          current: pagination.current,
          pageSize: pagination.pageSize,
        });
        setSelectedRowKeys([]);
        // 刷新统计数据
        fetchStatistics();
      } else {
        message.error(
          response.message ||
            (record.status === "locked" ? "解锁用户失败" : "锁定用户失败")
        );
      }
    } catch (error) {
      console.error(
        record.status === "locked" ? "解锁用户失败:" : "锁定用户失败:",
        error
      );
      message.error(
        record.status === "locked" ? "解锁用户失败" : "锁定用户失败"
      );
    }
  };

  // 处理编辑按钮点击
  const handleEdit = async (record) => {
    if (record.status === "locked") {
      message.warning("锁定状态的用户不能编辑");
      return;
    }
    if (record.email === "<EMAIL>") {
      message.warning("该用户不可编辑");
      return;
    }
    try {
      const [userResponse, orgResponse, roleResponse] = await Promise.all([
        usrManagementApi.getUserDetail(record.userId),
        usrManagementApi.getOrgTree(),
        usrManagementApi.getRoleDropdown(),
      ]);

      if (userResponse.code === 200) {
        const userData = userResponse.data;

        // 设置组织树和角色选项数据
        const orgTreeData = orgResponse.data || [];
        const roleOptions = roleResponse.data || [];
        setOrgTreeData(orgTreeData);
        setRoleOptions(roleOptions);

        // 查找用户的组织和角色，确保它们存在于选项中
        const userOrgId = userData.orgUnitIds?.[0];
        const userRoleId = userData.roleIds?.[0];

        editForm.setFieldsValue({
          ...userData,
          birthDate: userData.birthDate ? dayjs(userData.birthDate) : null,
          orgUnitName: userOrgId ? { value: userOrgId } : undefined,
          roleId: userRoleId ? { value: userRoleId } : undefined,
        });

        setEditModalVisible(true);
        setCurrentUserId(userData.userId);
      }
    } catch (error) {
      console.error("获取用户详情失败:", error);
    }
  };

  // 处理编辑表单提交
  const handleEditUser = async (values) => {
    try {
      const params = {
        ...values,
        userId: currentUserId,
        username: values.username,
        birthDate: values.birthDate?.format("YYYY-MM-DD"),
        orgUnitIds: values.orgUnitName ? [values.orgUnitName.value] : [],
        roleIds: values.roleId ? [values.roleId.value] : [],
        orgUnitName: undefined,
        roleId: undefined,
      };

      const response = await usrManagementApi.updateUser(params);

      if (response.code === 200) {
        message.success("用户信息更新成功！");
        setEditModalVisible(false);
        setCurrentUserId(null);
        editForm.resetFields();
        // 刷新用户列表
        fetchUserList({
          current: pagination.current,
          pageSize: pagination.pageSize,
        });
        // 刷新统计数据
        fetchStatistics();
      }else if (response.code === 400) {
        message.error(response.message);
      }
    } catch (error) {
      console.error("更新用户信息失败:", error);
    }
  };

  // 处理重置密码
  const handleResetPassword = async (values) => {
    try {
      const params = {
        userId: selectedUser.userId,
        password: values.password,
      };

      const response = await usrManagementApi.resetPassword(params);

      if (response.code === 200) {
        message.success("密码重置成功！");
        setResetPasswordModalVisible(false);
        resetPasswordForm.resetFields();
      }
    } catch (error) {
      console.error("密码重置失败:", error);
    }
  };

  // 打开重置密码弹窗
  const showResetPasswordModal = async (record) => {
    if (record.status === "locked") {
      message.warning("锁定状态的用户不能重置密码");
      return;
    }
    if (record.email === "<EMAIL>") {
      message.warning("该用户不可重置密码");
      return;
    }
    try {
      const response = await usrManagementApi.getUserDetail(record.userId);
      if (response.code === 200) {
        const userData = response.data;
        setSelectedUser(record);
        resetPasswordForm.setFieldsValue({
          username: userData.username,
        });
        setResetPasswordModalVisible(true);
      }
    } catch (error) {
      console.error("获取用户信息失败:", error);
    }
  };

  // 处理删除用户
  const handleDeleteUser = async (userId) => {
    // 检查用户是否处于锁定状态
    const user = users.find((u) => u.userId === userId);
    if (user && user.status === "locked") {
      message.warning("锁定状态的用户不能删除");
      return;
    }
    if (user && user.email === "<EMAIL>") {
      message.warning("该用户不可删除");
      return;
    }

    try {
      const response = await usrManagementApi.deleteUsers([userId]);
      if (response.code === 200) {
        message.success("用户删除成功");
        setSelectedRowKeys([]);
        // 刷新用户列表
        fetchUserList({
          current: pagination.current,
          pageSize: pagination.pageSize,
        });
        // 刷新统计数据
        fetchStatistics();
      }
    } catch (error) {
      console.error("删除用户失败:", error);
    }
  };

  // 处理编辑权限按钮点击
  const handleAddPermission = async (record) => {
    if (record.email === "<EMAIL>") {
      message.warning("该用户不可编辑权限");
      return;
    }
    try {
      // 获取用户详情和用户权限
      const [userResponse, userPermissionResponse] = await Promise.all([
        usrManagementApi.getUserDetail(record.userId),
        usrManagementApi.getUserPermissions(record.userId),
      ]);

      if (userResponse.code === 200 && userPermissionResponse.code === 200) {
        const userData = userResponse.data;
        const userPermissions = userPermissionResponse.data;
        setSelectedUserData(userData);

        // 获取权限树数据
        const permissionResponse = await usrManagementApi.getPermissionTree({
          permissionId: "",
          roleId: userData.roleIds?.[0] || "",
        });

        // 转换权限树数据
        const transformedPermissionTree = permissionResponse.data?.map((item) => ({
          title: item.name,
          value: item.id,
          subValue: item.id,
          children: item.childrenList?.map((child) => ({
            title: child.name,
            value: item.id + "-" + child.id,
            subValue: item.id + "-" + child.id,
            children: child.childrenList?.map((grandChild) => ({
              title: grandChild.name,
              value: child.id + "-" + grandChild.id,
              subValue: child.id + "-" + grandChild.id,
            })),
          })),
        })) || [];

        setPermissionTreeData(transformedPermissionTree);

        // 处理用户权限数据，构建选中的权限列表
        const permissions = new Set();
        const parentPermissionMap = new Map();
        const newHalfCheckedKeys = new Set();

        // 首先添加所有子权限
        userPermissions.forEach(permission => {
          if (permission.parentPermissionId) {
            const permissionKey = permission.parentPermissionId + "-" + permission.permissionId;
            permissions.add(permissionKey);
            
            // 记录父权限ID
            if (!parentPermissionMap.has(permission.parentPermissionId)) {
              parentPermissionMap.set(permission.parentPermissionId, new Set());
            }
            parentPermissionMap.get(permission.parentPermissionId).add(permission.permissionId);
          }
        });

        // 检查并添加父权限
        transformedPermissionTree.forEach(parent => {
          const childPermissions = parentPermissionMap.get(parent.value);
          if (childPermissions) {
            // 检查是否所有子权限都被选中
            const allChildrenSelected = parent.children?.every(child => 
              childPermissions.has(child.subValue.split('-')[1])
            );
            
            // 检查是否有任何子权限被选中
            const anyChildSelected = parent.children?.some(child => 
              childPermissions.has(child.subValue.split('-')[1])
            );
            
            if (allChildrenSelected) {
              permissions.add(parent.value);
            } else if (anyChildSelected) {
              newHalfCheckedKeys.add(parent.value);
            }
          }
        });
        
        setCheckedPermissions(Array.from(permissions));
        setHalfCheckedKeys(Array.from(newHalfCheckedKeys));
        
        permissionForm.setFieldsValue({
          username: userData.username,
          permissions: Array.from(permissions),
          type: userPermissions[0]?.grantType || "grant",
          reason: userPermissions[0]?.reason || "",
        });

        setPermissionModalVisible(true);
      } else {
        message.error(
          userResponse.message ||
            userPermissionResponse.message ||
            "获取用户信息失败"
        );
      }
    } catch (error) {
      console.error("获取用户信息失败:", error);
    }
  };

  // 处理权限节点选择
  const handleNodeCheck = (node, checked) => {
    // 创建新的选中键列表
    let newCheckedKeys = [...checkedPermissions];
    
    // 获取所有子节点的value
    const getAllChildValues = (item) => {
      let values = [item.value];
      if (item.children && item.children.length > 0) {
        item.children.forEach(child => {
          values = [...values, ...getAllChildValues(child)];
        });
      }
      return values;
    };
    
    // 获取父节点路径
    const getParentPath = (data, targetValue, path = []) => {
      for (const item of data) {
        if (item.value === targetValue) {
          return path;
        }
        
        if (item.children && item.children.length > 0) {
          const foundPath = getParentPath(item.children, targetValue, [...path, item]);
          if (foundPath.length) {
            return foundPath;
          }
        }
      }
      
      return [];
    };
    
    // 检查节点下是否有任何子节点被选中
    const hasCheckedDescendant = (item, checkedValues) => {
      if (!item.children || item.children.length === 0) {
        return checkedValues.includes(item.value);
      }
      
      return item.children.some(child => {
        const childChecked = checkedValues.includes(child.value);
        const descendantChecked = child.children && child.children.length > 0 ? 
                                  hasCheckedDescendant(child, checkedValues) : false;
        return childChecked || descendantChecked;
      });
    };
    
    // 检查节点及其所有后代是否都被选中
    const areAllDescendantsChecked = (item, checkedValues) => {
      // 检查当前节点是否选中
      if (!checkedValues.includes(item.value)) {
        return false;
      }
      
      // 如果没有子节点，则返回true
      if (!item.children || item.children.length === 0) {
        return true;
      }
      
      // 检查所有子节点是否都被选中
      return item.children.every(child => areAllDescendantsChecked(child, checkedValues));
    };
    
    if (checked) {
      // 选中节点逻辑 - 选中节点及其所有子节点
      const childValues = getAllChildValues(node);
      newCheckedKeys = [...new Set([...newCheckedKeys, ...childValues])];
      
      // 处理父节点选中状态
      const parentNodes = getParentPath(permissionTreeData, node.value);
      
      // 更新每个父节点的状态
      parentNodes.forEach(parent => {
        // 获取所有直接子节点的value
        const childValues = parent.children.map(child => child.value);
        
        // 检查是否所有直接子节点都被选中
        const allDirectChildrenChecked = childValues.every(value => newCheckedKeys.includes(value));
        
        // 检查是否所有子节点及其后代都被选中
        const allDescendantsOfChildrenChecked = parent.children.every(child => {
          if (!child.children || child.children.length === 0) {
            return newCheckedKeys.includes(child.value);
          }
          return areAllDescendantsChecked(child, newCheckedKeys);
        });

        // 检查是否有任何子节点被选中（包括深层后代）
        const anyChildChecked = parent.children.some(child => 
          hasCheckedDescendant(child, newCheckedKeys)
        );
        
        if (allDirectChildrenChecked && allDescendantsOfChildrenChecked) {
          // 如果所有子节点和所有后代都被选中，设置父节点为完全选中状态
          if (!newCheckedKeys.includes(parent.value)) {
            newCheckedKeys.push(parent.value);
          }
          setHalfCheckedKeys(prevState => prevState.filter(value => value !== parent.value));
        } else if (anyChildChecked) {
          // 如果有任何子节点被选中但不是所有，则设置父节点为半选状态
          newCheckedKeys = newCheckedKeys.filter(value => value !== parent.value);
          if (!halfCheckedKeys.includes(parent.value)) {
            setHalfCheckedKeys(prevState => [...prevState, parent.value]);
          }
        } else {
          // 如果没有子节点被选中，取消父节点的选中和半选状态
          newCheckedKeys = newCheckedKeys.filter(value => value !== parent.value);
          setHalfCheckedKeys(prevState => prevState.filter(value => value !== parent.value));
        }
      });
    } else {
      // 取消选中节点逻辑 - 取消选中节点及其所有子节点
      const childValues = getAllChildValues(node);
      newCheckedKeys = newCheckedKeys.filter(value => !childValues.includes(value));
      
      // 处理父节点选中状态
      const parentNodes = getParentPath(permissionTreeData, node.value);
      
      // 更新每个父节点的状态
      parentNodes.forEach(parent => {
        // 获取所有直接子节点的value
        const childValues = parent.children.map(child => child.value);
        
        // 检查是否所有直接子节点都被选中
        const allDirectChildrenChecked = childValues.every(value => newCheckedKeys.includes(value));
        
        // 检查是否所有子节点及其后代都被选中
        const allDescendantsOfChildrenChecked = parent.children.every(child => {
          if (!child.children || child.children.length === 0) {
            return newCheckedKeys.includes(child.value);
          }
          return areAllDescendantsChecked(child, newCheckedKeys);
        });

        // 检查是否有任何子节点被选中（包括深层后代）
        const anyChildChecked = parent.children.some(child => 
          hasCheckedDescendant(child, newCheckedKeys)
        );
        
        if (allDirectChildrenChecked && allDescendantsOfChildrenChecked) {
          // 如果所有子节点和所有后代都被选中，设置父节点为完全选中状态
          if (!newCheckedKeys.includes(parent.value)) {
            newCheckedKeys.push(parent.value);
          }
          setHalfCheckedKeys(prevState => prevState.filter(value => value !== parent.value));
        } else if (anyChildChecked) {
          // 如果有任何子节点被选中但不是所有，则设置父节点为半选状态
          newCheckedKeys = newCheckedKeys.filter(value => value !== parent.value);
          if (!halfCheckedKeys.includes(parent.value)) {
            setHalfCheckedKeys(prevState => [...prevState, parent.value]);
          }
        } else {
          // 如果没有子节点被选中，取消父节点的选中和半选状态
          newCheckedKeys = newCheckedKeys.filter(value => value !== parent.value);
          setHalfCheckedKeys(prevState => prevState.filter(value => value !== parent.value));
        }
      });
    }
    
    setCheckedPermissions(newCheckedKeys);
  };

  // 获取选中权限的标签组件
  const getSelectedPermissionTags = () => {
    if (checkedPermissions.length === 0) {
      return null;
    }
    
    const permissionMap = new Map();
    
    const buildPermissionMap = (nodes) => {
      nodes.forEach(node => {
        permissionMap.set(node.value, {
          title: node.title,
          type: node.type
        });
        
        if (node.children && node.children.length > 0) {
          buildPermissionMap(node.children);
        }
      });
    };
    
    buildPermissionMap(permissionTreeData);
    
    return (
      <div 
        style={{ 
          maxHeight: '100px', 
          overflowY: 'auto',
          padding: '5px',
          display: 'flex',
          flexWrap: 'wrap',
          gap: '5px'
        }}
      >
        {checkedPermissions.map(key => {
          const permission = permissionMap.get(key);
          if (!permission) return null;
          
          return (
            <Tag
              key={key}
              closable
              style={{ 
                display: 'flex',
                alignItems: 'center',
                margin: '2px',
                backgroundColor: '#f5f5f5'
              }}
              onClose={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleRemovePermissionTag(key);
              }}
            >
              {permission.title}
            </Tag>
          );
        })}
      </div>
    );
  };

  // 处理移除权限标签
  const handleRemovePermissionTag = (key) => {
    const newCheckedKeys = checkedPermissions.filter(k => k !== key);
    setCheckedPermissions(newCheckedKeys);
    
    // 处理父节点状态
    const getParentPath = (data, targetKey, path = []) => {
      for (const item of data) {
        if (item.value === targetKey) {
          return path;
        }
        
        if (item.children && item.children.length > 0) {
          const foundPath = getParentPath(item.children, targetKey, [...path, item]);
          if (foundPath.length) {
            return foundPath;
          }
        }
      }
      
      return [];
    };
    
    // 获取节点的所有子节点值
    const getAllChildValues = (item) => {
      let values = [item.value];
      if (item.children && item.children.length > 0) {
        item.children.forEach(child => {
          values = [...values, ...getAllChildValues(child)];
        });
      }
      return values;
    };
    
    // 检查节点下是否有任何子节点被选中（递归检查整个子树）
    const hasCheckedDescendant = (item, checkedKeys) => {
      if (!item.children || item.children.length === 0) {
        return checkedKeys.includes(item.value);
      }
      
      return item.children.some(child => {
        const childChecked = checkedKeys.includes(child.value);
        const descendantChecked = child.children && child.children.length > 0 ? 
                                hasCheckedDescendant(child, checkedKeys) : false;
        return childChecked || descendantChecked;
      });
    };
    
    // 检查所有直接子节点是否都被选中
    const areAllChildrenChecked = (item, checkedKeys) => {
      if (!item.children || item.children.length === 0) {
        return true;
      }
      
      return item.children.every(child => checkedKeys.includes(child.value));
    };
    
    const parentNodes = getParentPath(permissionTreeData, key);
    
    // 更新每个父节点的状态
    let newHalfCheckedKeys = [...halfCheckedKeys];
    
    parentNodes.forEach(parent => {
      // 检查是否有任何子节点或后代被选中
      const anyChildChecked = hasCheckedDescendant(parent, newCheckedKeys);
      
      if (!anyChildChecked) {
        // 如果没有任何子节点被选中，则移除父节点的选中和半选状态
        newHalfCheckedKeys = newHalfCheckedKeys.filter(k => k !== parent.value);
      } else {
        // 如果有子节点被选中
        const allDirectChildrenChecked = areAllChildrenChecked(parent, newCheckedKeys);
        
        if (allDirectChildrenChecked) {
          // 如果所有直接子节点都被选中，则设置父节点为选中状态
          if (!newCheckedKeys.includes(parent.value)) {
            setCheckedPermissions(prevState => [...prevState, parent.value]);
          }
          newHalfCheckedKeys = newHalfCheckedKeys.filter(k => k !== parent.value);
        } else {
          // 如果只有部分子节点被选中，则设置为半选状态
          if (!newHalfCheckedKeys.includes(parent.value)) {
            newHalfCheckedKeys = [...newHalfCheckedKeys, parent.value];
          }
        }
      }
    });
    
    setHalfCheckedKeys(newHalfCheckedKeys);
  };

  // 权限下拉内容
  const permissionDropdownContent = (
    <div className="permission-dropdown-content" style={{ padding: '10px', minWidth: '300px' }}>
      <div style={{ marginBottom: '10px' }}>
        <Button 
          type='primary' 
          size="small" 
          onClick={() => {
            const getAllValues = (nodes) => {
              let values = [];
              nodes.forEach(node => {
                values.push(node.value);
                if (node.children && node.children.length > 0) {
                  values = [...values, ...getAllValues(node.children)];
                }
              });
              return values;
            };
            const allValues = getAllValues(permissionTreeData);
            setCheckedPermissions(allValues);
            setHalfCheckedKeys([]);
          }}
          style={{ marginRight: '8px' }}
        >
          全选
        </Button>
        <Button 
          size="small" 
          onClick={() => {
            setCheckedPermissions([]);
            setHalfCheckedKeys([]);
          }}
        >
          清空
        </Button>
      </div>
      <Divider style={{ margin: '8px 0' }} />
      {permissionTreeData.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <div className="loading-spinner"></div>
          <p>正在加载权限数据...</p>
        </div>
      ) : (
        <CustomTree 
          treeData={permissionTreeData}
          checkedKeys={checkedPermissions}
          halfCheckedKeys={halfCheckedKeys}
          onCheck={handleNodeCheck}
        />
      )}
    </div>
  );

  // 处理权限表单提交
  const handlePermissionSubmit = async () => {
    try {
      const values = await permissionForm.validateFields();
      const permissionIds = [];
      
      // 合并完全选中和半选中的键
      const allPermissions = [...new Set([...checkedPermissions, ...halfCheckedKeys])];
      
      allPermissions.forEach(permission => {
        if (permission.includes('-')) {
          permissionIds.push(...permission.split('-'));
        } else {
          permissionIds.push(permission);
        }
      });

      const data = {
        userId: selectedUserData.userId,
        permissionIds: [...new Set(permissionIds)],
        tenantId: selectedUserData.tenantId,
        grantType:'grant',
        reason: values.reason,
      };
      
      const response = await usrManagementApi.grantUserPermission(data);
      if (response.code === 200) {
        message.success("权限授予成功");
        setPermissionModalVisible(false);
        permissionForm.resetFields();
        setCheckedPermissions([]);
        setHalfCheckedKeys([]);
        fetchUserList({
          current: pagination.current,
          pageSize: pagination.pageSize,
        });
      }
    } catch (error) {
      console.error("授予权限失败:", error);
    }
  };

  // 处理权限弹窗取消
  const handlePermissionCancel = () => {
    setPermissionModalVisible(false);
    permissionForm.resetFields();
    setCheckedPermissions([]);
    setHalfCheckedKeys([]);
  };

  // Table columns
  const columns = [
    {
      title: "用户名",
      dataIndex: "username",
      key: "username",
      width: "10%",
      align: "center",
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "昵称",
      dataIndex: "nickname",
      key: "nickname",
      width: "10%",
      align: "center",
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "性别",
      dataIndex: "gender",
      key: "gender",
      width: "5%",
      align: "center",
      render: (text) => {
        const genderMap = {
          female: "女",
          male: "男",
          unknown: "未知",
        };
        return genderMap[text] || "-";
      },
    },
    {
      title: "组织",
      dataIndex: "orgUnitName",
      key: "orgUnitName",
      width: "10%",
      align: "center",
      render: (text) => <span>{text && text !== "unknown" ? text : "-"}</span>,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: "10%",
      align: "center",
      render: (status) => {
        const statusMap = {
          active: "活跃",
          locked: "锁定",
          disabled: "禁用",
          pending: "待验证",
        };
        return <span>{statusMap[status] || "-"}</span>;
      },
    },
    {
      title: "邮箱",
      dataIndex: "email",
      key: "email",
      width: "10%",
      align: "center",
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "手机号",
      dataIndex: "phoneNumber",
      key: "phoneNumber",
      width: "10%",
      align: "center",
      render: (text) => <span>{text || "-"}</span>,
    },

    {
      title: "操作",
      key: "action",
      align: "center",
      render: (_, record) => (
        <Space wrap>
          <Tooltip
            title={
              record.status === "locked"
                ? "锁定状态不能编辑"
                : record.email === "<EMAIL>"
                  ? "该用户不可编辑"
                  : "编辑"
            }
          >
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              disabled={record.status === "locked" || record.email === "<EMAIL>"}
            />
          </Tooltip>
          <Tooltip
            title={
              record.status === "locked"
                ? "锁定状态不能编辑权限"
                : record.email === "<EMAIL>"
                  ? "该用户不可编辑权限"
                  : "编辑权限"
            }
          >
            <Button
              type="text"
              onClick={() => handleAddPermission(record)}
              disabled={record.status === "locked" || record.email === "<EMAIL>"}
            >
              <img src={editPermissionImg} alt="编辑权限" style={{ width: '16px', height: '16px' }} />
            </Button>
          </Tooltip>
          <Tooltip
            title={
              record.status === "locked"
                ? "锁定状态不能重置密码"
                : record.email === "<EMAIL>"
                  ? "该用户不可重置密码"
                  : "重置密码"
            }
          >
            <Button
              type="text"
              icon={<KeyOutlined />}
              onClick={() => showResetPasswordModal(record)}
              disabled={record.status === "locked" || record.email === "<EMAIL>"}
            />
          </Tooltip>
          {record.status === "locked" ? (
            <Tooltip
              title={
                record.email === "<EMAIL>"
                  ? "该用户不可解锁"
                  : "解锁"
              }
            >
              <Button
                type="text"
                icon={<LockOutlined />}
                onClick={() => handleLockStatusChange(record)}
                disabled={record.email === "<EMAIL>"}
              />
            </Tooltip>
          ) : (
            <Tooltip
              title={
                record.email === "<EMAIL>"
                  ? "该用户不可锁定"
                  : "锁定"
              }
            >
              <Button
                type="text"
                icon={<UnlockOutlined />}
                onClick={() => handleLockStatusChange(record)}
                disabled={record.email === "<EMAIL>"}
              />
            </Tooltip>
          )}
          <Tooltip
            title={
              record.status === "locked"
                ? "锁定状态不能删除"
                : record.email === "<EMAIL>"
                  ? "该用户不可删除"
                  : "删除"
            }
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              disabled={record.status === "locked" || record.email === "<EMAIL>"}
              onClick={() => {
                Modal.confirm({
                  title: "删除确认",
                  content: `确定要删除用户 "${record.username}" 吗？`,
                  okText: "确定",
                  cancelText: "取消",
                  centered: true,
                  onOk: () => handleDeleteUser(record.userId),
                });
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleCreateUser = async (values) => {
    try {
      const params = {
        username: values.username,
        realName: values.realName,
        nickname: values.nickname,
        email: values.email,
        phoneNumber: values.phone,
        accountType: values.accountType,
        status: values.status,
        password: values.password,
        noticeType: values.sendNotification.join(","),
        tenantId: values.tenantId,
        gender: values.gender,
        birthDate: values.birthDate?.format("YYYY-MM-DD"),
        address: values.address,
        position: values.position,
        orgUnitIds: values.orgUnitName ? [values.orgUnitName.value] : [],
        roleIds: values.roleId ? [values.roleId.value] : [],
        bio: values.bio,
      };

      const response = await usrManagementApi.createUser(params);

      if (response.code === 200) {
        message.success("用户创建成功！");
        setCreateModalVisible(false);
        form.resetFields();
        // 刷新用户列表
        fetchUserList({
          current: pagination.current,
          pageSize: pagination.pageSize,
        });
        // 刷新统计数据
        fetchStatistics();
      }else if (response.code === 400) {
        message.error(response.message);
      }
    } catch (error) {
      console.error("创建用户失败:", error);
    }
  };

  // 处理搜索按钮点击
  const handleSearch = () => {
    // 执行搜索
    setLoading(true);
    usrManagementApi
      .getUserPage({
        pageNum: 1,
        pageSize: pagination.pageSize,
        status: filters.status === "all" ? "" : filters.status,
        accountType: filters.accountType === "all" ? "" : filters.accountType,
        tenantId: filters.tenantId === "all" ? "" : filters.tenantId,
        content: filters.content,
      })
      .then((response) => {
        if (response.code === 200) {
          setUsers(response.data.content || []);
          setPagination({
            ...pagination,
            total: response.data.totalElements,
            current: 1,
            pageSize: response.data.size,
          });
        }
      })
      .catch((error) => {
        console.error("搜索失败:", error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleImport = async () => {
    if (!importFile) {
      message.warning("请选择要导入的文件");
      return;
    }

    setImportLoading(true);
    try {
      const response = await usrManagementApi.importUsers(importFile);
      if (response.code === 200) {
        const failRowInfo = response.data.failRowInfo;
        const successRowInfo = response.data.successRowInfo;
        if (successRowInfo.length) {
          message.success(successRowInfo.join(","));
        }

        if (failRowInfo.length) {
          message.error(failRowInfo.join(","), 5);
        }
        // message.success(failRowInfo.length ? failRowInfo.join(","): "导入成功");
        fetchUserList({
          current: pagination.current,
          pageSize: pagination.pageSize,
        }); // 刷新用户列表
        fetchStatistics(); // 刷新统计数据
      }
    } catch (error) {
      console.error("导入失败:", error);
    } finally {
      setImportLoading(false);
      setImportModalVisible(false);
      setImportFile(null);
    }
  };

  // 在打开弹窗时获取数据
  const handleCreateModalOpen = async () => {
    await Promise.all([fetchOrgTree(), fetchRoleOptions()]);
    setCreateModalVisible(true);
  };

  return (
    <div className="user-management">
      {/* Header */}
      <div className="page-header">
        <div className="header-left">
          {/* <Breadcrumb
            items={[
              { title: "首页" },
              { title: "系统管理" },
              { title: "用户管理" },
            ]}
          />
          <h2>用户管理</h2> */}
        </div>
        <div className="header-right">
          <Space wrap>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateModalOpen}
            >
              创建用户
            </Button>
            <Button
              onClick={() => {
                usrManagementApi.downloadImportTemplate().then((blob) => {
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement("a");
                  a.href = url;
                  a.download = "用户导入模板.xlsx";
                  document.body.appendChild(a);
                  a.click();
                  window.URL.revokeObjectURL(url);
                  a.remove();
                });
              }}
            >
              下载导入模版
            </Button>
            <Button
              icon={<UploadOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              导入
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={() => {
                // 处理导出用户操作
                const params = {
                  status: filters.status === "all" ? "" : filters.status,
                  accountType:
                    filters.accountType === "all" ? "" : filters.accountType,
                  tenantId: filters.tenantId === "all" ? "" : filters.tenantId,
                  content: filters.content,
                };

                usrManagementApi
                  .exportUsers(params)
                  .then((blob) => {
                    // 检查响应是否为JSON (错误信息)
                    if (blob.type === "application/json") {
                      // 处理错误响应
                      return blob.text().then((text) => {
                        const error = JSON.parse(text);
                        throw new Error(error.message || "导出失败");
                      });
                    }

                    // 正常导出流程
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement("a");
                    a.href = url;
                    a.download = `用户数据_${new Date().toLocaleDateString()}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    message.success("导出成功");
                  })
                  .catch((error) => {
                    console.error("导出失败:", error);
                  });
              }}
            >
              导出
            </Button>
            {/* <Button icon={<ReloadOutlined />} onClick={handleRefresh}>刷新</Button> */}
          </Space>
        </div>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} className="statistics-cards">
        {statistics.map((stat, index) => (
          <Col span={6} key={index}>
            <Card style={{ height: '100%' }}>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
              />
              <div className="sub-statistic">
                <span>{stat.subTitle}</span>
                <span className="sub-value">{stat.subValue}</span>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Filter Bar */}
      <Card className="filter-bar">
        <Space wrap>
          <label>用户状态:</label>
          <Select
            value={filters.status}
            style={{ width: 120 }}
            onChange={(value) =>
              setFilters((prev) => ({ ...prev, status: value }))
            }
          >
            <Option value="all">全部状态</Option>
            <Option value="active">活跃</Option>
            <Option value="locked">锁定</Option>
            <Option value="disabled">禁用</Option>
            {/* <Option value="pending">待验证</Option> */}
          </Select>
          <label>用户类型:</label>
          <Select
            value={filters.accountType}
            style={{ width: 120 }}
            onChange={(value) =>
              setFilters((prev) => ({ ...prev, accountType: value }))
            }
          >
            <Option value="all">全部类型</Option>
            <Option value="admin">管理员</Option>
            <Option value="user">普通用户</Option>
            <Option value="system">系统用户</Option>
          </Select>
          <label>租户:</label>
          <Select
            value={filters.tenantId}
            style={{ width: 120 }}
            onChange={(value) => {
              setFilters((prev) => ({ ...prev, tenantId: value }));
              fetchTenantOptions(value);
            }}
          >
            <Option value="all">全部租户</Option>
            {tenantOptions.map((tenant) => (
              <Option key={tenant.id} value={tenant.id}>
                {tenant.name}
              </Option>
            ))}
          </Select>
          <label>搜索:</label>
          <Input
            placeholder="搜索用户名/邮箱/手机..."
            style={{ width: 250 }}
            allowClear
            value={filters.content}
            onChange={(e) =>
              setFilters((prev) => ({ ...prev, content: e.target.value }))
            }
          />
          <Button
            type="primary"
            icon={<SearchOutlined />}
            onClick={handleSearch}
          >
            搜索
          </Button>
          <Button
            onClick={() => {
              // 重置所有筛选条件
              setFilters({
                status: "all",
                accountType: "all",
                tenantId: "all",
                content: "",
              });
              // 执行搜索操作
              setLoading(true);
              usrManagementApi
                .getUserPage({
                  pageNum: 1,
                  pageSize: pagination.pageSize,
                  status: "",
                  accountType: "",
                  tenantId: "",
                  content: "",
                })
                .then((response) => {
                  if (response.code === 200) {
                    setUsers(response.data.content || []);
                    setPagination({
                      ...pagination,
                      total: response.data.totalElements,
                      current: 1,
                      pageSize: response.data.size,
                    });
                  }
                })
                .catch((error) => {
                  console.error("搜索失败:", error);
                })
                .finally(() => {
                  setLoading(false);
                });
            }}
          >
            重置
          </Button>
          <Button
            icon={<LockOutlined />}
            onClick={async () => {
              if (selectedRowKeys.length === 0) {
                message.warning("请选择要锁定的用户");
                return;
              }
              // 检查是否选择了邮箱为*******************的用户
              const hasAdminUsers = users.some(
                (user) => selectedRowKeys.includes(user.userId) && user.email === "<EMAIL>"
              );

              if (hasAdminUsers) {
                message.warning("该用户不可锁定");
                return;
              }
              try {
                const response = await usrManagementApi.lockUsers(
                  selectedRowKeys
                );
                if (response.code === 200) {
                  message.success("已锁定选中用户");
                  setSelectedRowKeys([]);
                  // 刷新用户列表
                  fetchUserList({
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                  });
                  // 刷新统计数据
                  fetchStatistics();
                }
              } catch (error) {
                console.error("锁定用户失败:", error);
              }
            }}
          >
            锁定
          </Button>
          <Button
            icon={<UnlockOutlined />}
            onClick={async () => {
              if (selectedRowKeys.length === 0) {
                message.warning("请选择要解锁的用户");
                return;
              }
              // 检查是否选择了邮箱为*******************的用户
              const hasAdminUsers = users.some(
                (user) => selectedRowKeys.includes(user.userId) && user.email === "<EMAIL>"
              );

              if (hasAdminUsers) {
                message.warning("该用户不可解锁");
                return;
              }
              try {
                const response = await usrManagementApi.unlockUsers(
                  selectedRowKeys
                );
                if (response.code === 200) {
                  message.success("已解锁选中用户");
                  setSelectedRowKeys([]);
                  // 刷新用户列表
                  fetchUserList({
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                  });
                  // 刷新统计数据
                  fetchStatistics();
                }
              } catch (error) {
                console.error("解锁用户失败:", error);
              }
            }}
          >
            解锁
          </Button>
          <Button
            danger
            icon={<DeleteOutlined />}
            disabled={selectedRowKeys.length === 0}
            onClick={() => {
              // 检查是否选择了锁定状态的用户
              const hasLockedUsers = users.some(
                (user) => selectedRowKeys.includes(user.userId) && user.status === "locked"
              );

              // 检查是否选择了邮箱为*******************的用户
              const hasAdminUsers = users.some(
                (user) => selectedRowKeys.includes(user.userId) && user.email === "<EMAIL>"
              );

              if (hasLockedUsers) {
                message.warning("锁定用户不可删除");
                return;
              }

              if (hasAdminUsers) {
                message.warning("该用户不可删除");
                return;
              }
              
              Modal.confirm({
                title: "删除确认",
                content: `确定要删除选中的 ${selectedRowKeys.length} 个用户吗？`,
                okText: "确定",
                cancelText: "取消",
                centered: true,
                onOk: async () => {
                  try {
                    const response = await usrManagementApi.deleteUsers(
                      selectedRowKeys
                    );
                    if (response.code === 200) {
                      message.success("用户删除成功");
                      setSelectedRowKeys([]);
                      // 刷新用户列表
                      fetchUserList({
                        current: pagination.current,
                        pageSize: pagination.pageSize,
                      });
                      // 刷新统计数据
                      fetchStatistics();
                    }
                  } catch (error) {
                    console.error("删除用户失败:", error);
                  }
                },
              });
            }}
          >
            批量删除
          </Button>
        </Space>
      </Card>

      {/* User Table */}
      <Card className="user-table-card">
        <div>
          <Table
            columns={columns}
            dataSource={users}
            rowKey="userId"
            rowSelection={{
              selectedRowKeys,
              onChange: setSelectedRowKeys,
              getCheckboxProps: (record) => ({
                disabled: record.status === "disabled" || record.email === "<EMAIL>", // 禁用"disabled"状态的用户选择框和邮箱为*******************的用户选择框
              }),
            }}
            loading={loading}
            scroll={{ y: users.length > 10 ? 700 : undefined }}
            tableLayout="fixed"
            pagination={{
              ...pagination,
              showQuickJumper: true,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
            onChange={handleTableChange}
            columnTitle={{ style: { textAlign: "center" } }}
          />
        </div>
      </Card>

      {/* Create User Modal */}
      <Modal
        title="创建用户"
        open={createModalVisible}
        okText={"创建"}
        onOk={() => form.submit()}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        width={720}
        centered
      >
        <Form form={form} layout="vertical" onFinish={handleCreateUser}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[{ required: true, message: "请输入用户名" }]}
              >
                <Input placeholder="请输入用户名"/>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="password"
                label="密码"
                rules={[{ required: true, message: "请输入密码" }]}
              >
                <Input.Password placeholder="密码包含特殊字符，大小写字母，长度为8~20位" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="realName"
                label="真实姓名"
                rules={[{ required: true, message: "请输入真实姓名" }]}
              >
                <Input placeholder="请输入真实姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="nickname"
                label="昵称"
                rules={[{ message: "请输入昵称" }]}
              >
                <Input placeholder="请输入昵称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: "请输入邮箱" },
                  { type: "email", message: "请输入有效的邮箱地址" },
                ]}
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号"
                rules={[
                  { required: true, message: "请输入手机号" },
                  { pattern: /^1\d{10}$/, message: "请输入有效的手机号" },
                ]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="accountType"
                label="账户类型"
                rules={[{ required: true, message: "请选择账户类型" }]}
              >
                <Select placeholder="请选择账户类型">
                  <Option value="admin">管理员</Option>
                  <Option value="user">普通用户</Option>
                  <Option value="system">系统用户</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="账户状态"
                initialValue="active"
                rules={[{ required: true, message: "请选择账户状态" }]}
              >
                <Radio.Group>
                  <Radio value="active">启用</Radio>
                  <Radio value="disabled">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="sendNotification"
                label="通知方式"
                rules={[{ required: true, message: "请选择通知方式" }]}
              >
                <Select mode="multiple" placeholder="请选择通知方式">
                  <Option value="email">邮件通知</Option>
                  <Option value="sms">短信通知</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="tenantId"
                label="所属租户"
                rules={[{ required: true, message: "请选择所属租户" }]}
              >
                <Select placeholder="请选择所属租户">
                  {tenantOptions.map((tenant) => (
                    <Option key={tenant.id} value={tenant.id}>
                      {tenant.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="birthDate" label="出生日期">
                <DatePicker
                  style={{ width: "100%" }}
                  placeholder="请选择出生日期"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="gender" label="性别">
                <Select placeholder="请选择性别">
                  <Option value="male">男</Option>
                  <Option value="female">女</Option>
                  <Option value="unknown">未知</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="position" label="职位">
                <Input placeholder="请输入职位" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="roleId" label="角色">
                <Select
                  placeholder="请选择角色"
                  optionLabelProp="children"
                  showSearch
                  allowClear
                  optionFilterProp="children"
                  labelInValue
                >
                  {roleOptions.map((role) => (
                    <Option key={role.id} value={role.id}>
                      {role.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="orgUnitName" label="组织名称">
                <TreeSelect
                  showSearch
                  style={{ width: "100%" }}
                  dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
                  placeholder="请选择组织名称"
                  allowClear
                  treeData={orgTreeData}
                  fieldNames={{
                    label: "name",
                    value: "id",
                    children: "childrenList",
                  }}
                  treeDefaultExpandAll
                  labelInValue
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="address" label="地址信息">
                <Input.TextArea rows={1} placeholder="请输入地址信息" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="bio" label="个人简介">
                <Input.TextArea rows={2} placeholder="请输入个人简介" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 编辑用户模态框 */}
      <Modal
        title="编辑用户"
        okText="编辑"
        open={editModalVisible}
        onOk={() => editForm.submit()}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
        }}
        width={720}
        centered
      >
        <Form form={editForm} layout="vertical" onFinish={handleEditUser}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[{ required: true, message: "请输入用户名" }]}
              >
                <Input placeholder="请输入用户名"/>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="realName"
                label="真实姓名"
                rules={[{ required: true, message: "请输入真实姓名" }]}
              >
                <Input placeholder="请输入真实姓名" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="nickname" label="昵称">
                <Input placeholder="请输入昵称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="accountType"
                label="账户类型"
                rules={[{ required: true, message: "请选择账户类型" }]}
              >
                <Select>
                  <Option value="admin">管理员</Option>
                  <Option value="user">普通用户</Option>
                  <Option value="system">系统用户</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: "请输入邮箱" },
                  { type: "email", message: "请输入有效的邮箱地址" },
                ]}
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phoneNumber"
                label="手机号"
                rules={[
                  { required: true, message: "请输入手机号" },
                  { pattern: /^1\d{10}$/, message: "请输入有效的手机号" },
                ]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="账户状态"
                rules={[{ required: true, message: "请选择账户状态" }]}
              >
                <Radio.Group>
                  <Radio value="active">启用</Radio>
                  <Radio value="disabled">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="tenantId"
                label="所属租户"
                rules={[{ required: true, message: "请选择所属租户" }]}
              >
                <Select placeholder="请选择所属租户">
                  {tenantOptions.map((tenant) => (
                    <Option key={tenant.id} value={tenant.id}>
                      {tenant.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="birthDate" label="出生日期">
                <DatePicker
                  style={{ width: "100%" }}
                  placeholder="请选择出生日期"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="gender" label="性别">
                <Select placeholder="请选择性别">
                  <Option value="male">男</Option>
                  <Option value="female">女</Option>
                  <Option value="unknown">未知</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="position" label="职位">
                <Input placeholder="请输入职位" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="roleId" label="角色">
                <Select
                  placeholder="请选择角色"
                  optionLabelProp="children"
                  showSearch
                  allowClear
                  optionFilterProp="children"
                  labelInValue
                >
                  {roleOptions.map((role) => (
                    <Option key={role.id} value={role.id}>
                      {role.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="orgUnitName" label="组织名称">
                <TreeSelect
                  showSearch
                  style={{ width: "100%" }}
                  dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
                  placeholder="请选择组织名称"
                  allowClear
                  treeData={orgTreeData}
                  fieldNames={{
                    label: "name",
                    value: "id",
                    children: "childrenList",
                  }}
                  treeDefaultExpandAll
                  labelInValue
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="address" label="地址信息">
                <Input.TextArea rows={1} placeholder="请输入地址信息" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="bio" label="个人简介">
                <Input.TextArea rows={2} placeholder="请输入个人简介" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 重置密码弹窗 */}
      <Modal
        title="重置密码"
        open={resetPasswordModalVisible}
        onOk={() => resetPasswordForm.submit()}
        onCancel={() => {
          setResetPasswordModalVisible(false);
          resetPasswordForm.resetFields();
        }}
        width={400}
        centered
      >
        <Form
          form={resetPasswordForm}
          layout="vertical"
          onFinish={handleResetPassword}
        >
          <Form.Item name="username" label="用户名">
            <Input disabled />
          </Form.Item>
          <Form.Item
            name="password"
            label="新密码"
            rules={[
              { required: true, message: "请输入新密码" },
              { min: 8, message: "密码长度不能小于8位" },
              { max: 20, message: "密码长度不能大于20位" },
            ]}
          >
            <Input.Password placeholder="密码包含特殊字符，大小写字母，长度为8~20位" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 导入用户弹窗 */}
      <Modal
        title="导入用户"
        open={importModalVisible}
        onOk={handleImport}
        confirmLoading={importLoading}
        onCancel={() => {
          setImportModalVisible(false);
          setImportFile(null);
        }}
        width={500}
        centered
      >
        <Upload.Dragger
          beforeUpload={(file) => {
            setImportFile(file);
            return false;
          }}
          onRemove={() => setImportFile(null)}
          fileList={importFile ? [importFile] : []}
          maxCount={1}
          style={{ padding: "10px 0" }}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined style={{ fontSize: 48, color: "#40a9ff" }} />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p
            className="ant-upload-hint"
            style={{ color: "rgba(0, 0, 0, 0.45)" }}
          >
            支持单个或批量上传。严禁上传公司数据或其他违禁文件。
          </p>
        </Upload.Dragger>
      </Modal>

      {/* 编辑权限弹窗 */}
      <Modal
        title="授予权限"
        open={permissionModalVisible}
        onOk={() => permissionForm.submit()}
        onCancel={handlePermissionCancel}
        centered
        width={600}
      >
        <Form
          form={permissionForm}
          layout="vertical"
          onFinish={handlePermissionSubmit}
        >
          <Form.Item label="用户名">
            <Input value={selectedUserData?.username} disabled />
          </Form.Item>
          <Form.Item name="permissions" label="权限">
            <Dropdown
              dropdownRender={() => permissionDropdownContent}
              open={permissionDropdownVisible}
              onOpenChange={setPermissionDropdownVisible}
              trigger={['click']}
              overlayStyle={{ maxWidth: '550px', maxHeight: '400px' }}
            >
              <div 
                style={{ 
                  border: '1px solid #d9d9d9',
                  borderRadius: '2px',
                  backgroundColor: '#fff',
                  width: '100%',
                  cursor: 'pointer'
                }}
                onClick={() => setPermissionDropdownVisible(true)}
              >
                {checkedPermissions.length > 0 ? (
                  getSelectedPermissionTags()
                ) : (
                  <div style={{ padding: '4px 11px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ color: '#bfbfbf' }}>请选择权限</span>
                    <DownOutlined />
                  </div>
                )}
              </div>
            </Dropdown>
          </Form.Item>
          {/* <Form.Item name="type" label="授权类型">
            <Radio.Group>
              <Radio value="grant">授予</Radio>
              <Radio value="deny">拒绝</Radio>
              <Radio value="temporary">临时</Radio>
            </Radio.Group>
          </Form.Item> */}
          <Form.Item name="reason" label="授权原因">
            <Input.TextArea rows={4} placeholder="请输入授权原因" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement;
