import { Row, Col, Card, Descriptions } from 'antd'

const CustomerGroupDetail = ({ selectedGroup, customerType }) => {
  if (!selectedGroup) {
    return (
      <div className='empty-detail'>
        <p>请选择一个客户分组查看详细信息</p>
      </div>
    )
  }



  return (
    <div className='customer-detail'>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title='组织信息'>
            <Descriptions bordered column={2}>
              <Descriptions.Item label='组织类型'>
                {customerType.find(
                  item => item.itemCode === selectedGroup?.ownershipType
                )?.itemName || '-'}
              </Descriptions.Item>

              {/* <Descriptions.Item label="组织规模">
                {selectedGroup.scale}
              </Descriptions.Item> */}
              <Descriptions.Item label='联系人'>
                {selectedGroup?.contacts || '无'}
              </Descriptions.Item>
              <Descriptions.Item label='联系电话'>
                {selectedGroup?.contactNumber || '无'}
              </Descriptions.Item>
              <Descriptions.Item label='备注'>
                {selectedGroup?.assignmentNotes || '无'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* <Col span={12}>
          <Card title="服务配置">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Tag color="blue">{managementAttributes.serviceLevel}</Tag>
                <Tag color="green">优先级: {managementAttributes.supportPriority}</Tag>
              </div>
              <div className="device-quota">
                <span>设备配额使用情况</span>
                <Progress
                  percent={Math.round((managementAttributes.resourceQuota.currentDeviceCount / managementAttributes.resourceQuota.maxDeviceCount) * 100)}
                  format={() => `${managementAttributes.resourceQuota.currentDeviceCount}/${managementAttributes.resourceQuota.maxDeviceCount}`}
                />
              </div>
              <div>
                <h4>已启用功能</h4>
                <Space wrap>
                  {serviceConfig.allowedFeatures.map((feature, index) => (
                    <Tag key={index} color="blue">{feature}</Tag>
                  ))}
                </Space>
              </div>
            </Space>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="通知设置">
            <List
              size="small"
              dataSource={[
                { type: '邮件通知', enabled: serviceConfig.notificationSettings.emailEnabled },
                { type: '短信通知', enabled: serviceConfig.notificationSettings.smsEnabled },
                { type: '应用推送', enabled: serviceConfig.notificationSettings.appPushEnabled }
              ]}
              renderItem={item => (
                <List.Item>
                  <Space>
                    {item.type}
                    <Tag color={item.enabled ? 'green' : 'red'}>
                      {item.enabled ? '已启用' : '未启用'}
                    </Tag>
                  </Space>
                </List.Item>
              )}
            />
          </Card>
        </Col> */}
      </Row>
    </div>
  )
}

export default CustomerGroupDetail
