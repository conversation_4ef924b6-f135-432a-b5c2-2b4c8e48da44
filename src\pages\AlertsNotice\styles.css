.alerts-notice {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.page-header {
  margin-bottom: 24px;
}

.header-left {
  margin-bottom: 16px;
}

.header-left h2 {
  margin: 8px 0 0;
  font-size: 24px;
  font-weight: 500;
}

.notice-banner {
  margin-bottom: 16px;
}

.notice-banner .ant-carousel {
  .slick-prev,
  .slick-next {
    color: rgba(0, 0, 0, 0.45);
    font-size: 24px;
  }
}

.filter-bar {
  margin-bottom: 16px;
}

.view-switch {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notice-grid {
  flex: 1;
  min-height: 0;
  overflow: auto;
}


.notice-grid>.ant-row{
 width: 100%;
}

.notice-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s;
}

.notice-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.notice-card.urgent {
  border: 1px solid #ff4d4f;
}

.notice-card.important {
  border: 1px solid #faad14;
}

.notice-card.pinned {
  background: #fafafa;
}

.notice-card-header {
  margin-bottom: 12px;
}

.notice-card-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  line-height: 1.4;
}

.notice-card-content {
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 16px;
  flex: 1;
}

.notice-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.notice-card-channels {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
  color: rgba(0, 0, 0, 0.45);
}

/* Modal styles */
.ant-modal-body .ant-form-item {
  margin-bottom: 24px;
}

.ant-modal-body .ant-radio-group {
  width: 100%;
  display: flex;
  gap: 8px;
}

.ant-modal-body .ant-radio-button-wrapper {
  flex: 1;
  text-align: center;
}

/* Responsive styles */
@media (max-width: 768px) {
  .alerts-notice {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .view-switch {
    flex-direction: column;
    gap: 16px;
  }

  .ant-modal {
    max-width: 100vw;
    margin: 8px;
  }
}