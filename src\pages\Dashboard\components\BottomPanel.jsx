import { Row, Col, Card } from 'antd';
import {useEffect,useRef} from 'react';
import echarts from "./../../../utils/echart"
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs';

const BottomPanel = ({currentMonthRatEvents = []}) => {
  const chartInstance = useRef(null);
  const navigate = useNavigate()

  // chart初始化
  const chartInit = chartData => {
    let chartDom = document.getElementById('dashboard-chart2');
    if (!chartDom) {
      return;
    }

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartDom);
    }

    const option = {
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '13%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            show: false,
            backgroundColor: '#6a7985',
            fontSize: '12px',
          },
        },
        formatter: function(params) {
          return params[0].axisValue + '<br/>' + '事件数:&nbsp;&nbsp; ' + params[0].value;
        }
      },
      legend: {
        show: false,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLabel: {
          fontSize: '12px', // 可选：调整字体大小
        },
        axisLine: {
          show: true, // 显示x轴轴线
          lineStyle: {
            color: '#B0B0B0',
            width: 1,
          },
        },
        data: chartData.map(item => item.date) || [],
      },
      yAxis: {
        type: 'value',
        name: '次',
        nameLocation: 'end',
        minInterval: 1, 
        nameGap: 10,
        nameTextStyle: {
          fontSize: '12px',
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
        axisLine: {
          show: true, // 显示y轴轴线
          lineStyle: {
            color: '#B0B0B0',
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
      },
      series: [
        {
          type: 'line',
          // smooth: true,
          emphasis: {
            focus: 'series',
          },
          lineStyle: {
            color: '#F8BD8D', // 折线颜色
            width: 2,
          },
          itemStyle: {
            color: '#B45B13', // 拐点颜色
          },
          data: chartData.map(item => item.count),
        },
      ],
    };
    option && chartInstance.current.setOption(option, true);

    // 添加点击事件
    chartInstance.current.off('click'); // 先解绑，防止重复绑定
    chartInstance.current.on('click', function(params) {
      if (params.componentType === 'series' && params.seriesType === 'line') {
        navigate('/sensing/event',
          {
            state: {
              startTime: dayjs(params.name).format("YYYY-MM-DD 00:00:00"),
              endTime: dayjs(params.name).format("YYYY-MM-DD 23:59:59"),
            }   
          }
        )
      }
    });
  };

  useEffect(() => {
    if (currentMonthRatEvents.length) {
      chartInit(currentMonthRatEvents);
      
      // Add resize handler
      const handleResize = () => {
        if (chartInstance.current) {
          chartInstance.current.resize();
        }
      };
      
      window.addEventListener('resize', handleResize);
      
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [currentMonthRatEvents.length]);
  return (
    <Row gutter={[16, 16]} style={{height: "100%"}}>
      <Col span={24}>
        <Card title={<span><b>本月鼠患事件数量</b></span>} className="dashboard-card dashboard-card-3">
        {
         currentMonthRatEvents.length ? <div style={{ width: '100%', height: '55vh', padding: 20 }} id='dashboard-chart2'>
        </div> :"暂无数据"
        }
        </Card>
      </Col>
    </Row>
  );
};

export default BottomPanel;