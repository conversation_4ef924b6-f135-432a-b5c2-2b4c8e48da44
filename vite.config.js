import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import cesium from "vite-plugin-cesium";

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
  const isDev = command === "serve";
  return {
    base: '/',
    plugins: [react(),cesium()],
    server: isDev ? {
      host: "0.0.0.0",
      proxy: {
        '/api': {
          // target: 'http://************:8084',
          target: 'http://************:8000',
          // target: "https://qsqcloud.com",
          // target: "https://test.qsqcloud.com/",
          //  target: "https://ltest.qsqcloud.com/",
          // target: "https://canary.qsqcloud.com/",
          changeOrigin: true,
          secure: false
        }
      }
    } : undefined,
    build: {
      outDir: "dist",
      assetsDir: "assets",
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ["react", "react-dom", "react-router-dom"],
          },
        },
      },
    },
  };
});
