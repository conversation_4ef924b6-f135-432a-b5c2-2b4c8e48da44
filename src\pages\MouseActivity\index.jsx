import React, { useState, useEffect, useRef } from "react";
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Select,
  DatePicker,
  Tabs,
  Spin,
} from "antd";
import { DownloadOutlined } from "@ant-design/icons";
import {
  getMousesTotalData,
  getMousesTotalDay,
  getMousesTotalWeek,
  exportMousesStatisticsData,
} from "./../../service/statistics";
import { getCameraList } from "../../service/deviceCamera";
import echarts from "./../../utils/echart";
import { debounce } from "lodash";
import dayjs from "dayjs";
import "./styles.css";
import useGroup from "../../hooks/useGroup";
import { color } from "echarts";

const { RangePicker } = DatePicker;
const { Option } = Select;

const MouseActivity = () => {
  const [selectedCamera, setSelectedCamera] = useState([]); // 选中的摄像头
  const [cameraData, setCameraData] = useState([]); // 获取摄像头数据
  const [cameraLoading, setCameraLoading] = useState(false); //摄像头loading
  const [searchCameraData, setSearchCameraData] = useState({
    keyword: "",
    size: 50,
    page: 0,
  }); // 分页查询摄像头
  const [totalPages, setTotalPages] = useState(0);
  const [searchData, setSearchData] = useState({
    dateType: "hour",
    startTime: dayjs().format("YYYY-MM-DD 00:00:00"),
    endTime: dayjs().format("YYYY-MM-DD 23:59:59"),
    cameraIds: [],
    // isNotGroup: 1,
    // regionId: "",
    // customerId: '',
    //   groupType: 1
  });
  const [timeRange, setTimeRange] = useState("hour");
  const [dateRange, setDateRange] = useState([dayjs(), dayjs()]);
  const [loading, setLoading] = useState(false);
  const chartInstance1 = useRef(null);
  const chartInstance2 = useRef(null);
  const chartInstance3 = useRef(null);
  const [gropNode, gropSearch] = useGroup();
  const [tab, setTab] = useState("overview");

  const handleTimeRangeChange = (value) => {
    setTimeRange(value);
    setSearchData((prev) => ({
      ...prev,
      dateType: value,
    }));
  };

  const handleExport = async () => {
    try {
      const result = await exportMousesStatisticsData(
        {
          ...searchData,
          isNotGroup: gropSearch.isNotGroup,
          regionId: gropSearch.groupId,
          customerId: gropSearch.orgId,
          groupType: gropSearch.groupType,
        },
        tab === "overview" ? 0 : 1
      );
      const blob = new Blob([result], { type: "application/xlsx" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "download.xlsx";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Export failed:", error);
    }
  };

  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
    setSearchData((prev) => ({
      ...prev,
      startTime: dates[0].format("YYYY-MM-DD 00:00:00"),
      endTime: dates[1].format("YYYY-MM-DD 23:59:59"),
    }));
  };

  // 获取摄像头列表
  const fetchCameraList = async (obj = {}) => {
    setLoading(true);
    try {
      const res = await getCameraList({
        ...searchCameraData,
        ...gropSearch,
        ...obj,
        isNotGroup: !gropSearch.isNotGroup,
      });
      setTotalPages(res.totalPages);
      setLoading(false);
      return res.content;
    } catch (error) {
      console.error("Failed to fetch camera list:", error);
    }
  };

  // 下拉搜索
  const handleSearch = async (value) => {
    setCameraLoading(true);
    try {
      const data = await fetchCameraList({ keyword: value, page: 0 });
      setCameraData(data);
    } catch (error) {
      console.error("Failed to search cameras:", error);
    } finally {
      setCameraLoading(false);
    }
  };

  // 滚动加载
  const loadMore = async (e) => {
    if (cameraLoading) return;
    const bottom =
      e.target.scrollHeight === e.target.scrollTop + e.target.clientHeight;
    if (bottom && totalPages - 1 > searchCameraData.page) {
      setSearchCameraData({
        ...searchCameraData,
        page: searchCameraData.page + 1,
      });
      setCameraLoading(true);
      const newOptions = await fetchCameraList({
        page: searchCameraData.page + 1,
      });
      setCameraData((prevOptions) => [...prevOptions, ...newOptions]);
      setCameraLoading(false);
    }
  };

  const fetchData = async (obj = {}) => {
    if (!gropSearch) {
      return;
    }
    const tempObj = {
      ...searchData,
      ...obj,
      isNotGroup: gropSearch.isNotGroup,
      regionId: gropSearch.groupId,
      customerId: gropSearch.orgId,
      groupType: gropSearch.groupType,
    };
    try {
      if (tab === "overview") {
        const res = await getMousesTotalData(tempObj);
        chartInit1(res);
      } else {
        const [result2, result3] = await Promise.all([
          getMousesTotalDay(tempObj),
          getMousesTotalWeek(tempObj),
        ]);
        chartInit2(result2);
        chartInit3(result3);
      }
    } catch (error) {
      console.log(error);
    }
  };

  // chart初始化
  const chartInit1 = (chartData) => {
    let chartDom = document.getElementById("mouse-activity-chart1");
    if (!chartDom) {
      return;
    }

    if (!chartInstance1.current) {
      chartInstance1.current = echarts.init(chartDom);
    }

    // 检查是否有数据
    const hasData = chartData && chartData.length > 0;

    // 默认时间轴数据（0-23点）
    const defaultTimeData = Array.from({ length: 24 }, (_, i) => `${i}:00`);

    const option = {
      // 当没有数据时显示标题
      ...(!hasData && {
        title: {
          text: "暂无数据",
          left: "center",
          top: "middle",
          textStyle: {
            fontSize: 18,
            fontWeight: "bold",
            color: "#666",
          },
        },
      }),
      grid: {
        left: "8%",
        right: "6%",
        bottom: "10%",
        top: "5%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            show: false,
            backgroundColor: "#6a7985",
            fontSize: "12px",
          },
        },
      },
      legend: {
        show: false,
      },
      xAxis: {
        type: "category",
        boundaryGap: true,
        axisLabel: {
          fontSize: "12px", // 可选：调整字体大小
        },
        axisLine: {
          show: true, // 显示x轴轴线
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        data: hasData
          ? chartData.map((item) => {
              return timeRange === "hour" ? item.hour : item.date;
            })
          : defaultTimeData,
      },
      yAxis: {
        type: "value",
        name: "次",
        nameLocation: "end",
        nameGap: 10,
        minInterval: 1,
        min: 0,
        max: hasData ? undefined : 100,
        nameTextStyle: {
          fontSize: "12px",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
        axisLine: {
          show: true, // 显示y轴轴线
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
      },
      // 当没有数据时显示提示文字
      ...(!hasData && {
        graphic: [
          {
            type: "text",
            left: "center",
            top: "middle",
            z: 100,
            style: {
              text: "暂无数据",
              fontSize: 18,
              fontWeight: "bold",
              fill: "#666",
            },
          },
        ],
      }),
      ...(chartData &&
        chartData.length > 30 && {
          toolbox: {
            feature: {
              dataZoom: {
                yAxisIndex: "none",
                title: {
                  zoom: "区域缩放",
                  back: "区域缩放还原",
                },
              },
              restore: {
                title: "还原",
              },
            },
            right: 20,
          },
          dataZoom: [
            {
              type: "slider",
              xAxisIndex: 0,
              start: 0,
              end: 100,
            },
            {
              type: "inside",
              xAxisIndex: [0],
              zoomLock: false,
              moveOnMouseMove: true,
              moveOnMouseWheel: true,
            },
          ],
        }),
      series: [
        {
          type: "bar",
          // smooth: true,
          emphasis: {
            focus: "series",
          },
          data: hasData ? chartData.map((item) => item.count) : [],
          ...(chartData && chartData.length < 10
            ? {
                barWidth: 30,
              }
            : {}),
        },
      ],
    };
    option && chartInstance1.current.setOption(option, true);
  };

  // chart初始化
  const chartInit2 = (chartData) => {
    let chartDom = document.getElementById("mouse-activity-chart2");
    if (!chartDom) {
      return;
    }

    if (!chartInstance2.current) {
      chartInstance2.current = echarts.init(chartDom);
    }

    // 检查是否有数据
    const hasData = chartData && chartData.length > 0;

    const option = {
      // 当没有数据时显示标题
      ...(!hasData && {
        title: {
          text: "暂无数据",
          left: "center",
          top: "middle",
          textStyle: {
            fontSize: 18,
            fontWeight: "bold",
            color: "#666",
          },
        },
      }),
      grid: {
        left: "3%",
        right: "6%",
        bottom: "3%",
        top: "13%",
        containLabel: true,
      },
      tooltip: {
        trigger: "item",
      },
      legend: {
        orient: "vertical",
        left: "left",
        show: hasData,
        formatter: function (name) {
          // 找到对应的数据项
          const item = chartData.find(
            (i) =>
              (i.time_slot === "凌晨(1:00-6:59)" &&
                name === "凌晨(1:00-6:59)") ||
              (i.time_slot === "上午(7:00-12:59)" &&
                name === "上午(7:00-12:59)") ||
              (i.time_slot === "下午(13:00-18:59)" &&
                i.status !== 1 &&
                name === "下午(13:00-18:59)") ||
              (i.time_slot === "晚上(19:00-00:59)" &&
                i.status !== 1 &&
                name === "晚上(19:00-00:59)")
          );
          return item ? `${name} ${item.count}` : name;
        },
      },
      // 当没有数据时显示提示文字
      ...(!hasData && {
        graphic: [
          {
            type: "text",
            left: "center",
            top: "middle",
            z: 100,
            style: {
              text: "暂无数据",
              fontSize: 18,
              fontWeight: "bold",
              fill: "#666",
            },
          },
        ],
      }),
      series: [
        {
          name: "活动次数",
          type: "pie",
          radius: "50%",
          data: hasData
            ? chartData.map((item) => ({
                value: item.count,
                name: item.time_slot,
              }))
            : [],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    };
    option && chartInstance2.current.setOption(option, true);
  };
  // chart初始化
  const chartInit3 = (chartData) => {
    let chartDom = document.getElementById("mouse-activity-chart3");
    if (!chartDom) {
      return;
    }

    if (!chartInstance3.current) {
      chartInstance3.current = echarts.init(chartDom);
    }

    // 检查是否有数据
    const hasData = chartData && chartData.length > 0;

    // 默认星期数据
    const defaultWeekData = [
      "周一",
      "周二",
      "周三",
      "周四",
      "周五",
      "周六",
      "周日",
    ];

    const option = {
      // 当没有数据时显示标题
      ...(!hasData && {
        title: {
          text: "暂无数据",
          left: "center",
          top: "middle",
          textStyle: {
            fontSize: 18,
            fontWeight: "bold",
            color: "#666",
          },
        },
      }),
      grid: {
        left: "5%",
        right: "6%",
        bottom: "10%",
        top: "5%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            show: false,
            backgroundColor: "#6a7985",
            fontSize: "12px",
          },
        },
      },
      legend: {
        data: false,
      },
      xAxis: {
        type: "category",
        boundaryGap: true,
        axisLabel: {
          fontSize: "12px", // 可选：调整字体大小
          align: "center",
        },
        axisLine: {
          show: true, // 显示x轴轴线
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        data: hasData
          ? chartData.map((item) => item.day_name)
          : defaultWeekData,
      },
      yAxis: {
        type: "value",
        name: "次",
        nameLocation: "end",
        minInterval: 1,
        min: 0,
        max: hasData ? undefined : 100,
        nameGap: 10,
        nameTextStyle: {
          fontSize: "12px",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
        axisLine: {
          show: true, // 显示y轴轴线
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
      },
      // 当没有数据时显示提示文字
      ...(!hasData && {
        graphic: [
          {
            type: "text",
            left: "center",
            top: "middle",
            z: 100,
            style: {
              text: "暂无数据",
              fontSize: 18,
              fontWeight: "bold",
              fill: "#666",
            },
          },
        ],
      }),
      series: [
        {
          type: "bar",
          // stack: 'Total',
          data: hasData ? chartData.map((item) => item.count) : [],
          ...(chartData && chartData.length < 10
            ? {
                barWidth: 30,
              }
            : {}),
        },
      ],
    };
    option && chartInstance3.current.setOption(option, true);
  };

  function chartResize() {
    chartInstance1.current?.resize();
    chartInstance2.current?.resize();
    chartInstance3.current?.resize();
  }

  window.addEventListener("resize", chartResize);

  useEffect(() => {
    if (cameraData.length > 0 && selectedCamera.length > 0) {
      fetchData();
    }
  }, [searchData, tab]);

  useEffect(() => {
    (async () => {
      // 获取摄像头列表
      if (gropSearch) {
        const res = await fetchCameraList();
        if (res && res.length > 0) {
          setCameraData([{ cameraNo: "全选", cameraId: "全选" }, ...res]);
          setSelectedCamera(["全选"]);
          setSearchData((prev) => ({
            ...prev,
            cameraIds: [],
          }));
        } else {
          setSelectedCamera([]);
          setCameraData([]);
          if (tab === "overview") {
            chartInit1([]);
          } else {
            chartInit2([]);
            chartInit3([]);
          }
        }
      }
    })();
  }, [gropSearch]);

  useEffect(() => {
    // 清理函数，在组件卸载时取消请求
    return () => {
      if (window.cancelToken) {
        window.cancelToken(); // 取消请求
      }
      window.removeEventListener("resize", chartResize);
    };
  }, []);

  const handleTabChange = (value) => {
    setTab(value);
    if (selectedCamera.length === 0) {
      chartInit1([]);
      chartInit2([]);
      chartInit3([]);
    }
    setTimeout(() => {
      chartResize();
    }, 0);
  };

  return (
    <div className="mouse-activity">
      {/* Header */}
      <div className="page-header">
        <div className="header-title"></div>
        {/* <div className='header-actions'>
          <Space>
            <Button icon={<DownloadOutlined />} onClick={handleExport}>
              导出
            </Button>
          </Space>
        </div> */}
      </div>

      {/* Main Content */}
      <div className="mouse-active-content">
        <div className="mouse-active-side">{gropNode()}</div>
        <Tabs
          defaultActiveKey="overview"
          className="mouse-active-content-chart"
          onChange={handleTabChange}
          tabBarExtraContent={
            <div style={{ width: "100%" }}>
              <div
                style={{
                  width: "100%",
                  display: "flex",
                  alignItems: "center",
                  gap: "16px",
                }}
              >
                <Space style={{ marginRight: "10px" }}>
                  {tab === "overview" && (
                    <>
                      <span>统计类型：</span>
                      <Select
                        value={timeRange}
                        style={{ width: 90, marginRight: 8 }}
                        options={[
                          { value: "hour", label: "按小时" },
                          { value: "day", label: "按天" },
                          { value: "month", label: "按月" },
                          { value: "year", label: "按年" },
                        ]}
                        onChange={handleTimeRangeChange}
                      />
                    </>
                  )}
                  <span>时间范围：</span>
                  <RangePicker
                    value={dateRange}
                    allowClear={false}
                    style={{ width: "250px" }}
                    onChange={handleDateRangeChange}
                    disabledDate={(current) => {
                      return current && current > dayjs().endOf("day");
                    }}
                  />
                </Space>
                <div
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                >
                  <span>摄像头编号：</span>
                  <Select
                    showSearch
                    mode="multiple"
                    maxTagCount={3}
                    placeholder="请选择摄像头编号"
                    onPopupScroll={loadMore}
                    onSearch={debounce(handleSearch, 300)}
                    loading={cameraLoading}
                    style={{ minWidth: "200px" }}
                    optionFilterProp="children"
                    filterOption={false}
                    onDeselect={(value) => {
                      if (window.cancelToken) {
                        window.cancelToken();
                      }
                      setSearchData((prev) => ({
                        ...prev,
                        cameraIds: selectedCamera.filter(
                          (item) => item != value
                        ),
                      }));
                    }}
                    onBlur={() => {
                      setSearchData((prev) => ({
                        ...prev,
                        cameraIds: selectedCamera.filter(
                          (item) => item != "全选"
                        ),
                      }));
                      // if(selectedCamera.includes("全选")){
                      //   fetchData({
                      //     cameraIds: []
                      //   })
                      // }
                    }}
                    value={selectedCamera}
                    onChange={(value) => {
                      // 如果选择了全选，清空其他选项
                      if (value.includes("全选")) {
                        setSelectedCamera(["全选"]);
                      } else {
                        setSelectedCamera(value);
                      }

                      if (value.length === 0) {
                        chartInit1([]);
                        chartInit2([]);
                        chartInit3([]);
                      }
                    }}
                    onDropdownVisibleChange={async (open) => {
                      if (open) {
                        const tempSearch = {
                          keyword: "",
                          size: 50,
                          page: 0,
                        };
                        const res = await fetchCameraList(tempSearch);
                        setSearchCameraData(tempSearch);
                        if (res && res.length > 0) {
                          setCameraData([
                            { cameraNo: "全选", cameraId: "全选" },
                            ...res,
                          ]);
                        }
                      }
                    }}
                  >
                    {cameraData?.map((option) => (
                      <Option
                        key={option.cameraId}
                        value={option.cameraId}
                        disabled={
                          (option.cameraId === "全选" &&
                            selectedCamera.length > 0 &&
                            !selectedCamera.includes("全选")) ||
                          (option.cameraId !== "全选" &&
                            selectedCamera.includes("全选"))
                        }
                      >
                        {option.cameraNo}
                      </Option>
                    ))}
                  </Select>
                </div>
              </div>
            </div>
          }
          items={[
            {
              key: "overview",
              label: "活动概览",
              children: (
                <>
                  <Card
                    title="活动强度趋势"
                    // extra={<Button icon={<FullscreenOutlined />}>放大</Button>}
                  >
                    <Spin spinning={loading}>
                      <div
                        style={{ width: "100%", height: "60vh", padding: 20 }}
                        id="mouse-activity-chart1"
                      ></div>
                    </Spin>
                  </Card>
                </>
              ),
            },

            {
              key: "activity_distribution",
              label: "活动分布",
              children: (
                <Row gutter={[16, 16]} style={{ height: "100%" }}>
                  <Col span={12}>
                    <Card title="一天中的活动分布">
                      <Spin spinning={loading}>
                        <div
                          style={{ width: "100%", height: "60vh", padding: 20 }}
                          id="mouse-activity-chart2"
                        ></div>
                      </Spin>
                      {/* {maxDayData ? (
                      <div className='chart-summary'>
                        {maxDayData.time_slot}活动占比最多:{' '}
                        {maxDayData.percentage}%
                      </div>
                    ) : (
                      <div className='chart-summary' />
                    )} */}
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="一周中的活动分布">
                      <Spin spinning={loading}>
                        <div
                          style={{ width: "100%", height: "60vh", padding: 20 }}
                          id="mouse-activity-chart3"
                        ></div>
                      </Spin>
                      {/* {maxWeekData ? (
                      <div className='chart-summary'>
                        最活跃日: {maxWeekData.day_name} ({maxWeekData.count}次)
                      </div>
                    ) : (
                      <div className='chart-summary' />
                    )} */}
                    </Card>
                  </Col>
                </Row>
              ),
            },
          ]}
        />
      </div>
    </div>
  );
};

export default MouseActivity;
