import { useState, useEffect, useRef } from 'react'
import { Layout, Tabs, Tree, Button } from 'antd'
import {
  // Math as CesiumMath,
  Viewer,
  Cartesian3,
  UrlTemplateImageryProvider,
  ScreenSpaceEventType,
  GeoJsonDataSource,
  Color,
  defined,
  ScreenSpaceEventHandler
} from 'cesium'
import {
  GlobalOutlined,
  TeamOutlined,
  FullscreenOutlined
} from '@ant-design/icons'
import {
  getDeviceGroupsByLocation,
  getCustomerGroupsTree
} from '../../service/deviceGroups'
import './styles.css'
import useSelect from '../../hooks/useSelect'
const { Sider, Content } = Layout

const DeviceDistribution = () => {
  const [activeTab, setActiveTab] = useState('location') // 当前选中的tab
  const [selectedKeys, setSelectedKeys] = useState([]) // 选中的节点
  const [expandedKeys, setExpandedKeys] = useState([]) // 展开的节点
  const [treeData, setTreeData] = useState(null) // 树数据
  const mapRef = useRef(null)
  const eventHandlerRef = useRef(null) // 添加事件处理器引用
  const [
    node,
    searchValue,
    autoExpandParent,
    filteredTreeData,
    setAutoExpandParent
  ] = useSelect(treeData, setExpandedKeys)

  // 四川省和重庆市的坐标数据
  const regionData = [
    {
      name: '四川省',
      longitude: 104.06637,
      latitude: 30.67118,
      zoomLevel: 8,
      height: 500000
    },
    {
      name: '重庆市',
      longitude: 106.55156,
      latitude: 29.56301,
      zoomLevel: 8,
      height: 500000
    }
  ]

  // 获取位置树
  const getLocationTree = async () => {
    try {
      const res = await getDeviceGroupsByLocation(1)
      const key = []
      const transformNode = (item, isFlag = false) => {
        key.push(item.id || item.locationId)
        return {
          title: item.groupName || item.description,
          key: item.id || item.locationId,
          value: item.id || item.locationId,
          isLocal: item.isLocal,
          ...(isFlag && { selectable: !!item.isLocal }),
          children:
            item.children && item.children.length > 0
              ? item.children.map(child => transformNode(child, isFlag))
              : item.deviceLocationTreeVos &&
                item.deviceLocationTreeVos.length > 0
              ? item.deviceLocationTreeVos.map(child =>
                  transformNode(child, isFlag)
                )
              : undefined
        }
      }

      const transformedData = res.map(item => transformNode(item))
      setSelectedKeys([key[0]])
      setTreeData([
        ...transformedData
        // { title: '未分组', key: 'isNotGroup', isLocal: 0 }
      ])
      setExpandedKeys(key)
    } catch (error) {
      console.error('Failed to fetch location tree:', error)
    }
  }

  // 获取客户分组树
  const fetchCustomerGroupsTree = async () => {
    try {
      const res = await getCustomerGroupsTree()
      const transformNode = item => ({
        title: item.ownershipName,
        key: item.ownershipId,
        value: item.ownershipId,
        children:
          item.children && item.children.length > 0
            ? item.children.map(child => transformNode(child))
            : undefined
      })
      const transformedData = res.map(item => transformNode(item))

      setTreeData([
        ...transformedData
        // { title: '未分组', key: 'isNotGroup', isLocal: 0 }
      ])
      setSelectedKeys([transformedData[0]?.key])
    } catch (error) {
      console.error('Failed to fetch customer groups tree:', error)
    }
  }

  useEffect(() => {
    ;(async () => {
      await getLocationTree()
    })()
  }, [])

  useEffect(() => {
    // 清理函数，在组件卸载时取消请求
    return () => {
      if (window.cancelToken) {
        window.cancelToken() // 取消请求
      }
    }
  }, [])

  // 切换tab
  const onGroupTypeChange = key => {
    setTreeData(null)
    setActiveTab(key)
    if (key === 'location') {
      getLocationTree()
    } else {
      fetchCustomerGroupsTree()
    }
  }

  // 树节点选中
  const onTreeSelect = selectedKeys => {
    if (window.cancelToken) {
      window.cancelToken()
    }
    if (selectedKeys.length > 0) {
      setSelectedKeys(selectedKeys)
    }
  }

  // 添加区域图标
  const addRegionIcons = () => {
    if (!mapRef.current) return

    regionData.forEach(region => {
      mapRef.current.entities.add({
        name: region.name,
        position: Cartesian3.fromDegrees(region.longitude, region.latitude), // 将经纬度转化为Cesium的三维坐标
        billboard: {
          image: '/mapIcon.png',
          width: 32,
          height: 32,
          scaleByDistance: { // 配置随距离变化缩放效果（近大远小）
            near: 1000000,
            nearValue: 1.0,
            far: 5000000,
            farValue: 0.3
          },
          distanceDisplayCondition: { // 设置显示距离范围（0米到1000万米可见）
            near: 0, 
            far: 10000000
          }
        }
      })
    })
  }

  // 处理图标点击事件
  const handleIconClick = () => {
    if (!mapRef.current) return

    eventHandlerRef.current = new ScreenSpaceEventHandler(mapRef.current.scene.canvas)
    
    eventHandlerRef.current.setInputAction((event) => {
      const pickedObject = mapRef.current.scene.pick(event.position)
      
      if (defined(pickedObject) && pickedObject.id) {
        // 1. 先重置所有区域图标为默认图片
        mapRef.current.entities.values.forEach(entity => {
          if (regionData.find(r => r.name === entity.name)) {
            entity.billboard.image = "/mapIcon.png"
          }
        })

        // 2. 设置当前点击的为选中图片
        const region = regionData.find(r => r.name === pickedObject.id.name)
        pickedObject.id.billboard.image = "/selectMapIcon.png"
        
        if (region) {
          // 飞行到指定区域
          mapRef.current.camera.flyTo({
            destination: Cartesian3.fromDegrees(
              region.longitude,
              region.latitude,
              region.height
            ),
            duration: 2.0,
            complete: () => {
              console.log(`已放大到${region.name}`)
            }
          })
        }
      }
    }, ScreenSpaceEventType.LEFT_CLICK)
  }

  // 鼠标悬停和移开事件
const handleIconHover = () => {
  if (!mapRef.current) return

  const canvas = mapRef.current.scene.canvas
  // 鼠标移动
  eventHandlerRef.current.setInputAction((event) => {
    const pickedObject = mapRef.current.scene.pick(event.endPosition)
    // 先全部还原
    mapRef.current.entities.values.forEach(entity => {
      if (regionData.find(r => r.name === entity.name) && entity.billboard) {
        entity.billboard.width = 32
        entity.billboard.height = 32
      }
    })
    // 悬停放大
    if (defined(pickedObject) && pickedObject.id) {
      const region = regionData.find(r => r.name === pickedObject.id.name)
      if (region && pickedObject.id.billboard) {
        pickedObject.id.billboard.width = 48
        pickedObject.id.billboard.height = 48
        canvas.style.cursor = 'pointer'
        return
      }
    }
    // 恢复为默认
    canvas.style.cursor = 'default'
  }, ScreenSpaceEventType.MOUSE_MOVE)
}

  // 初始化地图
  const initMap = () => {
    mapRef.current = new Viewer('map-container', {
      animation: false, //是否创建动画小器件，左下角仪表，可以修改动画播放倍率、暂停动画等
      baseLayerPicker: false, //是否显示右上角图层选择器，可以在小部件中选择底图和地形
      fullscreenButton: false, //是否显示全屏按钮
      creditContainer: document.createElement('div'), // 隐藏版权信息
      geocoder: false, //是否显示geocoder小器件，右上角查询按钮
      homeButton: false, //是否显示Home按钮
      infoBox: false,
      sceneModePicker: false, //是否显示3D/2D选择器
      selectionIndicator: false, //是否显示选取指示器组件，选择实体时，实体本身会出现一个绿色方块，如果为false，选择实体则不出现
      timeline: false, //是否显示时间轴
      navigationHelpButton: false, //是否显示右上角的帮助按钮
      scene3DOnly: true, //如果设置为true，则所有几何图形以3D模式绘制以节约GPU资源
      fullscreenElement: document.body, //全屏时渲染的HTML元素,
      useDefaultRenderLoop: true, //如果需要控制渲染循环，则设为true
      targetFrameRate: undefined, //使用默认render loop时的帧率
      showRenderLoopErrors: false, //如果设为true，将在一个HTML面板中显示错误信息
      automaticallyTrackDataSourceClocks: true, //自动追踪最近添加的数据源的时钟设置
      // imageryProvider: new UrlTemplateImageryProvider({
      //   url: `${import.meta.env.VITE_API_MAP_RUL}/map/{z}/{x}/{y}.jpg`,
      // }),
      imageryProvider: new UrlTemplateImageryProvider({
        url: 'https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
        style: 'default',
        format: 'image/png'
      })
    })
    // 视角位于中国全境
    mapRef.current.camera.setView({
      destination: Cartesian3.fromDegrees(104.0, 35.0, 3000000)
    })

    // 高亮中国区域
    GeoJsonDataSource.load('/resource/china.json', {
      stroke: Color.RED,
      fill: Color.TRANSPARENT,
      strokeWidth: 2
    }).then(dataSource => {
      mapRef.current.dataSources.add(dataSource)
    })

    // 禁用双击事件
    mapRef.current.cesiumWidget.screenSpaceEventHandler.removeInputAction(
      ScreenSpaceEventType.LEFT_DOUBLE_CLICK
    )

    // 鼠标拖动
    // mapRef.current.scene.screenSpaceCameraController.enableTranslate = false
    // 放大缩小
    // mapRef.current.scene.screenSpaceCameraController.enableZoom = true
    // 开启抗锯齿
    mapRef.current.scene.postProcessStages.fxaa.enabled = false

    // 添加区域图标
    addRegionIcons()
    
    // 添加点击事件
    handleIconClick()

    //添加图标悬停效果
    handleIconHover()
  }

  useEffect(() => {
    if (!mapRef.current) {
      initMap()
    }

    return () => {
      if (eventHandlerRef.current) {
        eventHandlerRef.current.destroy()
        eventHandlerRef.current = null
      }
      if (mapRef.current) {
        mapRef.current.entities.removeAll()
        mapRef.current = null
      }
    }
  }, [])

  const handleFullscreen = () => {
    const mapContainer = document.getElementById('map-container')
    mapContainer.requestFullscreen()
  }

  return (
    <Layout className='device-distribution-container'>
      <Sider width={300} className='group-sider'>
        <div className='group-header'>
          <Tabs
            activeKey={activeTab}
            onChange={onGroupTypeChange}
            items={[
              {
                key: 'location',
                label: (
                  <span>
                    <GlobalOutlined />
                    地理分组
                  </span>
                )
              },
              {
                key: 'customer',
                label: (
                  <span>
                    <TeamOutlined />
                    客户分组
                  </span>
                )
              }
            ]}
          />
        </div>
        {node()}
        {treeData && (
          <div className='group-tree-scroll'>
            <Tree
              showIcon
              onSelect={onTreeSelect}
              onExpand={keys => {
                setExpandedKeys(keys)
                setAutoExpandParent(false) // 用户手动展开后关闭自动展开
              }}
              selectedKeys={selectedKeys}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              treeData={searchValue ? filteredTreeData : treeData}
              blockNode
            />
          </div>
        )}
      </Sider>
      <Content
        className='device-distribution-content'
        style={{ position: 'relative' }}
      >
        <div id='map-container' style={{ height: '100%' }}></div>
        <Button
          type='primary'
          icon={<FullscreenOutlined />}
          onClick={handleFullscreen}
          style={{
            position: 'absolute',
            bottom: '20px',
            right: '20px',
            zIndex: 100,
            background: 'rgba(0,0,0,0.7)',
            border: '1px solid rgba(255,255,255,0.1)',
            backdropFilter: 'blur(10px)',
            borderRadius: '8px'
          }}
        />
      </Content>
    </Layout>
  )
}

export default DeviceDistribution
