import fetch from './fetch';

import {API_PREFIX} from "./constant"

// 升级包分页查询
export const getFirmwarePackageList = (params) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/firmware-packages/search`,
    method: 'GET',
    params:params
  })
}


// 升级包状态
export const getFirmwarePackageStatus = () => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/firmware-packages/statistics/status`,
    method: 'GET',
  })
}

// 创建升级包
export const createFirmwarePackage = (data) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/firmware-packages/upload`,
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 上传驱鼠器固件
export const uploadQsqFirmwarePackage = (file) => {
  const formData = new FormData();
  formData.append('file', file);
  return fetch({
    url: `${API_PREFIX["device-service"]}/firmware-packages/upload/qsq`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除升级包
export const delFirmwarePackage = (packageId) =>{
  return fetch({
    url:  `${API_PREFIX["device-service"]}/firmware-packages/${packageId}`,
    method:"delete"
  })
}

// 更新升级包信息
export const updateFirmwarePackage = (packageId,data) =>{
  return fetch({
    url: `${API_PREFIX["device-service"]}/firmware-packages/${packageId}`,
    method: "put",
    data
  })
}

// 获取升级包详情
export const getFirmwarePackagebyId = (packageId) =>{
  return fetch({
    url: `${API_PREFIX["device-service"]}/firmware-packages/${packageId}`,
    method: "get",
  })
}

// 下载升级包
export const downloadFirmwarePackage = (packageId) =>{
  return fetch({
    url: `${API_PREFIX["device-service"]}/firmware-packages/${packageId}/download`,
    method: "get",
    responseType: 'blob',
  })
}

// 审批升级包
export const approveFirmwarePackage = (packageId,approved) =>{
  return fetch({
    url: `${API_PREFIX["device-service"]}/firmware-packages/${packageId}/approve`,
    method: "get",
    params: {
      approved
    }
  })
}