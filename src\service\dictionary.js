import fetch from "./fetch";
import {API_PREFIX} from "./constant"

// 获取策略类型
export const getStrategyType = () => {
  return fetch({
    url: `${API_PREFIX["user-service"]}/dict/item/queryByTypeCode`,
    method: "GET",
    params: {
      typeCode: "strategy_type",
    },
  });
};

// 获取音频类型
export const getAudioType = () => {
  return fetch({
    url: `${API_PREFIX["user-service"]}/dict/item/queryByTypeCode`,
    method: "GET",
    params: {
      typeCode: "audio_type",
    },
  });
};

// 获取组织类型
export const getCustomerType = () => {
  return fetch({
    url: `${API_PREFIX["user-service"]}/dict/item/queryByTypeCode`,
    method: "GET",
    params: {
      typeCode: "customer_type",
    },
  });
};

// 获取国标信息
export const getNationalStandardInformationType = () => {
  return fetch({
    url: `${API_PREFIX["user-service"]}/dict/item/queryByTypeCode`,
    method: "GET",
    params: {
      typeCode: "national_standard_information",
    },
  });
};

// 获取摄像头类型
export const getCameraType = () => {
  return fetch({
    url: `${API_PREFIX["user-service"]}/dict/item/queryByTypeCode`,
    method: "GET",
    params: {
      typeCode: "camera_type",
    },
  });
};

