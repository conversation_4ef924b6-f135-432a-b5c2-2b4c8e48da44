// 登录日志记录工具

export const saveLoginResponse = (responseData) => {
  try {
    // 将登录响应数据保存到 localStorage
    if (responseData) {
      // 如果是对象，先转换为 JSON 字符串
      const dataToStore = typeof responseData === 'object' 
        ? JSON.stringify(responseData) 
        : responseData;
      localStorage.setItem('logEntry', dataToStore);
    }
  } catch (error) {
    console.error('保存登录日志失败:', error);
  }
};

export const getLoginEntry = () => {
  try {
    // 从 localStorage 获取登录数据
    const storedData = localStorage.getItem('logEntry');
    if (!storedData) return null;
    
    // 尝试解析 JSON 字符串，如果失败则返回原始字符串
    try {
      return JSON.parse(storedData);
    } catch (e) {
      return storedData;
    }
  } catch (error) {
    console.error('获取登录日志失败:', error);
    return null;
  }
}; 

export const getevent = (ratEventId) => {
  try {
    if(ratEventId){
      // 将鼠患事件ID保存到localStorage
      localStorage.setItem('ratEventId', ratEventId);
      return ratEventId;
    }
    // 如果没有参数，则返回存储的值
    return localStorage.getItem('ratEventId');
  } catch (error) {
    console.error("获取鼠患事件失败",error);
    return null;
  }
}

// 清空鼠患事件ID
export const clearEvent = () => {
  try {
    localStorage.removeItem('ratEventId');
  } catch (error) {
    console.error("清空鼠患事件失败", error);
  }
}