{"name": "mouse-repellent-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/icons": "^5.3.0", "@ant-design/plots": "^2.2.5", "@ant-design/pro-components": "^2.6.49", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@turf/turf": "^6.5.0", "antd": "^5.14.1", "axios": "^1.6.7", "cesium": "^1.106.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.4.2", "immer": "^10.0.3", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "mobx": "^6.12.0", "mobx-react-lite": "^4.0.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-router-dom": "^6.22.1", "recharts": "^2.12.0", "yup": "^1.3.3"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "vite": "^5.4.2", "vite-plugin-cesium": "^1.2.23"}}