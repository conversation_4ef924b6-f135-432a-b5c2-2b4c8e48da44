// API基础URL - 可以根据实际部署环境手动配置

import { API_PREFIX } from "./constant";
import { getLoginEntry } from '../utils/loginLogger';
import { message } from 'antd';

const handleApiError = (response) => {
  return response.json().then(data => {
    if (response.status === 401) {
      message.error(data?.message);
      setTimeout(() => {
        window.location.href = '/login'
      }, 500);
    } else if (response.status === 400 || response.status === 404 || response.status === 500) {
      message.error(data?.message);
    } else if (response.status === 403) {
      // Handle forbidden
    }
    return Promise.reject(data);
  });
};

// 检查响应数据是否包含错误码
const checkResponseData = (data) => {
  if (data && data.code && data.code !== 200) {
    message.error(data.message || '操作失败');
    return Promise.reject(data);
  }
  return data;
};

// 日志管理相关接口
export const logManagementApi = {
  // 分页获取日志列表
  // getLogList: (pageNum = 1, pageSize = 10, operationType = 1, filters = {}) =>
  //   fetch(`${API_PREFIX["audit-service"]}/system/log/page`, {
  //     method: 'POST',
  //     headers: {
  //       'Content-Type': 'application/json',
  //       'Authorization': getLoginEntry()
  //     },
  //     body: JSON.stringify({
  //       pageNum,
  //       pageSize,
  //       ...filters
  //     })
  //   }).then(response => {
  //     if (!response.ok) return handleApiError(response);
  //     return response.json();
  //   }),

  // 获取日志详情
  // getLogDetail: (id) =>
  //   fetch(`${API_PREFIX["audit-service"]}/system/log/${id}`, {
  //     headers: {
  //       'Accept': 'application/json',
  //       'Authorization': getLoginEntry()
  //     }
  //   }).then(response => {
  //     if (!response.ok) return handleApiError(response);
  //     return response.json();
  //   }),

  // 导出日志数据
  exportLogData: (params) =>
    fetch(`${API_PREFIX["audit-service"]}/operation/log/export`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify(params)
    }).then(response => {
      if (!response.ok) return handleApiError(response);
      return response.blob().then(checkResponseData);
    }),

  // 导出系统日志数据
  // exportSystemLogData: (params) =>
  //   fetch(`${API_PREFIX["audit-service"]}/system/log/export`, {
  //     method: 'POST',
  //     headers: {
  //       'Content-Type': 'application/json',
  //       'Authorization': getLoginEntry()
  //     },
  //     body: JSON.stringify(params)
  //   }).then(response => {
  //     if (!response.ok) return handleApiError(response);
  //     return response.blob();
  //   }),

  // 导出操作日志数据
  exportOperationLogData: (params) =>
    fetch(`${API_PREFIX["audit-service"]}/operation/log/export`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify(params)
    }).then(response => {
      // if (!response.ok) return handleApiError(response);
      return response.blob().then(checkResponseData);
    }),

  // 获取操作日志列表
  getOperationLogList: (params) =>
    fetch(`${API_PREFIX["audit-service"]}/operation/log/page`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify(params)
    }).then(response => {
      if (!response.ok) return handleApiError(response);
      return response.json().then(checkResponseData);
    }),

  // 获取操作日志详情
  getOperationLogDetail: (id) =>
    fetch(`${API_PREFIX["audit-service"]}/operation/log/${id}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': getLoginEntry()
      }
    }).then(response => {
      if (!response.ok) return handleApiError(response);
      return response.json().then(checkResponseData);
    }),

  // 系统日志搜索
  // searchSystemLog: (params) =>
  //   fetch(`${API_PREFIX["audit-service"]}/system/log/page`, {
  //     method: 'POST',
  //     headers: {
  //       'Content-Type': 'application/json',
  //       'Authorization': getLoginEntry()
  //     },
  //     body: JSON.stringify(params)
  //   }).then(response => {
  //     if (!response.ok) return handleApiError(response);
  //     return response.json();
  //   }),

  // 操作日志搜索
  searchOperationLog: (params) =>
    fetch(`${API_PREFIX["audit-service"]}/operation/log/page`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify(params)
    }).then(response => {
      if (!response.ok) return handleApiError(response);
      return response.json().then(checkResponseData);
    }),

  // 获取用户日志列表
  getUserLogList: (params) =>
    fetch(`${API_PREFIX["user-service"]}/user/security/log/page`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify(params)
    }).then(response => {
      if (!response.ok) return handleApiError(response);
      return response.json().then(checkResponseData);
    })
};

