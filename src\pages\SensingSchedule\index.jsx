import React, { useState } from 'react';
import {
  Layout,
  Card,
  Tabs,
  Input,
  Button,
  Space,
  Table,
  Tag,
  Progress,
  Statistic,
  DatePicker,
  Select,
  Tree,
  Badge,
  Drawer,
  Row,
  Col,
  Tooltip,
  Modal,
  Form,
  Radio,
  Slider,
  Timeline,
  Descriptions
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  FilterOutlined,
  GlobalOutlined,
  TeamOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  ThunderboltOutlined,
  BulbOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  EyeOutlined,
  EditOutlined,
  BarChartOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { Line, Radar, Column } from '@ant-design/plots';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';
import './styles.css';

dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

const { Sider, Content } = Layout;
const { Search } = Input;
const { RangePicker } = DatePicker;

const SensingSchedule = () => {
  const [groupType, setGroupType] = useState('location');
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [timeRange, setTimeRange] = useState('today');
  const [viewMode, setViewMode] = useState('list');
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [executeModalVisible, setExecuteModalVisible] = useState(false);
  const [executeForm] = Form.useForm();

  // Mock data for statistics
  const statistics = [
    {
      title: '今日执行次数',
      value: 128,
      icon: <ThunderboltOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
      trend: { value: 12, type: 'up' }
    },
    {
      title: '执行成功率',
      value: 95.8,
      icon: <BulbOutlined style={{ fontSize: '24px', color: '#52c41a' }} />,
      suffix: '%'
    },
    {
      title: '预测准确度',
      value: 92.5,
      icon: <RobotOutlined style={{ fontSize: '24px', color: '#faad14' }} />,
      suffix: '%'
    },
    {
      title: '能源优化率',
      value: 15.2,
      icon: <BarChartOutlined style={{ fontSize: '24px', color: '#722ed1' }} />,
      suffix: '%'
    }
  ];

  // Mock data for tree
  const treeData = [
    {
      title: '杭州总部',
      key: '0-0',
      icon: <GlobalOutlined />,
      children: [
        {
          title: '科技大厦',
          key: '0-0-0',
          icon: <EnvironmentOutlined />,
          children: [
            {
              title: (
                <Space>
                  3楼会议室A
                  <Badge count={5} size="small" />
                  <Tag color="green">已启用</Tag>
                </Space>
              ),
              key: '0-0-0-0',
              isLeaf: true
            }
          ]
        }
      ]
    }
  ];

  // Mock data for execution records
  const records = [
    {
      key: '1',
      status: 'success',
      executeTime: '2024-02-20 15:30:00',
      type: 'auto',
      strategy: '智能阵型策略A',
      deviceCount: 5,
      paramCount: 8,
      accuracy: 95.5,
      energyImpact: -12.5,
      nextSchedule: '2024-02-20 18:30:00'
    },
    {
      key: '2',
      status: 'processing',
      executeTime: '2024-02-20 15:00:00',
      type: 'manual',
      strategy: '高密度协同策略B',
      deviceCount: 3,
      paramCount: 6,
      accuracy: 88.5,
      energyImpact: -8.2,
      nextSchedule: '2024-02-20 19:00:00'
    }
  ];

  // Table columns
  const columns = [
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Badge
          status={
            status === 'success' ? 'success' :
            status === 'processing' ? 'processing' :
            status === 'warning' ? 'warning' : 'error'
          }
          text={
            status === 'success' ? '成功' :
            status === 'processing' ? '执行中' :
            status === 'warning' ? '部分成功' : '失败'
          }
        />
      )
    },
    {
      title: '执行时间',
      dataIndex: 'executeTime',
      key: 'executeTime',
      render: (time) => (
        <Tooltip title={time}>
          {dayjs(time).fromNow()}
        </Tooltip>
      )
    },
    {
      title: '执行类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={
          type === 'auto' ? 'blue' :
          type === 'manual' ? 'green' : 'orange'
        }>
          {type === 'auto' ? '自动执行' :
           type === 'manual' ? '手动触发' : '定期评估'}
        </Tag>
      )
    },
    {
      title: '策略名称',
      dataIndex: 'strategy',
      key: 'strategy'
    },
    {
      title: '目标设备数',
      dataIndex: 'deviceCount',
      key: 'deviceCount'
    },
    {
      title: '调整参数数',
      dataIndex: 'paramCount',
      key: 'paramCount'
    },
    {
      title: '预测准确度',
      dataIndex: 'accuracy',
      key: 'accuracy',
      render: (accuracy) => (
        <Tooltip title={`${accuracy}%`}>
          <Progress
            percent={accuracy}
            size="small"
            status={
              accuracy >= 90 ? 'success' :
              accuracy >= 70 ? 'normal' : 'exception'
            }
          />
        </Tooltip>
      )
    },
    {
      title: '能源影响',
      dataIndex: 'energyImpact',
      key: 'energyImpact',
      render: (impact) => (
        <Tag color={impact < 0 ? 'green' : 'red'}>
          {impact < 0 ? `节省 ${Math.abs(impact)}%` : `增加 ${impact}%`}
        </Tag>
      )
    },
    {
      title: '下次执行',
      dataIndex: 'nextSchedule',
      key: 'nextSchedule',
      render: (time) => (
        <Tooltip title={time}>
          {dayjs(time).format('HH:mm:ss')}
        </Tooltip>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedRecord(record);
              setDrawerVisible(true);
            }}
          >
            详情
          </Button>
          <Button
            type="text"
            icon={<PlayCircleOutlined />}
            onClick={() => setExecuteModalVisible(true)}
          >
            执行
          </Button>
        </Space>
      )
    }
  ];

  // Execution details drawer
  const renderExecutionDetails = () => {
    if (!selectedRecord) return null;

    // Mock data for parameter adjustments
    const parameterData = [
      { parameter: '超声波频率', before: '25kHz', after: '28kHz', change: '+12%' },
      { parameter: '发射功率', before: '80W', after: '70W', change: '-12.5%' },
      { parameter: '扫描角度', before: '120°', after: '150°', change: '+25%' }
    ];

    // Mock data for learning curve
    const learningData = Array.from({ length: 24 }, (_, i) => ({
      time: `${i}:00`,
      accuracy: 75 + Math.random() * 20
    }));

    const learningConfig = {
      data: learningData,
      xField: 'time',
      yField: 'accuracy',
      smooth: true,
      point: {
        size: 2,
        shape: 'circle'
      }
    };

    return (
      <div className="execution-details">
        <Tabs
          items={[
            {
              key: 'basic',
              label: '基本信息',
              children: (
                <Card>
                  <Descriptions column={2}>
                    <Descriptions.Item label="执行ID">EXEC-20240220-001</Descriptions.Item>
                    <Descriptions.Item label="策略名称">{selectedRecord.strategy}</Descriptions.Item>
                    <Descriptions.Item label="执行时间">{selectedRecord.executeTime}</Descriptions.Item>
                    <Descriptions.Item label="执行类型">{selectedRecord.type}</Descriptions.Item>
                    <Descriptions.Item label="目标设备数">{selectedRecord.deviceCount}</Descriptions.Item>
                    <Descriptions.Item label="调整参数数">{selectedRecord.paramCount}</Descriptions.Item>
                  </Descriptions>
                </Card>
              )
            },
            {
              key: 'parameters',
              label: '参数调整',
              children: (
                <Card>
                  <Table
                    dataSource={parameterData}
                    columns={[
                      { title: '参数名称', dataIndex: 'parameter' },
                      { title: '调整前', dataIndex: 'before' },
                      { title: '调整后', dataIndex: 'after' },
                      {
                        title: '变化率',
                        dataIndex: 'change',
                        render: (text) => (
                          <Tag color={text.startsWith('+') ? 'red' : 'green'}>{text}</Tag>
                        )
                      }
                    ]}
                    pagination={false}
                  />
                </Card>
              )
            },
            {
              key: 'learning',
              label: '学习效果',
              children: (
                <Card>
                  <Line {...learningConfig} />
                </Card>
              )
            }
          ]}
        />
      </div>
    );
  };

  // Manual execution modal
  const renderExecuteModal = () => {
    return (
      <Modal
        title="手动执行调度"
        open={executeModalVisible}
        onOk={() => {
          executeForm.validateFields().then(values => {
            console.log('Execute values:', values);
            setExecuteModalVisible(false);
            executeForm.resetFields();
          });
        }}
        onCancel={() => {
          setExecuteModalVisible(false);
          executeForm.resetFields();
        }}
        width={720}
      >
        <Form
          form={executeForm}
          layout="vertical"
        >
          <Form.Item
            name="strategy"
            label="执行策略"
            rules={[{ required: true }]}
          >
            <Select>
              <Select.Option value="strategy1">智能阵型策略A</Select.Option>
              <Select.Option value="strategy2">高密度协同策略B</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="mode"
            label="执行模式"
            rules={[{ required: true }]}
          >
            <Radio.Group>
              <Radio.Button value="standard">标准</Radio.Button>
              <Radio.Button value="conservative">保守</Radio.Button>
              <Radio.Button value="aggressive">激进</Radio.Button>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            name="priority"
            label="执行优先级"
            rules={[{ required: true }]}
          >
            <Slider
              marks={{
                0: '低',
                50: '中',
                100: '高'
              }}
              step={null}
              defaultValue={50}
            />
          </Form.Item>

          <Form.Item
            name="devices"
            label="目标设备"
            rules={[{ required: true }]}
          >
            <Select mode="multiple" placeholder="选择目标设备">
              <Select.Option value="device1">设备1 (USR-2024-001)</Select.Option>
              <Select.Option value="device2">设备2 (USR-2024-002)</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    );
  };

  return (
    <div className="sensing-schedule">
      {/* Page Header */}
      <div className="page-header">
        <div className="header-left">
          <h2>自适应调度</h2>
        </div>
        <div className="header-right">
          <Space>
            <Select
              defaultValue="today"
              style={{ width: 120 }}
              options={[
                { value: 'today', label: '今天' },
                { value: 'yesterday', label: '昨天' },
                { value: 'week', label: '本周' },
                { value: 'month', label: '本月' },
                { value: 'custom', label: '自定义' }
              ]}
              onChange={(value) => setTimeRange(value)}
            />
            {timeRange === 'custom' && (
              <RangePicker />
            )}
            {/* <Button icon={<SearchOutlined />}>搜索</Button> */}
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={() => setExecuteModalVisible(true)}
            >
              手动执行
            </Button>
          </Space>
        </div>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} className="statistics-cards">
        {statistics.map((stat, index) => (
          <Col span={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
                suffix={
                  <>
                    {stat.suffix}
                    {stat.trend && (
                      <span style={{ marginLeft: '8px', fontSize: '14px' }} className={`trend-${stat.trend.type}`}>
                        {stat.trend.type === 'up' ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                        {stat.trend.value}% 环比
                      </span>
                    )}
                  </>
                }
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Main Content */}
      <Layout className="main-content" style={{width: "100%", paddingBottom: "24px"}}>
        {/* Left Sider */}
        <Sider width={300} className="group-sider">
          <Card className="tree-card">
            <Tabs
              activeKey={groupType}
              onChange={setGroupType}
              items={[
                {
                  key: 'location',
                  label: (
                    <span>
                      <GlobalOutlined />
                      地理分组
                    </span>
                  )
                },
                {
                  key: 'customer',
                  label: (
                    <span>
                      <TeamOutlined />
                      客户分组
                    </span>
                  )
                }
              ]}
            />
            <div className="tree-search">
              <Search
                placeholder="搜索设备或分组..."
                allowClear
              />
            </div>
            <Tree
              treeData={treeData}
              selectedKeys={selectedKeys}
              onSelect={setSelectedKeys}
            />
          </Card>
        </Sider>

        {/* Right Content */}
        <Content className="record-content" style={{height: "100%"}}>
          <Card className="filter-bar">
            <Space wrap>
              <Select
                defaultValue="all"
                style={{ width: 120 }}
                options={[
                  { value: 'all', label: '全部类型' },
                  { value: 'auto', label: '自动执行' },
                  { value: 'manual', label: '手动触发' },
                  { value: 'periodic', label: '定期评估' }
                ]}
              />
              <Select
                defaultValue="all"
                style={{ width: 120 }}
                options={[
                  { value: 'all', label: '全部状态' },
                  { value: 'success', label: '成功' },
                  { value: 'partial', label: '部分成功' },
                  { value: 'failed', label: '失败' }
                ]}
              />
              <Select
                defaultValue="all"
                style={{ width: 120 }}
                options={[
                  { value: 'all', label: '全部策略' },
                  { value: 'strategy1', label: '智能阵型策略A' },
                  { value: 'strategy2', label: '高密度协同策略B' }
                ]}
              />
              <Button icon={<FilterOutlined />}>
                更多筛选
              </Button>
            </Space>
          </Card>

          <Card className="record-table">
            <Table
              columns={columns}
              style={{width: "100%",overflowX: "auto"}}
              dataSource={records}
              pagination={{
                total: 100,
                pageSize: 10,
                showQuickJumper: true,
                showSizeChanger: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </Card>
        </Content>
      </Layout>

      {/* Execution Detail Drawer */}
      <Drawer
        title="执行详情"
        placement="right"
        width={720}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        {renderExecutionDetails()}
      </Drawer>

      {/* Manual Execution Modal */}
      {renderExecuteModal()}
    </div>
  );
};

export default SensingSchedule;