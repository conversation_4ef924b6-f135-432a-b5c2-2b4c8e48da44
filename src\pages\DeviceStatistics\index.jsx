import { useState, useEffect, useRef } from "react";
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Select,
  DatePicker,
  Statistic,
  Tabs,
  Spin,
} from "antd";
import {
  DownloadOutlined,
  DashboardOutlined,
  WifiOutlined,
  HeartOutlined,
  AlertOutlined,
} from "@ant-design/icons";
import echarts from "./../../utils/echart";
import {
  getDevicesStatisticsData,
  exportDevicesStatisticsData,
} from "../../service/statistics";
import { getDeviceRepellentList } from "../../service/deviceRepellent";
import { debounce, max, min, set } from "lodash";
const { Option } = Select;
import dayjs from "dayjs";
import useGroup from "../../hooks/useGroup";
import "./styles.css";

const { RangePicker } = DatePicker;

const DeviceStatistics = () => {
  const [deviceData, setDeviceData] = useState({}); // 设备运行数据
  const [selectedDevice, setSelectedDevice] = useState([]); // 选中的驱鼠器
  const [deviceRepellentData, setDeviceRepellentData] = useState([]); // 获取驱鼠器数据
  const [deviceRepellentLoading, setDeviceRepellentLoading] = useState(false); //驱鼠器loading
  const [searchDeviceData, setSearchDeviceData] = useState({
    keyword: "",
    size: 50,
    page: 0,
  }); // 分页驱鼠器
  const [totalPages, setTotalPages] = useState(0);
  const [searchData, setSearchData] = useState({
    dateType: "hour",
    startTime: dayjs().format("YYYY-MM-DD 00:00:00"),
    endTime: dayjs().format("YYYY-MM-DD 23:59:59"),
    deviceIds: [],
    // isNotGroup: 1,
    // regionId: "",
    // customerId: '',
    // groupType: 1
  });
  const [activeTab, setActiveTab] = useState("overview");
  const [timeRange, setTimeRange] = useState("hour");
  const [dateRange, setDateRange] = useState([dayjs(), dayjs()]);
  const [loading, setLoading] = useState(false);
  const chartInstance1 = useRef(null);
  const chartInstance2 = useRef(null);
  const chartInstance3 = useRef(null);
  const chartInstance4 = useRef(null);
  const [gropNode, gropSearch] = useGroup();

  const handleTimeRangeChange = (value) => {
    setTimeRange(value);
    setSearchData((prev) => ({
      ...prev,
      dateType: value,
    }));
  };

  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
    setSearchData((prev) => ({
      ...prev,
      startTime: dates[0].format("YYYY-MM-DD 00:00:00"),
      endTime: dates[1].format("YYYY-MM-DD 23:59:59"),
    }));
  };

  const handleExport = async () => {
    try {
      const result = await exportDevicesStatisticsData({
        ...searchData,
        isNotGroup: gropSearch.isNotGroup,
        regionId: gropSearch.groupId,
        customerId: gropSearch.orgId,
        groupType: gropSearch.groupType,
      });
      const blob = new Blob([result], { type: "application/xlsx" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "download.xlsx";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Export failed:", error);
    }
  };

  const fetchData = async (obj = {}) => {
    if (!gropSearch) {
      return;
    }
    setLoading(true);
    try {
      const res = await getDevicesStatisticsData({
        ...searchData,
        ...obj,
        isNotGroup: gropSearch.isNotGroup,
        regionId: gropSearch.groupId,
        customerId: gropSearch.orgId,
        groupType: gropSearch.groupType,
      });
      setDeviceData(res);
      setLoading(false);
    } catch (error) {
      console.log(error);
    }
  };

  // 获取驱鼠器列表
  const fetchDiviceList = async (obj = {}) => {
    try {
      const res = await getDeviceRepellentList({
        ...searchDeviceData,
        ...gropSearch,
        ...obj,
      });
      setTotalPages(res.totalPages);
      return res.content;
    } catch (error) {
      console.error("Failed to fetch camera list:", error);
    }
  };

  // 下拉搜索
  const handleSearch = async (value) => {
    setDeviceRepellentLoading(true);
    try {
      const data = await fetchDiviceList({ keyword: value, page: 0 });
      setDeviceRepellentData([{ deviceNo: "全选", deviceId: "全选" }, ...data]);
    } catch (error) {
      console.error("Failed to search cameras:", error);
    } finally {
      setDeviceRepellentLoading(false);
    }
  };

  // 滚动加载
  const loadMore = async (e) => {
    if (deviceRepellentLoading) return;
    const bottom =
      e.target.scrollHeight === e.target.scrollTop + e.target.clientHeight;
    if (bottom && totalPages - 1 > searchDeviceData.page) {
      setSearchDeviceData({
        ...searchDeviceData,
        page: searchDeviceData.page + 1,
      });
      setDeviceRepellentLoading(true);
      const newOptions = await fetchDiviceList({
        page: searchDeviceData.page + 1,
      });
      setDeviceRepellentData((prevOptions) => [...prevOptions, ...newOptions]);
      setDeviceRepellentLoading(false);
    }
  };

  // 中间的统计数据
  const coreMetrics = [
    {
      title: "设备在线率",
      value: deviceData?.head?.deviceOnlineRate?.onlineRate ?? "无",
      icon: <WifiOutlined style={{ color: "#1890ff", fontSize: 24 }} />,
      suffix: "%",
    },
    {
      title: "平均健康评分",
      value: deviceData?.head?.deviceHealth ?? "无",
      icon: <HeartOutlined style={{ color: "#52c41a", fontSize: 24 }} />,
    },

    // {
    //   title: '活跃时间占比',
    //   value: deviceData?.onlineRate ?? '无',
    //   icon: <ClockCircleOutlined style={{ color: '#eb2f96', fontSize: 24 }} />
    // }
  ];

  function chartResize() {
    chartInstance1.current?.resize();
    chartInstance2.current?.resize();
    chartInstance3.current?.resize();
    chartInstance4.current?.resize();
  }
  window.addEventListener("resize", chartResize);

  // chart初始化
  const chartInit1 = (chartData) => {
    let chartDom = document.getElementById("device-statistics-chart1");
    if (!chartDom) {
      return;
    }

    if (!chartInstance1.current) {
      chartInstance1.current = echarts.init(chartDom);
    }

    // 检查是否有数据
    const hasData = chartData && chartData.length > 0;

    // 默认时间轴数据（0-23点）
    const defaultTimeData = Array.from({ length: 24 }, (_, i) => `${i}:00`);

    const option = {
      // 当暂无数据时显示标题
      ...(!hasData && {
        title: {
          text: "暂无数据",
          left: "center",
          top: "middle",
          textStyle: {
            fontSize: 18,
            fontWeight: "bold",
            color: "#666",
          },
        },
      }),
      grid: {
        left: "5%",
        right: "5%",
        bottom: "10%",
        top: "8%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            show: false,
            backgroundColor: "#6a7985",
            fontSize: "12px",
          },
        },
      },
      legend: {
        show: false,
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        axisLabel: {
          fontSize: "12px", // 可选：调整字体大小
        },
        axisLine: {
          show: true, // 显示x轴轴线
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        data: hasData ? chartData.map((item) => item.time) : defaultTimeData,
      },
      yAxis: {
        type: "value",
        name: "%",
        nameLocation: "end",
        nameGap: 10,
        minInterval: 1,
        min: 0,
        max: hasData ? undefined : 100,
        nameTextStyle: {
          fontSize: "12px",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
        axisLine: {
          show: true, // 显示y轴轴线
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
      },
      // 当暂无数据时显示提示文字
      ...(!hasData && {
        graphic: [
          {
            type: "text",
            left: "center",
            top: "middle",
            z: 100,
            style: {
              text: "暂无数据",
              fontSize: 18,
              fontWeight: "bold",
              fill: "#666",
            },
          },
        ],
      }),
      ...(chartData &&
        chartData.length > 30 && {
          toolbox: {
            feature: {
              dataZoom: {
                yAxisIndex: "none",
                title: {
                  zoom: "区域缩放",
                  back: "区域缩放还原",
                },
              },
              restore: {
                title: "还原",
              },
            },
            right: 20,
          },
          dataZoom: [
            {
              type: "slider",
              xAxisIndex: 0,
              start: 0,
              end: 100,
            },
            {
              type: "inside",
              xAxisIndex: [0],
              zoomLock: false,
              moveOnMouseMove: true,
              moveOnMouseWheel: true,
            },
          ],
        }),
      series: [
        {
          type: "line",
          smooth: true,
          emphasis: {
            focus: "series",
          },
          data: hasData ? chartData.map((item) => item.onlineRate) : [],
        },
      ],
    };
    option && chartInstance1.current.setOption(option, true);
  };

  // chart初始化
  const chartInit2 = (chartData) => {
    let chartDom = document.getElementById("device-statistics-chart2");
    if (!chartDom) {
      return;
    }

    if (!chartInstance2.current) {
      chartInstance2.current = echarts.init(chartDom);
    }

    // 检查是否有数据
    const hasData = chartData && chartData.length > 0;

    // 默认时间轴数据（0-23点）
    const defaultTimeData = Array.from({ length: 24 }, (_, i) => `${i}:00`);

    const option = {
      // 当暂无数据时显示标题
      ...(!hasData && {
        title: {
          text: "暂无数据",
          left: "center",
          top: "middle",
          textStyle: {
            fontSize: 18,
            fontWeight: "bold",
            color: "#666",
          },
        },
      }),
      grid: {
        left: "5%",
        right: "5%",
        bottom: "10%",
        top: "8%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            show: false,
            backgroundColor: "#6a7985",
            fontSize: "12px",
          },
        },
      },
      legend: {
        data: ["最高温", "平均温", "最低温"],
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        axisLabel: {
          fontSize: "12px", // 可选：调整字体大小
        },
        axisLine: {
          show: true, // 显示x轴轴线
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        data: hasData ? chartData.map((item) => item.time) : defaultTimeData,
      },
      yAxis: {
        type: "value",
        name: "℃",
        nameLocation: "end",
        nameGap: 10,
        minInterval: 1,
        min: 0,
        max: hasData ? undefined : 100,
        nameTextStyle: {
          fontSize: "12px",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
        axisLine: {
          show: true, // 显示y轴轴线
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
      },
      // 当暂无数据时显示提示文字
      ...(!hasData && {
        graphic: [
          {
            type: "text",
            left: "center",
            top: "middle",
            z: 100,
            style: {
              text: "暂无数据",
              fontSize: 18,
              fontWeight: "bold",
              fill: "#666",
            },
          },
        ],
      }),
      ...(chartData &&
        chartData.length > 30 && {
          toolbox: {
            feature: {
              dataZoom: {
                yAxisIndex: "none",
                title: {
                  zoom: "区域缩放",
                  back: "区域缩放还原",
                },
              },
              restore: {
                title: "还原",
              },
            },
            right: 20,
          },
          dataZoom: [
            {
              type: "slider",
              xAxisIndex: 0,
              start: 0,
              end: 100,
            },
            {
              type: "inside",
              xAxisIndex: [0],
              zoomLock: false,
              moveOnMouseMove: true,
              moveOnMouseWheel: true,
            },
          ],
        }),
      series: [
        {
          name: "最高温",
          type: "line",
          // stack: 'Total',
          data: hasData ? chartData.map((item) => item.maxTemperature) : [],
        },
        {
          name: "平均温",
          type: "line",
          // stack: 'Total',
          data: hasData ? chartData.map((item) => item.avgTemperature) : [],
        },
        {
          name: "最低温",
          type: "line",
          // stack: 'Total',
          data: hasData ? chartData.map((item) => item.minTemperature) : [],
        },
      ],
    };
    option && chartInstance2.current.setOption(option, true);
  };

  // chart初始化
  const chartInit3 = (chartData) => {
    let chartDom = document.getElementById("device-statistics-chart3");
    if (!chartDom) {
      return;
    }

    if (!chartInstance3.current) {
      chartInstance3.current = echarts.init(chartDom);
    }

    // 检查是否有数据
    const hasData = chartData && chartData.length > 0;

    // 默认时间轴数据（0-23点）
    const defaultTimeData = Array.from({ length: 24 }, (_, i) => `${i}:00`);

    const option = {
      // 当暂无数据时显示标题
      ...(!hasData && {
        title: {
          text: "暂无数据",
          left: "center",
          top: "middle",
          textStyle: {
            fontSize: 18,
            fontWeight: "bold",
            color: "#666",
          },
        },
      }),
      grid: {
        left: "5%",
        right: "5%",
        bottom: "10%",
        top: "8%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            show: false,
            backgroundColor: "#6a7985",
            fontSize: "12px",
          },
        },
      },
      legend: {
        data: ["最高湿度", "平均湿度", "最低湿度"],
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        axisLabel: {
          fontSize: "12px", // 可选：调整字体大小
        },
        axisLine: {
          show: true, // 显示x轴轴线
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        data: hasData ? chartData.map((item) => item.time) : defaultTimeData,
      },
      yAxis: {
        type: "value",
        name: "%RH",
        nameLocation: "end",
        nameGap: 10,
        minInterval: 1,
        min: 0,
        max: hasData ? undefined : 100,
        nameTextStyle: {
          fontSize: "12px",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
        axisLine: {
          show: true, // 显示y轴轴线
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
      },
      // 当暂无数据时显示提示文字
      ...(!hasData && {
        graphic: [
          {
            type: "text",
            left: "center",
            top: "middle",
            z: 100,
            style: {
              text: "暂无数据",
              fontSize: 18,
              fontWeight: "bold",
              fill: "#666",
            },
          },
        ],
      }),
      ...(chartData &&
        chartData.length > 30 && {
          toolbox: {
            feature: {
              dataZoom: {
                yAxisIndex: "none",
                title: {
                  zoom: "区域缩放",
                  back: "区域缩放还原",
                },
              },
              restore: {
                title: "还原",
              },
            },
            right: 20,
          },
          dataZoom: [
            {
              type: "slider",
              xAxisIndex: 0,
              start: 0,
              end: 100,
            },
            {
              type: "inside",
              xAxisIndex: [0],
              zoomLock: false,
              moveOnMouseMove: true,
              moveOnMouseWheel: true,
            },
          ],
        }),
      series: [
        {
          name: "最高湿度",
          type: "line",
          // stack: 'Total',
          data: hasData ? chartData.map((item) => item.maxHumidity) : [],
        },
        {
          name: "平均湿度",
          type: "line",
          // stack: 'Total',
          data: hasData ? chartData.map((item) => item.avgHumidity) : [],
        },
        {
          name: "最低湿度",
          type: "line",
          // stack: 'Total',
          data: hasData ? chartData.map((item) => item.minHumidity) : [],
        },
      ],
    };
    option && chartInstance3.current.setOption(option, true);
  };


  // chart初始化
  const chartInit4 = (chartData) => {
    console.log('chartInit4 called with data:', chartData);

    let chartDom = document.getElementById("device-statistics-chart4");
    if (!chartDom) {
      console.log('Chart DOM not found'); // 添加调试信息
      return;
    }

    if (!chartInstance4.current) {
      chartInstance4.current = echarts.init(chartDom);
    }

    // 检查是否有数据 - 不仅要检查数组长度，还要检查是否有实际的计数数据
    const hasData = chartData && chartData.length > 0 && chartData.some(item => item.count > 0);
    console.log('hasData:', hasData, 'chartData length:', chartData?.length); // 添加调试信息

    // 默认时间轴数据（0-23点）
    const defaultTimeData = Array.from({ length: 24 }, (_, i) => `${i}:00`);

    // 当暂无数据时，使用简化的配置
    const option = !hasData ? {
      title: {
        text: "暂无数据",
        left: "center",
        top: "center",
        textStyle: {
          fontSize: 18,
          fontWeight: "bold",
          color: "#666",
        },
      },
      grid: {
        left: "5%",
        right: "5%",
        bottom: "10%",
        top: "15%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: defaultTimeData,
        axisLabel: {
          fontSize: "12px",
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
      },
      yAxis: {
        type: "value",
        name: "次",
        nameLocation: "end",
        min: 0,
        max: 100,
        nameGap: 10,
        nameTextStyle: {
          fontSize: "12px",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
      },
      series: [{
        type: "bar",
        data: [],
      }],
    } : {
      grid: {
        left: "5%",
        right: "5%",
        bottom: "10%",
        top: "8%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            show: false,
            backgroundColor: "#6a7985",
            fontSize: "12px",
          },
        },
      },
      legend: {
        show: false,
      },
      xAxis: {
        type: "category",
        boundaryGap: true,
        axisLabel: {
          fontSize: "12px",
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        data: chartData.map((item) => item[timeRange] || item.date),
      },
      yAxis: {
        type: "value",
        name: "次",
        nameLocation: "end",
        minInterval: 1,
        min: 0,
        nameGap: 10,
        nameTextStyle: {
          fontSize: "12px",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#B0B0B0",
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
      },
      ...(chartData &&
        chartData.length > 30 && {
          toolbox: {
            feature: {
              dataZoom: {
                yAxisIndex: "none",
                title: {
                  zoom: "区域缩放",
                  back: "区域缩放还原",
                },
              },
              restore: {
                title: "还原",
              },
            },
            right: 20,
          },
          dataZoom: [
            {
              type: "slider",
              xAxisIndex: 0,
              start: 0,
              end: 100,
            },
            {
              type: "inside",
              xAxisIndex: [0],
              zoomLock: false,
              moveOnMouseMove: true,
              moveOnMouseWheel: true,
            },
          ],
        }),
      series: [
        {
          type: "bar",
          emphasis: {
            focus: "series",
          },
          data: chartData.map((item) => item.count),
          ...(chartData.length < 10
            ? {
                barWidth: 30,
              }
            : {}),
        },
      ],
    };

    console.log('Setting option:', option); // 添加调试信息
    chartInstance4.current.setOption(option, true);
  };

  const handleTabChange = (value) => {
    setActiveTab(value);
    setTimeout(() => {
      chartInstance1.current?.resize();
      chartInstance2.current?.resize();
      chartInstance3.current?.resize();
      chartInstance4.current?.resize();
    }, 0);
  };

  useEffect(() => {
    if (activeTab === "overview") {
      chartInit1(deviceData?.run || []);
    }

    if (activeTab === "temperature") {
      setTimeout(() => {
        chartInit2(deviceData?.tem || []);
      }, 0);
    }

    if (activeTab === "humidity") {
      setTimeout(() => {
        chartInit3(deviceData?.hum || []);
      }, 0);
    }

    if (activeTab === "alerts") {
      setTimeout(() => {
        console.log('Initializing chart4 with alarm data:', deviceData?.alarm); // 添加调试信息
        chartInit4(deviceData?.alarm || []);
      }, 0);
    }

    return () => {
      window.removeEventListener("resize", chartResize);
    };
  }, [
    activeTab,
    deviceData?.hum,
    deviceData?.tem,
    deviceData?.alarm,
    deviceData?.run,
  ]);

  useEffect(() => {
    (async () => {
      // 获取驱鼠器列表
      if (gropSearch) {
        const res = await fetchDiviceList();
        if (res && res.length > 0) {
          setDeviceRepellentData([
            { deviceNo: "全选", deviceId: "全选" },
            ...res,
          ]);
          setSelectedDevice(["全选"]);
          setSearchData((prev) => ({
            ...prev,
            deviceIds: [],
          }));
        } else {
          setSelectedDevice([]);
          setDeviceData({});
          setDeviceRepellentData([]);
        }
      }
    })();
  }, [gropSearch]);

  useEffect(() => {
    if (deviceRepellentData.length > 0 && selectedDevice.length > 0) {
      fetchData();
    }
  }, [searchData]);

  useEffect(() => {
    return () => {
      if (window.cancelToken) {
        window.cancelToken(); // 取消请求
      }
    };
  }, []);

  // Tab content renderers
  const renderOverview = () => (
    <div className="tab-content">
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="驱鼠器在线率" loading={false}>
            <Spin spinning={loading} size="large" tip="加载中...">
              <div
                style={{
                  width: "100%",
                  height: "calc(100vh - 700px)",
                  padding: 20,
                }}
                id="device-statistics-chart1"
              ></div>
            </Spin>

            {/* {deviceData?.onlineRateAtMinutes?.length ? (
              <div
                style={{ width: '100%', height: '500px', padding: 20 }}
                id='device-statistics-chart1'
              ></div>
            ) : (
              <div style={{ width: '100%', height: '500px', padding: 20 }}>
                暂无数据
              </div>
            )} */}
          </Card>
        </Col>
      </Row>
    </div>
  );

  const renderAlerts = () => (
    <div className="tab-content">
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="告警统计">
            <Spin spinning={loading} size="large" tip="加载中...">
              <div
                style={{
                  width: "100%",
                  height: "calc(100vh - 700px)",
                  padding: 20,
                }}
                id="device-statistics-chart4"
              ></div>
            </Spin>
          </Card>
        </Col>
      </Row>
    </div>
  );

  return (
    <div className="device-statistics">
      {/* Header */}
      <div className="page-header">
        <div className="header-left"></div>
        <div className="header-right">
          <Space>
            <Select
              value={timeRange}
              style={{ width: 120 }}
              options={[
                { value: "hour", label: "按小时" },
                { value: "day", label: "按天" },
                { value: "month", label: "按月" },
                { value: "year", label: "按年" },
              ]}
              onChange={handleTimeRangeChange}
            />
            <RangePicker
              value={dateRange}
              allowClear={false}
              onChange={handleDateRangeChange}
              disabledDate={(current) => {
                const today = dayjs().endOf("day");
                // const oneMonthAgo = dayjs().subtract(1, 'month').startOf('day')
                // return current && (current > today || current < oneMonthAgo)
                return current && current > today;
              }}
            />
            {/* <Button icon={<DownloadOutlined />} onClick={handleExport}>
              导出
            </Button> */}
          </Space>
        </div>
      </div>

      <div className="device-statistics-content">
        <div className="device-statistics-side">{gropNode()}</div>
        <div className="device-statistics-content-chart">
          {/* Filter Bar */}
          <Card className="filter-bar">
            <Row align="middle" gutter={2}>
              <Col flex="100px">
                <span style={{ fontSize: "14px" }}>驱鼠器编号：</span>
              </Col>
              <Col flex="auto">
                <Select
                  showSearch
                  allowClear
                  mode="multiple"
                  placeholder="请选择驱鼠器编号"
                  onPopupScroll={loadMore}
                  maxTagCount={5}
                  onSearch={debounce(handleSearch, 300)}
                  loading={deviceRepellentLoading}
                  style={{ width: "100%" }}
                  optionFilterProp="children"
                  filterOption={false}
                  value={selectedDevice}
                  onDeselect={(value) => {
                    if (window.cancelToken) {
                      window.cancelToken();
                    }
                    setSearchData((prev) => ({
                      ...prev,
                      deviceIds: selectedDevice.filter((item) => item != value),
                    }));
                  }}
                  onChange={(value) => {
                    // 如果选择了全选，清空其他选项
                    if (value.includes("全选")) {
                      setSelectedDevice(["全选"]);
                    } else {
                      setSelectedDevice(value);
                    }

                    if (value.length === 0) {
                      setDeviceData({});
                    }
                  }}
                  onBlur={() => {
                    setSearchData((prev) => ({
                      ...prev,
                      deviceIds: selectedDevice.filter(
                        (item) => item != "全选"
                      ),
                    }));
                    // if(selectedDevice.includes("全选")){
                    //   fetchData({
                    //     deviceIds: []
                    //   })
                    // }
                  }}
                  onDropdownVisibleChange={async (open) => {
                    if (open) {
                      const tempSearch = {
                        keyword: "",
                        size: 50,
                        page: 0,
                      };
                      const res = await fetchDiviceList(tempSearch);
                      setSearchDeviceData(tempSearch);
                      if (res && res.length > 0) {
                        setDeviceRepellentData([
                          { deviceNo: "全选", deviceId: "全选" },
                          ...res,
                        ]);
                      } else {
                        setDeviceRepellentData([]);
                      }
                    }
                  }}
                >
                  {deviceRepellentData?.map((option) => (
                    <Option
                      key={option.deviceId}
                      value={option.deviceId}
                      disabled={
                        (option.deviceId === "全选" &&
                          selectedDevice.length > 0 &&
                          !selectedDevice.includes("全选")) ||
                        (option.deviceId !== "全选" &&
                          selectedDevice.includes("全选"))
                      }
                    >
                      {option.deviceNo}
                    </Option>
                  ))}
                </Select>
              </Col>
            </Row>
          </Card>

          {/* Core Metrics */}
          <Row gutter={[16, 16]} className="core-metrics">
            {coreMetrics.map((metric, index) => (
              <Col span={4} key={index}>
                <Card>
                  <Statistic
                    title={metric.title}
                    value={metric.value}
                    suffix={metric.value === "无" ? "" : metric.suffix}
                    prefix={metric.icon}
                    precision={2}
                  />
                </Card>
              </Col>
            ))}
          </Row>

          {/* Main Content */}
          <Card className="device-statistics-tab">
            <Tabs
              activeKey={activeTab}
              onChange={handleTabChange}
              className="device-statistics-content-chart"
              items={[
                {
                  key: "overview",
                  // forceRender: true,
                  label: (
                    <span>
                      <DashboardOutlined />
                      运行概览
                    </span>
                  ),
                  children: renderOverview(),
                },
                {
                  key: "temperature",
                  // forceRender: true,
                  label: (
                    <span className="icon_tab_tem">
                      <span></span>
                      温度
                    </span>
                  ),
                  children: (
                    <div className="tab-content">
                      <Row gutter={[16, 16]}>
                        <Col span={24}>
                          <Card title="驱鼠器温度统计">
                            <Spin
                              spinning={loading}
                              size="large"
                              tip="加载中..."
                            >
                              <div
                                style={{
                                  width: "100%",
                                  height: "calc(100vh - 700px)",
                                  padding: 20,
                                }}
                                id="device-statistics-chart2"
                              ></div>
                            </Spin>
                          </Card>
                        </Col>
                      </Row>
                    </div>
                  ),
                },
                {
                  key: "humidity",
                  // forceRender: true,
                  label: (
                    <span className="icon_tab_hum">
                      <span />
                      湿度
                    </span>
                  ),
                  children: (
                    <div className="tab-content">
                      <Row gutter={[16, 16]}>
                        <Col span={24}>
                          <Card title="驱鼠器湿度统计">
                            <Spin
                              spinning={loading}
                              size="large"
                              tip="加载中..."
                            >
                              <div
                                style={{
                                  width: "100%",
                                  height: "calc(100vh - 700px)",
                                  padding: 20,
                                }}
                                id="device-statistics-chart3"
                              ></div>
                            </Spin>
                          </Card>
                        </Col>
                      </Row>
                    </div>
                  ),
                },
                {
                  key: "alerts",
                  // forceRender: true,
                  label: (
                    <span>
                      <AlertOutlined />
                      告警事件
                    </span>
                  ),
                  children: renderAlerts(),
                },
              ]}
            />
          </Card>
        </div>
      </div>
    </div>
  );
};

export default DeviceStatistics;
