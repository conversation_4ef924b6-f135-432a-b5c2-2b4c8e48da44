import fetch from './fetch';
import {API_PREFIX} from "./constant"
import { method } from 'lodash';

// // 获取策略配置类型列表
// export const getCustomModelTypeList = () => {
//   return fetch({
//     url: `${API_PREFIX["device-service"]}/custom-models/getAllCustomModelType`,
//     method: 'GET',
//   })
// }


// 获取所有的策略配置
export const getAllCustomConfig = () => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/custom-models/getAll`,
    method: 'GET',
  })
}


// 获取策略配置列表
export const getCustomModelList = (params) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/custom-models/searchByType`,
    method: 'GET',
    params
  })
}

// 添加自定义配置
export const addCustomModelConfig = (data) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/custom-models`,
    method: 'post',
    data
  })
}

// 添加自定义配置
export const updateCustomModelConfig = (data) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/custom-models`,
    method: 'put',
    data
  })
}
// 批量删除
export const delManyCustomModelByIds = (ids) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/custom-models/deleteByIds`,
    method: "post",
    data: ids
  })
}

//下发配置
export const regionDeviceSetting = (params) =>{
  return fetch({
     url: `${API_PREFIX["device-service"]}/custom-models/regionDeviceSetting`,
     method:'get',
     params:params
  })
}

//获取地理位置树
export const getTree = (tenantId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/locations/tree/${tenantId}`,
    method:'get'
  })
}
