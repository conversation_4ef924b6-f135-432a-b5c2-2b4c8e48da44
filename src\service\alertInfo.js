import { getLoginEntry } from '../utils/loginLogger';
import { message } from 'antd';
// API基础URL - 可以根据实际部署环境手动配置
import {API_PREFIX} from "./constant"

const handleApiError = (response) => {
  return response.json().then(data => {
    if (response.status === 401) {
      message.error(data?.message);
      setTimeout(() => {
        window.location.href = '/login'
      }, 500);
    } else if (response.status === 400 || response.status === 404 || response.status === 500) {
      message.error(data?.message);
    } else if (response.status === 403) {
      // Handle forbidden
    }
    return Promise.reject(data);
  });
};

export const alertInfoApi = {
  findAlarms: async (params) => {
    const token = getLoginEntry();
    const queryParams = new URLSearchParams(params).toString();
    return fetch(`${API_PREFIX["view-service"]}/alarms/find?${queryParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },
  
  getAlarmById: async (id) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["view-service"]}/alarms/get/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },
  
  updateAlarm: async (id, data) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["view-service"]}/alarms/update/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(data)
    }).then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },
  
  getUserDropdown: async () => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/dropdown`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },

  deleteAlarm: async (id) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["view-service"]}/alarms/delete/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  }
};

