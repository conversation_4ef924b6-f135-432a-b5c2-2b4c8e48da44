.device-monitor {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.monitor-header {
  background: #fff;
  border-radius: 8px;
}

.monitor-content {
  flex: 1;
  background: #fff;
  border-radius: 8px;
}

.monitor-footer {
  background: #fff;
  border-radius: 8px;
}

.monitor-tabs {
  min-height: 500px;
}

.monitor-tabs .ant-tabs-nav {
  margin-bottom: 16px;
}

.monitor-tabs .ant-tabs-tab {
  padding: 12px 24px;
}

.monitor-tabs .ant-tabs-tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
}

.monitor-tabs .ant-tabs-content {
  padding: 16px 0;
}

/* Loading 状态下的背景样式 */
.monitor-content .ant-spin-container {
  background-color: #fff;
  min-height: 400px;
}

.monitor-content .ant-spin-spinning {
  background-color: #fff !important;
}

.monitor-content .ant-spin-blur {
  background-color: #fff !important;
}

.monitor-content .ant-spin-blur::after {
  background-color: rgba(255, 255, 255, 0.8) !important;
}

/* Tabs 包装器样式 */
.tabs-wrapper {
  position: relative;
  height: 100%;
}

/* 内容区域 loading 遮罩 */
.content-loading-overlay {
  position: absolute;
  top: 48px; /* Tabs 头部高度，避免遮挡标签 */
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff; /* 纯白色背景，完全遮挡后面的内容 */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  pointer-events: none;
}

.content-loading-overlay .ant-spin {
  position: static;
}

.content-loading-overlay .ant-spin-text {
  color: #1890ff;
  margin-top: 8px;
}

/* Overview component styles */
.metric-card {
  height: 140px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  height: 100%;
}

.metric-icon {
  padding: 8px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.02);
}

.metric-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-title {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.metric-value {
  font-size: 28px;
  font-weight: 600;
  line-height: 1;
  margin: 8px 0;
}

.metric-unit {
  font-size: 14px;
  margin-left: 4px;
  color: rgba(0, 0, 0, 0.45);
}

.status-normal {
  color: #52c41a;
}

.distribution-card {
  margin-top: 24px;
}

.time-card {
  background: #fafafa;
  border: none;
}

.trend-card {
  margin-top: 24px;
  height: 55vh;
}

.health-content{
  width: 100%;
}

/* 驱鼠器列表样式 */
.repellent-list-card {
  background: #fff;
  border-radius: 8px;
}

.repellent-list {
  display: flex;
  flex-direction: column;
}

.repellent-item {
  transition: all 0.3s ease;
}

.repellent-item:hover {
  background-color: #f5f5f5 !important;
  border-color: #1890ff !important;
}

.repellent-item.selected {
  background-color: #e6f7ff !important;
  border-color: #1890ff !important;
}

/* 自定义日期选择器样式 */
.custom-date-picker {
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 480px;
}

.custom-date-picker .ant-picker {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  min-width: 380px;
}

.custom-date-picker .ant-select {
  border-radius: 6px;
  flex-shrink: 0;
}

.custom-date-picker .ant-picker-range {
  padding: 4px 11px;
  height: 32px;
  width: 100%;
}

.custom-date-picker .ant-picker-range .ant-picker-separator {
  color: #bfbfbf;
  font-weight: normal;
  padding: 0 8px;
}

.custom-date-picker .ant-picker-input {
  width: 170px;
}

.custom-date-picker .ant-picker-input > input {
  font-size: 14px;
  color: #262626;
  width: 100%;
}

.custom-date-picker .ant-picker-range-separator {
  padding: 0 8px;
  flex-shrink: 0;
}

.custom-date-picker .ant-picker-input > input::placeholder {
  color: #bfbfbf;
}

/* 禁用状态下的样式 */
.custom-date-picker .ant-picker-disabled {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
}

.custom-date-picker .ant-picker-disabled .ant-picker-input > input {
  color: #00000040;
  background-color: transparent;
}

/* 选择器下拉样式 */
.custom-date-picker .ant-select-selector {
  height: 32px;
  padding: 4px 11px;
  border-radius: 6px;
}

.custom-date-picker .ant-select-selection-item {
  line-height: 22px;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .custom-date-picker {
    min-width: 400px;
  }

  .custom-date-picker .ant-picker {
    min-width: 320px;
  }

  .custom-date-picker .ant-picker-input {
    width: 140px;
  }
}

@media (max-width: 768px) {
  .custom-date-picker {
    flex-direction: column;
    align-items: stretch;
    min-width: auto;
  }

  .custom-date-picker .ant-select {
    margin-bottom: 8px;
    margin-right: 0;
  }

  .custom-date-picker .ant-picker {
    min-width: auto;
    width: 100%;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .device-monitor {
    padding: 8px;
  }

  .monitor-tabs .ant-tabs-tab {
    padding: 8px 16px;
  }

  .metric-card {
    height: auto;
  }

  .metric-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 16px 0;
  }

  .metric-info {
    width: 100%;
    align-items: center;
  }
}