import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Table,
  Tag,
  Input,
  Select,
  DatePicker,
  Timeline,
  Drawer,
  Radio,
  Tabs,
  List,
  message
} from 'antd';
import {
  SearchOutlined,
  DownloadOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined,
  ApiOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { logManagementApi } from '../../service/logManagement';
import './styles.css';

const { RangePicker } = DatePicker;
const { Option } = Select;

// 操作类型映射
const operationTypeMap = {
  0: '查询',
  1: '新增',
  2: '修改',
  3: '删除',
  4: '授权', 
  5: '导出',
  6: '导入'
};

const LogManagement = () => {
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [logType, setLogType] = useState('operation');
  const [timeRange, setTimeRange] = useState('today');
  const [dateRange, setDateRange] = useState([dayjs().startOf('day'), dayjs().endOf('day')]);
  const [loading, setLoading] = useState(false);
  const [logData, setLogData] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [selectedLevel, setSelectedLevel] = useState('all');
  const [selectedService, setSelectedService] = useState('');
  const [selectedInstance, setSelectedInstance] = useState('');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedModule, setSelectedModule] = useState('');
  const [selectedOperationType, setSelectedOperationType] = useState('0');
  const [selectedUsername, setSelectedUsername] = useState('');

  // 操作类型选项
  const operationTypeOptions = [
    { label: '查询', value: '0' },
    { label: '新增', value: '1' },
    { label: '修改', value: '2' },
    { label: '删除', value: '3' },
    { label: '授权', value: '4' },
    { label: '导出', value: '5' },
    { label: '导入', value: '6' }
  ];

  // 处理详情按钮点击事件
  const handleDetailClick = (record) => {
    setLoading(true);
    
    if (logType === 'operation') {
      // 当选择操作日志时，调用操作日志详情接口
      logManagementApi.getOperationLogDetail(record.id)
        .then(response => {
          if (response.code === 200) {
            // 将详情数据格式化后设置
            const detailData = {
              ...record,
              ...response.data,
              // 确保特定字段被保留
              apiUrl: response.data.apiUrl,
              duration: response.data.duration,
              method: response.data.method,
              module: response.data.module,
              operationType: response.data.operationType,
              requestMethod: response.data.requestMethod,
              requestParams: response.data.requestParams,
              result: response.data.result,
              startTime: response.data.startTime,
              endTime: response.data.endTime,
              ipAddress: response.data.ipAddress,
              location: response.data.location,
              message: response.data.message,
              // 为操作日志设置一个默认级别
              level: 'INFO',
              // 重组context和system对象用于详情展示
              context: {
                apiUrl: response.data.apiUrl || '',
                method: response.data.method || '',
                module: response.data.module || '',
                operationType: response.data.operationType !== undefined ? operationTypeMap[response.data.operationType] || '' : '',
                requestMethod: response.data.requestMethod || '',
                requestParams: response.data.requestParams || '',
                result: response.data.result || '',
                duration: response.data.duration ? `${response.data.duration}ms` : ''
              },
              system: {
                ipAddress: response.data.ipAddress || '',
                location: response.data.location || '',
                startTime: response.data.startTime || '',
                endTime: response.data.endTime || ''
              }
            };
            setSelectedRecord(detailData);
            setDrawerVisible(true);
          }
        })
        .catch(error => {
          console.error('获取日志详情失败:', error);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      // 其他日志类型使用原有接口
      logManagementApi.getLogDetail(record.id)
        .then(response => {
          if (response.code === 200) {
            // 将详情数据格式化后设置
            const detailData = {
              ...record,
              ...response.data,
              level: response.data.level || record.level,  // 确保优先使用API返回的level
              // 重组context和system对象用于详情展示
              context: {
                requestId: response.data.requestId || '',
                sessionId: response.data.sessionId || '',
                environment: response.data.environment || '',
                className: response.data.className || '',
                methodName: response.data.methodName || '',
                lineNumber: response.data.lineNumber || '',
                executionTime: response.data.executionTime || 0
              },
              system: {
                hostName: response.data.hostName || '',
                ipAddress: response.data.ipAddress || '',
                osName: response.data.osName || '',
                browserInfo: response.data.browserInfo || ''
              }
            };
            setSelectedRecord(detailData);
            setDrawerVisible(true);
          }
        })
        .catch(error => {
          console.error('获取日志详情失败:', error);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  // Table columns
  const columns = logType === 'operation' ? [
    {
      title: '模块',
      dataIndex: 'module',
      key: 'module'
    },
    {
      title: '地理位置',
      dataIndex: 'location',
      key: 'location'
    },
    {
      title: '操作结果',
      dataIndex: 'result',
      key: 'result'
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          onClick={() => handleDetailClick(record)}
        >
          详情
        </Button>
      )
    }
  ] : logType === 'user' ? [
    {
      title: '用户名',
      dataIndex: 'userName',
      key: 'userName',
      align: 'center'
    },
    {
      title: '事件类型',
      dataIndex: 'eventType',
      key: 'eventType',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: 'IP地址',
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      align: 'center'
    },
    {
      title: '地理位置',
      dataIndex: 'location',
      key: 'location',
      align: 'center'
    },
    {
      title: '事件时间',
      dataIndex: 'eventTime',
      key: 'eventTime',
      align: 'center',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      align: 'center',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'
    }
  ] : [
    {
      title: '应用名称',
      dataIndex: 'application',
      key: 'application'
    },
    {
      title: '服务名称',
      dataIndex: 'service',
      key: 'service',
      width: 120,
      render: (text) => <div style={{ whiteSpace: 'nowrap' }}>{text}</div>
    },
    {
      title: '日志消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true
    },
    {
      title: '异常类型',
      dataIndex: 'exceptionType',
      key: 'exceptionType'
    },
    {
      title: '异常消息',
      dataIndex: 'exceptionMessage',
      key: 'exceptionMessage',
      ellipsis: true
    },
    {
      title: '异常原因',
      dataIndex: 'exceptionCause',
      key: 'exceptionCause',
      ellipsis: true
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          onClick={() => handleDetailClick(record)}
        >
          详情
        </Button>
      )
    }
  ];

  const handleTimeRangeChange = (value) => {
    setTimeRange(value);
    switch (value) {
      case 'today':
        setDateRange([dayjs().startOf('day'), dayjs().endOf('day')]);
        break;
      case 'yesterday':
        setDateRange([dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')]);
        break;
      case 'week':
        setDateRange([dayjs().startOf('week'), dayjs().endOf('week')]);
        break;
      case 'month':
        setDateRange([dayjs().startOf('month'), dayjs().endOf('month')]);
        break;
      default:
        break;
    }
  };

  // 获取日志数据
  const fetchLogData = async (pageNum = 1, pageSize = 10) => {
    setLoading(true);
    try {
      let response;
      
      if (logType === 'operation') {
        // 当选择操作日志时，调用操作日志接口
        response = await logManagementApi.getOperationLogList({
          pageNum,
          pageSize,
          module: selectedModule ? (selectedModule === 'operation' ? '操作日志' : '系统日志') : '',
          operationType: parseInt(selectedOperationType)
        });
      } else if (logType === 'user') {
        // 当选择用户日志时，调用用户日志接口
        response = await logManagementApi.getUserLogList({
          pageNum,
          pageSize
        });
      } else {
        // 系统日志使用 logManagementApi
        response = await logManagementApi.getLogList(pageNum, pageSize);
      }
      
      if (response.code === 200) {
        // 将接口返回的数据映射到表格所需的格式
        const formattedData = response.data.content?.map(item => {
          if (logType === 'operation') {
            // 操作日志数据映射
            return {
              id: item.id || '',
              module: item.module || '未知模块',
              location: item.location || '-',
              result: item.result || '-',
              startTime: item.startTime || '',
              endTime: item.endTime || '',
              // 保留其他字段用于详情展示
              level: item.level || 'INFO',
              context: {
                requestId: item.requestId || '',
                sessionId: item.sessionId || '',
                environment: item.environment || '',
                operationType: item.operationType !== undefined ? operationTypeMap[item.operationType] || '' : '',
                userId: item.userId || '',
                userName: item.userName || ''
              },
              system: {
                hostName: item.hostName || '',
                ipAddress: item.ipAddress || '',
                osName: item.osName || '',
                browserInfo: item.browserInfo || ''
              }
            };
          } else if(logType === 'user'){
            // 用户日志数据映射
            return {
              id: item.id || '',
              userName: item.userName || '-',
              eventType: item.eventType || '-',
              status: item.status || '-',
              ipAddress: item.ipAddress || '-',
              location: item.location || '-',
              eventTime: item.eventTime || '',
              createdAt: item.createdAt || ''
            }
          }
          else {
            // 系统日志和其他日志类型的数据映射
            return {
              id: item.id || '',
              application: item.application || '未知应用',
              service: item.service || '未知服务',
              message: item.message || '',
              exceptionType: item.exceptionType || '',
              exceptionMessage: item.exceptionMessage || '',
              exceptionCause: item.exceptionCause || '',
              createdAt: item.createdAt || '',
              level: item.level || 'INFO',
              // 保留context和system用于详情展示
              context: {
                requestId: item.requestId || '',
                sessionId: item.sessionId || '',
                environment: item.environment || '',
                operationType: item.operationType !== undefined ? operationTypeMap[item.operationType] || '' : '',
                userId: item.userId || '',
                userName: item.userName || ''
              },
              system: {
                hostName: item.hostName || '',
                ipAddress: item.ipAddress || '',
                osName: item.osName || '',
                browserInfo: item.browserInfo || ''
              }
            };
          }
        }) || [];
        
        setLogData(formattedData);
        setPagination({
          current: pageNum,
          pageSize,
          total: response.data.totalElements || 0
        });
      }
    } catch (error) {
      console.error('获取日志数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchLogData(1, 10);
  }, []);

  // 日志类型变化时重新获取数据
  useEffect(() => {
    fetchLogData(1, 10);
  }, [logType]);

  // 表格分页变化时重新获取数据
  const handleTableChange = (pagination) => {
    fetchLogData(pagination.current, pagination.pageSize);
  };

  // 处理搜索按钮点击事件
  const handleSearch = () => {
    if (logType === 'system') {
      setLoading(true);
      const levelParam = selectedLevel === 'all' ? '' : selectedLevel;
      const serviceParam = selectedService || '';
      const instanceParam = selectedInstance || '';
      
      logManagementApi.searchSystemLog({
        pageNum: 1,
        pageSize: pagination.pageSize,
        level: levelParam,
        application: searchKeyword || '',
        service: serviceParam,
        instance: instanceParam
      })
        .then(response => {
          if (response && (response.code === 200 || response.data)) {
            const formattedData = (response.data?.content || response.data || []).map(item => ({
              id: item.id || '',
              application: item.application || '未知应用',
              service: item.service || '未知服务',
              message: item.message || '',
              exceptionType: item.exceptionType || '',
              exceptionMessage: item.exceptionMessage || '',
              exceptionCause: item.exceptionCause || '',
              createdAt: item.createdAt || '',
              level: item.level || 'INFO',
              context: {
                requestId: item.requestId || '',
                sessionId: item.sessionId || '',
                environment: item.environment || '',
                operationType: item.operationType !== undefined ? operationTypeMap[item.operationType] || '' : '',
                userId: item.userId || '',
                userName: item.userName || ''
              },
              system: {
                hostName: item.hostName || '',
                ipAddress: item.ipAddress || '',
                osName: item.osName || '',
                browserInfo: item.browserInfo || ''
              }
            }));
            
            setLogData(formattedData);
            setPagination({
              current: 1,
              pageSize: pagination.pageSize,
              total: response.data.totalElements || 0
            });
          }
        })
        .catch(error => {
          console.error('获取日志数据失败:', error);
        })
        .finally(() => {
          setLoading(false);
        });
    } else if (logType === 'operation') {
      setLoading(true);
      logManagementApi.searchOperationLog({
        pageNum: 1,
        pageSize: pagination.pageSize,
        module: selectedModule ? (selectedModule === 'operation' ? '操作日志' : '系统日志') : '',
        operationType: parseInt(selectedOperationType)
      })
      .then(response => {
        if (response && (response.code === 200 || response.data)) {
          const formattedData = (response.data?.content || response.data || []).map(item => ({
            id: item.id || '',
            module: item.module || '未知模块',
            location: item.location || '-',
            result: item.result || '-',
            startTime: item.startTime || '',
            endTime: item.endTime || '',
            level: item.level || 'INFO',
            context: {
              requestId: item.requestId || '',
              sessionId: item.sessionId || '',
              environment: item.environment || '',
              operationType: item.operationType !== undefined ? operationTypeMap[item.operationType] || '' : '',
              userId: item.userId || '',
              userName: item.userName || ''
            },
            system: {
              hostName: item.hostName || '',
              ipAddress: item.ipAddress || '',
              osName: item.osName || '',
              browserInfo: item.browserInfo || ''
            }
          }));
          
          setLogData(formattedData);
          setPagination({
            current: 1,
            pageSize: pagination.pageSize,
            total: response.data.totalElements || 0
          });
        }
      })
      .catch(error => {
        console.error('获取日志数据失败:', error);
      })
      .finally(() => {
        setLoading(false);
      });
    }
  };

  // 处理重置按钮点击事件
  const handleReset = () => {
    if (logType === 'operation') {
      setSelectedModule('');
      setSelectedOperationType('0');
      setLoading(true);
      
      logManagementApi.searchOperationLog({
        pageNum: 1,
        pageSize: pagination.pageSize,
        module: '',
        operationType: 0
      })
      .then(response => {
        if (response && (response.code === 200 || response.data)) {
          const formattedData = (response.data?.content || response.data || []).map(item => ({
            id: item.id || '',
            module: item.module || '未知模块',
            location: item.location || '-',
            result: item.result || '-',
            startTime: item.startTime || '',
            endTime: item.endTime || '',
            level: item.level || 'INFO',
            context: {
              requestId: item.requestId || '',
              sessionId: item.sessionId || '',
              environment: item.environment || '',
              operationType: item.operationType !== undefined ? operationTypeMap[item.operationType] || '' : '',
              userId: item.userId || '',
              userName: item.userName || ''
            },
            system: {
              hostName: item.hostName || '',
              ipAddress: item.ipAddress || '',
              osName: item.osName || '',
              browserInfo: item.browserInfo || ''
            }
          }));
          
          setLogData(formattedData);
          setPagination({
            current: 1,
            pageSize: pagination.pageSize,
            total: response.data.totalElements || 0
          });
        }
      })
      .catch(error => {
        console.error('获取日志数据失败:', error);
      })
      .finally(() => {
        setLoading(false);
      });
    } else {
      setSelectedLevel('all');
      setSelectedService('');
      setSelectedInstance('');
      setSearchKeyword('');
      
      // 使用重置后的值进行搜索
      if (logType === 'system') {
        setLoading(true);
        logManagementApi.searchSystemLog({
          pageNum: 1,
          pageSize: pagination.pageSize,
          level: '',
          application: '',
          service: '',
          instance: ''
        })
          .then(response => {
            if (response && (response.code === 200 || response.data)) {
              const formattedData = (response.data?.content || response.data || []).map(item => ({
                id: item.id || '',
                application: item.application || '未知应用',
                service: item.service || '未知服务',
                message: item.message || '',
                exceptionType: item.exceptionType || '',
                exceptionMessage: item.exceptionMessage || '',
                exceptionCause: item.exceptionCause || '',
                createdAt: item.createdAt || '',
                level: item.level || 'INFO',
                context: {
                  requestId: item.requestId || '',
                  sessionId: item.sessionId || '',
                  environment: item.environment || '',
                  operationType: item.operationType !== undefined ? operationTypeMap[item.operationType] || '' : '',
                  userId: item.userId || '',
                  userName: item.userName || ''
                },
                system: {
                  hostName: item.hostName || '',
                  ipAddress: item.ipAddress || '',
                  osName: item.osName || '',
                  browserInfo: item.browserInfo || ''
                }
              }));
              
              setLogData(formattedData);
              setPagination({
                current: 1,
                pageSize: pagination.pageSize,
                total: response.data.totalElements || 0
              });
            }
          })
          .catch(error => {
            console.error('获取日志数据失败:', error);
          })
          .finally(() => {
            setLoading(false);
          });
      }
    }
  };

  return (
    <div className="log-management">

      {/* Log Type Switch */}
      <Card className="log-type-switch">
        <Radio.Group value={logType} onChange={(e) => setLogType(e.target.value)}>
          {/* <Radio.Button value="system">系统日志</Radio.Button> */}
          <Radio.Button value="operation">操作日志</Radio.Button>
          <Radio.Button value="user">用户日志</Radio.Button>
        </Radio.Group>
      </Card>

      {/* Time Range Selector */}
      {/* <Card className="time-selector">
        <Space>
          <Select
            value={timeRange}
            onChange={handleTimeRangeChange}
            style={{ width: 120 }}
          >
            <Option value="today">今天</Option>
            <Option value="yesterday">昨天</Option>
            <Option value="week">本周</Option>
            <Option value="month">本月</Option>
            <Option value="custom">自定义</Option>
          </Select>
          <RangePicker
            showTime
            value={dateRange}
            onChange={(dates) => setDateRange(dates)}
            style={{ width: 380 }}
          />
          <Button type="primary">应用</Button>
        </Space>
      </Card> */}

      {/* Advanced Filter */}
      {logType !== 'user' && (
        <Card className="advanced-filter">
          <Space wrap style={{ width: '100%' }}>
            {logType === 'operation' ? (
              <>
                {/* <span>模块:</span>
                <Select
                  value={selectedModule}
                  onChange={setSelectedModule}
                  style={{ width: 120 }}
                  options={moduleOptions}
                /> */}
                <span>操作类型:</span>
                <Select
                  value={selectedOperationType}
                  onChange={setSelectedOperationType}
                  style={{ width: 120 }}
                  options={operationTypeOptions}
                />
              </>
            ) : (
              <>
                <span>级别:</span>
                <Radio.Group 
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value)}
                >
                  <Radio.Button value="">全部</Radio.Button>
                  <Radio.Button value="debug">DEBUG</Radio.Button>
                  <Radio.Button value="info">INFO</Radio.Button>
                  <Radio.Button value="warn">WARN</Radio.Button>
                  <Radio.Button value="error">ERROR</Radio.Button>
                  <Radio.Button value="fatal">FATAL</Radio.Button>
                </Radio.Group>
                <Input 
                  placeholder="输入服务名称"
                  value={selectedService}
                  onChange={(e) => setSelectedService(e.target.value)}
                  style={{ width: 120 }}
                  allowClear
                />
                <Input 
                  placeholder="输入实例名称"
                  value={selectedInstance}
                  onChange={(e) => setSelectedInstance(e.target.value)}
                  style={{ width: 120 }}
                  allowClear
                />
                <Input
                  placeholder="搜索应用名称..."
                  style={{ width: 200 }}
                  allowClear
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                />
              </>
            )}
            <Button 
              type='primary'
              icon={<SearchOutlined />}
              onClick={handleSearch}
            >
              搜索
            </Button>
            <Button 
              onClick={handleReset}
            >
              重置
            </Button>
            {logType !== 'user' && (
              <Button 
                icon={<DownloadOutlined />}
                onClick={() => {
                 if (logType === 'operation') {
                    // 当选择操作日志时，调用操作日志导出接口
                    logManagementApi.exportOperationLogData({
                      pageNum: pagination.current,
                      pageSize: pagination.pageSize,
                      module: selectedModule ? (selectedModule === 'operation' ? '操作日志' : '系统日志') : '',
                      operationType: parseInt(selectedOperationType)
                    }).then(response => {
                      // 处理文件下载
                      const url = window.URL.createObjectURL(new Blob([response]));
                      const link = document.createElement('a');
                      link.href = url;
                      link.setAttribute('download', `操作日志导出_${dayjs().format('YYYY-MM-DD')}.xlsx`);
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }).catch(error => {
                      console.error('导出日志失败:', error);
                    });
                  }
                }}
              >导出</Button>
            )}
          </Space>
        </Card>
      )}

      {/* Log Visualization */}
      {/* <Card className="log-visualization">
        <div className="visualization-header">
          <div className="trend-chart">
            ▁▂▃▂▄▅▆▅▂▃▁▁▂▅▇█▆▅▄▃▂▂▃▃▄▂▁▁▂▃▃▄▅▃▂▁▁▂▂▂▃▂▁▂▃▄▅▆▄▃▁▂▃▁
          </div>
          <Button>放大</Button>
        </div>
        <div className="level-distribution">
          <div className="level-row error">
            ERROR █ █ █ █
          </div>
          <div className="level-row warn">
            WARN  █ █ █ █ █ █ █ █ █ █ █
          </div>
          <div className="level-row info">
            INFO  █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █ █
          </div>
          <div className="level-row debug">
            DEBUG █ █ █ █ █ █
          </div>
        </div>
      </Card> */}

      {/* Log Table */}
      <Card className="log-table" style={{ minHeight: "500px" }}>
        <div>
          <Table
            columns={columns}
            dataSource={logData}
            loading={loading}
            pagination={pagination}
            onChange={handleTableChange}
            scroll={{ y: logData.length > 10 ? 650 : undefined }}
            rowKey="id"
          />
        </div>
      </Card>

      {/* Log Detail Drawer */}
      <Drawer
        title="日志详情"
        placement="right"
        width={600}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        {selectedRecord && (
          <Tabs
            items={[
              {
                key: 'basic',
                label: '基本信息',
                children: (
                  <Timeline
                    items={[
                      logType === 'operation' ? {
                        color: 'blue',
                        dot: <ClockCircleOutlined />,
                        children: (
                          <>
                            <div className="detail-label">开始时间</div>
                            <div>{selectedRecord.startTime ? dayjs(selectedRecord.startTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>
                          </>
                        )
                      } : {
                        color: 'blue',
                        dot: <ClockCircleOutlined />,
                        children: (
                          <>
                            <div className="detail-label">创建时间</div>
                            <div>{selectedRecord.createdAt ? dayjs(selectedRecord.createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>
                          </>
                        )
                      },
                      logType === 'operation' ? {
                        color: 'cyan',
                        dot: <ClockCircleOutlined />,
                        children: (
                          <>
                            <div className="detail-label">结束时间</div>
                            <div>{selectedRecord.endTime ? dayjs(selectedRecord.endTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>
                          </>
                        )
                      } : null,
                      logType === 'operation' ? {
                        color: 'green',
                        dot: <GlobalOutlined />,
                        children: (
                          <>
                            <div className="detail-label">模块</div>
                            <div>{selectedRecord.module || '-'}</div>
                          </>
                        )
                      } : {
                        color: 'green',
                        dot: <GlobalOutlined />,
                        children: (
                          <>
                            <div className="detail-label">应用名称</div>
                            <div>{selectedRecord.application || '-'}</div>
                          </>
                        )
                      },
                      logType === 'operation' ? {
                        color: 'purple',
                        dot: <ApiOutlined />,
                        children: (
                          <>
                            <div className="detail-label">API地址</div>
                            <div>{selectedRecord.apiUrl || '-'}</div>
                          </>
                        )
                      } : {
                        color: 'cyan',
                        dot: <ApiOutlined />,
                        children: (
                          <>
                            <div className="detail-label">服务名称</div>
                            <div>{selectedRecord.service || '-'}</div>
                          </>
                        )
                      },
                      logType === 'operation' ? {
                        color: 'blue',
                        dot: <ApiOutlined />,
                        children: (
                          <>
                            <div className="detail-label">方法</div>
                            <div>{selectedRecord.method || '-'}</div>
                          </>
                        )
                      } : null,
                      logType === 'operation' ? {
                        color: 'orange',
                        dot: <InfoCircleOutlined />,
                        children: (
                          <>
                            <div className="detail-label">执行时间</div>
                            <div>{selectedRecord.duration ? `${selectedRecord.duration}ms` : '-'}</div>
                          </>
                        )
                      } : null,
                      logType === 'operation' ? {
                        color: 'blue',
                        dot: <InfoCircleOutlined />,
                        children: (
                          <>
                            <div className="detail-label">操作结果</div>
                            <div>{selectedRecord.result || '-'}</div>
                          </>
                        )
                      } : null,
                      logType === 'operation' ? {
                        color: 'green',
                        dot: <InfoCircleOutlined />,
                        children: (
                          <>
                            <div className="detail-label">操作类型</div>
                            <div>{selectedRecord.operationType !== undefined ? (operationTypeMap[selectedRecord.operationType] || selectedRecord.operationType) : '-'}</div>
                          </>
                        )
                      } : null,
                      {
                        color: selectedRecord.level === 'ERROR' || selectedRecord.level === 'error' ? 'red' : 'orange',
                        dot: <WarningOutlined />,
                        children: (
                          <>
                            <div className="detail-label">级别</div>
                            <Tag color={
                              selectedRecord.level === 'ERROR' || selectedRecord.level === 'error' ? 'red' :
                              selectedRecord.level === 'WARN' || selectedRecord.level === 'warn' ? 'orange' : 'blue'
                            }>
                              {selectedRecord.level}
                            </Tag>
                          </>
                        )
                      },
                      logType !== 'operation' ? {
                        color: 'purple',
                        dot: <InfoCircleOutlined />,
                        children: (
                          <>
                            <div className="detail-label">日志消息</div>
                            <div>{selectedRecord.message || '-'}</div>
                          </>
                        )
                      } : null
                    ].filter(Boolean)}
                  />
                )
              },
              logType !== 'operation' ? {
                key: 'exception',
                label: '异常信息',
                children: (
                  <Card size="small">
                    <div className="exception-info">
                      <div className="detail-label">异常类型</div>
                      <div>{selectedRecord.exceptionType || '-'}</div>
                      <div className="detail-label">异常消息</div>
                      <div>{selectedRecord.exceptionMessage || '-'}</div>
                      <div className="detail-label">异常原因</div>
                      <div>{selectedRecord.exceptionCause || '-'}</div>
                    </div>
                  </Card>
                )
              } : null,
              {
                key: 'context',
                label: '上下文',
                children: selectedRecord.context && (
                  <List
                    size="small"
                    dataSource={Object.entries(selectedRecord.context)}
                    renderItem={([key, value]) => (
                      <List.Item>
                        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                          <span className="detail-label">{key}</span>
                          <span>{value || '-'}</span>
                        </Space>
                      </List.Item>
                    )}
                  />
                )
              },
              {
                key: 'system',
                label: '系统信息',
                children: selectedRecord.system && (
                  <List
                    size="small"
                    dataSource={Object.entries(selectedRecord.system)}
                    renderItem={([key, value]) => (
                      <List.Item>
                        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                          <span className="detail-label">{key}</span>
                          <span>{value || '-'}</span>
                        </Space>
                      </List.Item>
                    )}
                  />
                )
              }
            ].filter(Boolean)}
          />
        )}
      </Drawer>
    </div>
  );
};

export default LogManagement;