.security-audit {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  gap: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left h2 {
  margin: 8px 0 0;
  font-size: 24px;
  font-weight: 500;
}

.time-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statistics-cards .ant-card {
  border-radius: 8px;
}

.trend-value {
  font-size: 14px;
  margin-left: 8px;
}

.advanced-filter {
  background: #fff;
  border-radius: 4px;
}

.filter-section {
  margin-bottom: 16px;
}

.filter-label {
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 8px;
}

.view-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.audit-table {
  flex: 1;
  min-height: 0;
}

.audit-table .ant-table-row {
  cursor: pointer;
}

.audit-table .ant-table-row:hover {
  background-color: #f5f5f5;
}

.detail-label {
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 8px;
}

/* Drawer styles */
.ant-drawer-body {
  padding: 24px;
}

.ant-drawer-body .ant-timeline {
  margin-top: 24px;
}

.ant-drawer-body .ant-timeline-item {
  padding-bottom: 24px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .security-audit {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .time-selector {
    flex-direction: column;
    gap: 16px;
  }

  .time-selector .ant-space {
    width: 100%;
  }

  .time-selector .ant-picker {
    width: 100% !important;
  }

  .statistics-cards .ant-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .view-switch {
    flex-direction: column;
    gap: 16px;
  }

  .ant-drawer {
    width: 100% !important;
  }
}