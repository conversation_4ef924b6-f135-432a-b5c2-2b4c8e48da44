import { useEffect, useRef } from 'react'
import { Card, Row, Col, Progress } from 'antd'
import echarts from './../../../utils/echart'

import {
  HeartTwoTone,
  ClockCircleTwoTone,
  ThunderboltTwoTone,
  CheckCircleTwoTone,
  CloseCircleTwoTone,
  PauseCircleTwoTone
} from '@ant-design/icons'

const Overview = ({ data, isActive = false }) => {
  const chartInstance = useRef(null)
  useEffect(() => {
    if (isActive && chartInstance.current) {
      setTimeout(() => {
        chartInstance.current.resize()
      }, 0)
    }
  }, [isActive])

  useEffect(() => {
    chartInit(data.chart1)

    // Add resize handler
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize()
      }
    }
    setTimeout(() =>{
      handleResize()
    },0)

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [data?.chart1])

  // chart初始化
  const chartInit = chartData => {
    let chartDom = document.getElementById('chart1')
    if (!chartDom) {
      return
    }

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartDom)
    }

    const option = {
      grid: {
        left: '3%',
        right: '4%',
        bottom: '13%',
        top: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            show: false,
            backgroundColor: '#6a7985',
            fontSize: '12px'
          }
        },
        formatter: function (params) {
          let name = `${params[0].name}<br>`
          let dataIndex = params[0].data.value
          let { marker } = params[0]
          params.forEach(item => {
            // 根据数据点位置显示对应的状态文本
            if (dataIndex == 0.5) {
              name = `${name}<br>${marker} 离线`  // 位置0.5对应离线
            } else if (dataIndex == 1) {
              name = `${name}<br>${marker} 在线`  // 位置1对应在线
            } else if (dataIndex == 2) {
              name = `${name}<br>${marker} 驱鼠中`
            }
          })
          return name
        }
      },
      legend: {
        show: false
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLabel: {
          color: '#000000', // 设置 X 轴坐标值颜色
          fontSize: '12px' // 可选：调整字体大小
        },
        data: chartData?.x?.length ? chartData.x : []
      },
      yAxis: [
        {
          type: 'value',
          name: '驱鼠器状态',
          min: 0,
          max: 2,
          interval: 0.5, // 设置间隔为0.5，这样会在0, 0.5, 1, 1.5, 2位置显示刻度
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: '12px',
            padding: [0, 0, 0, 40] // 向左侧偏移名称
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          },
          axisLine: {
            show: true, // 显示y轴轴线
            lineStyle: {
              color: '#B0B0B0',
              width: 1,
            },
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            fontSize: '12px',
            formatter: function (value) {
              // 自定义显示的标签，让离线标签显示在0.5位置
              if (value == 0.5) {
                return '离线'  // 在0.5位置显示离线
              } else if (value == 1) {
                return '在线'
              } else if (value == 2) {
                return '驱鼠中'
              }
              return '' // 其他位置不显示标签
            }
          }
        },
        {
          position: 'right',
          min: 0,
          max: 2,
          interval: 0.5, // 设置间隔为0.5，这样会在0, 0.5, 1, 1.5, 2位置显示刻度
          type: 'value',
          name: '驱鼠器状态',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: '12px',
            padding: [0, 0, 0, -40] // 向右侧偏移名称
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#B0B0B0',
              width: 1,
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            fontSize: '12px',
            formatter: function (value) {
              // 自定义显示的标签，让离线标签显示在0.5位置
              if (value == 0.5) {
                return '离线'  // 在0.5位置显示离线
              } else if (value == 1) {
                return '在线'
              } else if (value == 2) {
                return '驱鼠中'
              }
              return '' // 其他位置不显示标签
            }
          },
          splitLine: {
            show: false
          }
        }
      ],
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none',
            title: {
              zoom: '区域缩放',
              back: '区域缩放还原'
            }
          },
          restore: {
            title: '还原'
          }
        },
        right: 20,
        top: -5
      },
      dataZoom: [
        {
          type: 'slider',
          xAxisIndex: 0,
          start: 0,
          end: 100
        },
        {
          type: 'inside',
          xAxisIndex: [0],
          zoomLock: false,
          moveOnMouseMove: true,
          moveOnMouseWheel: true
        }
      ],
      series: [
        {
          // name: '离线',
          type: 'line',
          smooth: true,
          stack: 'Total',
          symbol: 'circle', // 使用实心圆点
          symbolSize: 6, // 设置点的大小
          lineStyle: {
            color: '#003366' // 折线颜色改为深蓝色
          },
          emphasis: {
            focus: 'series'
          },
          data: chartData.y?.length
            ? chartData.y.map(value => {
                // 设置显示位置：离线在0.5位置（与标签对齐），在线在1位置，驱鼠中在2位置
                let displayValue = value;
                if (value == 0) {
                  displayValue = 0.5; // 离线显示在0.5位置，与"离线"标签对齐
                } else if (value == 1) {
                  displayValue = 1; // 在线显示在1位置
                } else if (value == 2) {
                  displayValue = 2; // 驱鼠中显示在2位置
                }

                return {
                  value: displayValue,
                  itemStyle: {
                    color:
                      value == 0
                        ? '#ff0000'  // 离线保持红色
                        : value == 1
                        ? '#399073'  // 在线保持绿色
                        : value == 2
                        ? '#00C7F2'  // 驱鼠中保持蓝色
                        : ''
                  }
                }
              })
            : []
        }
      ]
    }
    option && chartInstance.current.setOption(option, true)
  }

  return (
    <div className='overview-content'>
      {/* Core Metrics */}

      
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card className='metric-card'>
            <div className='metric-content'>
              <div className='metric-icon'>
                <HeartTwoTone
                  twoToneColor='#eb2f96'
                  style={{ fontSize: '32px' }}
                />
              </div>
              <div className='metric-info'>
                <div className='metric-title'>设备健康评分</div>
                <div className='metric-value'>
                  {data?.deviceWorkState ?? '无'}
                </div>
                {data?.deviceWorkState && (
                  <Progress
                    percent={data?.deviceWorkState}
                    showInfo={false}
                    strokeColor={
                      data?.deviceWorkState >= 90
                        ? '#52c41a' // green for >= 90%
                        : data?.deviceWorkState >= 60
                        ? '#faad14' // orange for 60-90%
                        : '#ff4d4f'
                    }
                  />
                )}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className='metric-card'>
            <div className='metric-content'>
              <div className='metric-icon'>
                <img
                  src='/temperature1.png'
                  style={{ width: '30px',height: "28px" }}
                />
              </div>
              <div className='metric-info'>
                <div className='metric-title'>设备温度</div>
                <div className='metric-value'>
                  {data?.deviceTemperature ?? '无'}
                  <span className='metric-unit'>
                    {data?.deviceTemperature ? '℃' : ''}
                  </span>
                </div>
                {data?.deviceTemperature ? (
                  <Progress
                    percent={data?.deviceTemperature}
                    showInfo={false}
                    strokeColor='#52c41a'
                  />
                ) : (
                  ''
                )}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className='metric-card'>
            <div className='metric-content'>
              <div className='metric-icon'>
              <img
                  src='/humidity1.png'
                  style={{ width: '30px',height: "28px" }}
                />
              </div>
              <div className='metric-info'>
                <div className='metric-title'>设备湿度</div>
                <div className='metric-value'>
                  {data?.deviceHumidity ?? '无'}
                  <span className='metric-unit'>
                    {data?.deviceHumidity ? '%RH' : ''}
                  </span>
                </div>
                {data?.deviceHumidity ? (
                  <Progress
                    percent={data?.deviceHumidity}
                    showInfo={false}
                    strokeColor='#faad14'
                  />
                ) : (
                  ''
                )}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className='metric-card'>
            <div className='metric-content'>
              <div className='metric-icon'>
                {(() => {
                  const statusMap = {
                    0: (
                      <CloseCircleTwoTone
                        twoToneColor='#ff4d4f'
                        style={{ fontSize: '32px' }}
                      />
                    ),
                    1: (
                      <CheckCircleTwoTone
                        twoToneColor='#52c41a'
                        style={{ fontSize: '32px' }}
                      />
                    ),
                    2: (
                      <PauseCircleTwoTone
                        twoToneColor='#faad14'
                        style={{ fontSize: '32px' }}
                      />
                    )
                  }
                  return (
                    statusMap[data?.deviceStatus] || (
                      <CheckCircleTwoTone
                        twoToneColor='#d9d9d9'
                        style={{ fontSize: '32px' }}
                      />
                    )
                  )
                })()}
              </div>
              <div className='metric-info'>
                <div className='metric-title'>设备状态</div>
                <div className='metric-value status-normal'>
                  {(() => {
                    const statusMap = {
                      0: { text: '离线', color: '#ff4d4f' },
                      1: { text: '在线', color: '#52c41a' },
                      2: { text: '禁用', color: '#faad14' }
                    }
                    const statusInfo = statusMap[data?.deviceStatus] || {
                      text: '未知',
                      color: '#d9d9d9'
                    }
                    return (
                      <span style={{ color: statusInfo.color }}>
                        {statusInfo.text}
                      </span>
                    )
                  })()}
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Trend Chart */}
      <Card
        title='驱鼠器状态'
        className='trend-card'
        style={{ marginBottom: 24 }}
      >
        <div
            style={{ width: '100%', height: '47vh', padding: 20 }}
            id='chart1'
          ></div>
      </Card>
    </div>
  )
}

export default Overview
