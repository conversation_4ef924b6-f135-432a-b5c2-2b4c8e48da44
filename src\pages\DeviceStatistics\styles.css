.device-statistics {
  padding: 24px;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  /* overflow: auto; */
  height: calc(100% - 64px);
}

.page-header {
  margin-bottom: 24px;
}

.header-left {
  margin-bottom: 16px;
}

.header-left h2 {
  margin: 8px 0 0;
  font-size: 24px;
  font-weight: 500;
}

.filter-bar {
  margin-bottom: 24px;
}

.core-metrics {
  margin-bottom: 24px;
}

.core-metrics .ant-card {
  border-radius: 8px;
}

.trend-info {
  margin-top: 8px;
  font-size: 14px;
}

.device-statistics-content{
  width: 100%;
  display: flex;
  /* height: calc(100% - 56px); */
  flex: 1;
  height: 100%;
  /* overflow: visible; */
}

.device-statistics-tab{
  height: calc(100% - 240px);
}

.device-statistics-side{
  width: 300px;
  margin-right: 24px;
  background-color: #fff;
  border-radius: 8px;
  padding: 5px;
}

.filter-bar .ant-card-body{
  border-radius: 8px;
}

.device-statistics-content-chart{
  width: 100%;
  /* overflow-y: auto; */
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-label {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.tab-content {
  padding: 16px 0;
  height: 100%;
  overflow: visible;
}

.ant-tabs-nav {
  margin-bottom: 16px !important;
}

.ant-tabs-tab {
  padding: 12px 16px !important;
}

.ant-tabs-tab-btn {
  display: flex !important;
  align-items: center;
  gap: 8px;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .core-metrics .ant-col {
    flex: 0 0 33.33%;
    max-width: 33.33%;
  }
}

@media (max-width: 768px) {
  .device-statistics {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    overflow-x: auto;
  }

  .core-metrics .ant-col {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .tab-content .ant-row {
    margin: 0 !important;
  }

  .tab-content .ant-col {
    padding: 8px !important;
  }
}