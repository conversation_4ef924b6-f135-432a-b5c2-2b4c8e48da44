import React from 'react';
import { Card, Table, Switch } from 'antd';

const AccessControl = ({ group }) => {
  const columns = [
    { title: '权限项', dataIndex: 'permission', key: 'permission' },
    { title: '继承自上级', dataIndex: 'inherited', key: 'inherited', render: (inherited) => <Switch checked={inherited} /> },
    { title: '当前组设置', dataIndex: 'current', key: 'current', render: (current) => <Switch checked={current} /> },
  ];

  const data = [
    { key: '1', permission: '查看设备', inherited: true, current: true },
    { key: '2', permission: '配置设备', inherited: true, current: false },
    { key: '3', permission: '管理分组', inherited: false, current: true },
  ];

  return (
    <Card title="访问控制">
      <Table columns={columns} dataSource={data} pagination={false} />
    </Card>
  );
};

export default AccessControl;