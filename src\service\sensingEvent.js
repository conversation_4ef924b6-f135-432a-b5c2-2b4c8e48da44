import fetch from "./fetch";

import {API_PREFIX} from "./constant"

// 分页查询老鼠事件记录
export const getSensingEventList = (params) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/rat-events`,
    method: "GET",
    params,
  });
};

// 删除老鼠事件记录
export const deleteSensingEvent = (id) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/rat-events/${id}`,
    method: "DELETE"
  });
};

// 查看老鼠事件记录
export const getSensingEvent = (id) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/rat-events/${id}`,
    method: "GET"
  });
};

//查询摄像头下面的驱鼠器
export const getDevicesByCameraId = (cameraId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/cameras/getDevicesByCameraId`,
    method: "GET",
    params: {
      cameraId
    }
  })
}