import React from 'react';
import { Drawer, Space, Button, Tabs, Alert, List, Tag, Avatar } from 'antd';
import {
  SettingOutlined,
  CheckOutlined,
  DeleteOutlined,
  BellOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons';

const NotificationDrawer = ({ open, onClose }) => {
  // Mock notifications data
  const notifications = [
    {
      id: 1,
      type: 'error',
      title: '设备告警 · 紧急 · 2分钟前',
      content: '驱鼠器设备 DM-2023-B567 出现异常断电',
      location: '仓储区3号仓库 - 西南角',
      actions: ['查看详情', '忽略']
    },
    {
      id: 2,
      type: 'warning',
      title: '系统通知 · 重要 · 今天 10:23',
      content: '系统将在今晚23:00-23:30进行例行维护，期间部分功能可能暂时不可用',
      actions: ['查看详情', '忽略']
    },
    {
      id: 3,
      type: 'warning',
      title: '设备告警 · 重要 · 今天 09:45',
      content: '3台驱鼠器电池电量低于20%，需要更换',
      location: '农田区域A - 东区',
      actions: ['查看详情', '标记完成', '忽略']
    },
    {
      id: 4,
      type: 'info',
      title: '任务提醒 · 普通 · 昨天',
      content: '您负责的驱鼠器季度检查任务已创建，请在10天内完成',
      dueDate: '2023-10-30',
      actions: ['查看任务', '标记完成', '忽略']
    },
    {
      id: 5,
      type: 'info',
      title: '系统通知 · 普通 · 昨天',
      content: '您的账户权限已更新，新增了"设备配置管理"权限',
      actions: ['查看权限详情', '忽略']
    },
    {
      id: 6,
      type: 'read',
      title: '系统通知 · 已读 · 2023-10-18',
      content: '新版本V3.2.1已发布，包含多项功能改进和问题修复',
      actions: ['查看更新详情']
    }
  ];

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'error':
        return <WarningOutlined style={{ color: '#ff4d4f' }} />;
      case 'warning':
        return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'info':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      default:
        return <BellOutlined style={{ color: '#8c8c8c' }} />;
    }
  };

  const getNotificationColor = (type) => {
    switch (type) {
      case 'error':
        return '#fff1f0';
      case 'warning':
        return '#fffbe6';
      case 'info':
        return '#e6f7ff';
      default:
        return '#f5f5f5';
    }
  };

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>消息通知</span>
          <Space>
            <Button type="text" icon={<SettingOutlined />}>设置</Button>
            <Button type="text" icon={<CheckOutlined />}>全部标为已读</Button>
            <Button type="text" icon={<DeleteOutlined />}>清空已读通知</Button>
          </Space>
        </div>
      }
      placement="right"
      onClose={onClose}
      open={open}
      width={400}
    >
      <Tabs
        items={[
          {
            key: 'all',
            label: '全部消息 (23)',
            children: (
              <List
                dataSource={notifications}
                renderItem={item => (
                  <List.Item
                    style={{
                      backgroundColor: getNotificationColor(item.type),
                      padding: '12px',
                      marginBottom: '8px',
                      borderRadius: '4px'
                    }}
                  >
                    <List.Item.Meta
                      avatar={getNotificationIcon(item.type)}
                      title={item.title}
                      description={
                        <div>
                          <p>{item.content}</p>
                          {item.location && (
                            <p style={{ margin: '8px 0' }}>
                              <small>位置: {item.location}</small>
                            </p>
                          )}
                          {item.dueDate && (
                            <p style={{ margin: '8px 0' }}>
                              <small>截止日期: {item.dueDate}</small>
                            </p>
                          )}
                          <Space size="small" style={{ marginTop: '8px' }}>
                            {item.actions.map((action, index) => (
                              <Button key={index} type="link" size="small">
                                {action}
                              </Button>
                            ))}
                          </Space>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            )
          },
          {
            key: 'system',
            label: '系统通知 (5)',
            children: 'System Notifications Content'
          },
          {
            key: 'device',
            label: '设备告警 (12)',
            children: 'Device Alerts Content'
          },
          {
            key: 'task',
            label: '任务提醒 (6)',
            children: 'Task Reminders Content'
          }
        ]}
      />
      <div style={{ marginTop: '16px', textAlign: 'center' }}>
        <Button type="link" block>
          查看全部通知历史
        </Button>
      </div>
    </Drawer>
  );
};

export default NotificationDrawer;