.log-management {
  padding: 24px;
  /* height: 100%; */
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  gap: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left h2 {
  margin: 8px 0 0;
  font-size: 24px;
  font-weight: 500;
}

.log-type-switch {
  text-align: center;
}

.log-type-switch .ant-radio-group {
  display: inline-flex;
  gap: 8px;
}

.time-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.advanced-filter {
  background: #fff;
  border-radius: 4px;
}

.log-visualization {
  background: #fff;
  border-radius: 4px;
}

.visualization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.trend-chart {
  font-family: monospace;
  font-size: 24px;
  line-height: 1;
  color: #1890ff;
}

.level-distribution {
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
}

.level-row {
  white-space: pre;
  margin-bottom: 4px;
}

.level-row.error {
  color: #ff4d4f;
}

.level-row.warn {
  color: #faad14;
}

.level-row.info {
  color: #1890ff;
}

.level-row.debug {
  color: #8c8c8c;
}

.log-table {
  flex: 1;
  min-height: 0;
}

.detail-label {
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 8px;
}

.exception-info pre {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  margin: 8px 0;
  overflow: auto;
}

/* Responsive styles */
@media (max-width: 768px) {
  .log-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .time-selector {
    flex-direction: column;
    gap: 16px;
  }

  .time-selector .ant-space {
    width: 100%;
  }

  .time-selector .ant-picker {
    width: 100% !important;
  }

  .advanced-filter .ant-space {
    width: 100%;
  }

  .advanced-filter .ant-select,
  .advanced-filter .ant-input-search {
    width: 100% !important;
  }

  .ant-drawer {
    width: 100% !important;
  }
}

.search-button{
  background-color: #1890ff;
  color: #fff;
}