import {
  Form,
  Input,
  Select,
  InputN<PERSON>ber,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Switch,
  Col
} from 'antd'
import PropTypes from 'prop-types'
import { useEffect } from 'react'

const { Option } = Select

const DetailStrategy = ({ handleBackToList, configType, strategyData }) => {
  const [form] = Form.useForm()

  // Set initial form values when component mounts or strategyData changes
  useEffect(() => {
    if (strategyData) {
      form.setFieldsValue(strategyData)
    }
  }, [strategyData, form])

  const renderChannelFields = channelNumber => {
    return (
      <Card
        title={`超声波通道${channelNumber}设置`}
        style={{ marginBottom: 16 }}
        key={channelNumber}
      >
        <Form.Item
          name={`throttle${channelNumber}`}
          label='是否开启'
          valuePropName='checked'
        >
          <Switch disabled/>
        </Form.Item>

        <Form.Item
          name={`frequency${channelNumber}`}
          label='频率模式'
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
        >
          <Select disabled>
            <Option value={1}>自动变频模式</Option>
            <Option value={2}>固定频率模式</Option>
            <Option value={3}>固定跳频模式</Option>
          </Select>
        </Form.Item>
        <Form.Item
          name={`firstFrequency${channelNumber}`}
          label='频率1'
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
        >
          <InputNumber
            style={{ width: '100%' }}
            suffix='KHz'
            min={20}
            max={65}
            step={0.1}
            disabled
          />
        </Form.Item>
        <Form.Item
          name={`firstTime${channelNumber}`}
          label='持续时长'
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
        >
          <InputNumber
            style={{ width: '100%' }}
            suffix='s'
            min={10}
            max={60}
            disabled
          />
        </Form.Item>
        <Form.Item
          name={`secondFrequency${channelNumber}`}
          label='频率2'
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
        >
          <InputNumber
            style={{ width: '100%' }}
            suffix='KHz'
            min={20}
            max={65}
            step={0.1}
            disabled
          />
        </Form.Item>
        <Form.Item
          name={`secondTime${channelNumber}`}
          label='持续时长'
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
        >
          <InputNumber
            style={{ width: '100%' }}
            suffix='s'
            min={10}
            max={60}
            disabled
          />
        </Form.Item>
        <Form.Item
          name={`thirdFrequency${channelNumber}`}
          label='频率3'
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
        >
          <InputNumber
            style={{ width: '100%' }}
            suffix='KHz'
            min={20}
            max={65}
            step={0.1}
            disabled
          />
        </Form.Item>
        <Form.Item
          name={`thirdTime${channelNumber}`}
          label='持续时长'
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
        >
          <InputNumber
            style={{ width: '100%' }}
            suffix='s'
            min={10}
            max={60}
            disabled
          />
        </Form.Item>
        <Form.Item
          name={`powerMode`}
          label='强度模式'
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
        >
          <Select disabled>
            <Option value={1}>自动强度模式</Option>
            <Option value={2}>固定强度模式</Option>
          </Select>
        </Form.Item>
        <Form.Item
          name={`prePower`}
          label='预设强度'
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
        >
          <InputNumber
            style={{ width: '100%' }}
            min={10}
            max={100}
            suffix='%'
            disabled
          />
        </Form.Item>
      </Card>
    )
  }

  const renderBasicInfo = () => (
    <Card title='基本信息' style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item
            name='name'
            label='策略名称'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <Input disabled />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            name='typeCode'
            label='策略类型'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <Select
              placeholder='请选择策略类型'
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              options={configType.map(item => ({
                value: item.key,
                label: item.title
              }))}
              disabled
            />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            name='workStartTime'
            label='工作开始时间'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <TimePicker format='HH:mm' style={{ width: '100%' }} disabled />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            name='workEndTime'
            label='工作结束时间'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <TimePicker format='HH:mm' style={{ width: '100%' }} disabled />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16} style={{ height: 56 }}>
        <Col span={6}>
          <Form.Item
            name='ultrasoundWorkMode'
            label='超声波工作模式'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <Select disabled>
              <Option value='always'>持续模式</Option>
              <Option value='trigger'>触发模式</Option>
              <Option value='switch'>开关模式</Option>
              <Option value='unmanned'>无人工作模式</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            name='ultrasoundDuration'
            label='持续时间'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <InputNumber style={{ width: '100%' }} suffix='s' disabled />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            name='ultrasoundOpenTime'
            label='开始时间'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <TimePicker format='HH:mm' style={{ width: '100%' }} disabled />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            name='ultrasoundCloseTime'
            label='结束时间'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <TimePicker format='HH:mm' style={{ width: '100%' }} disabled />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16} style={{ height: 56 }}>
        <Col span={6}>
          <Form.Item
            name='ledWorkMode'
            label='灯光工作模式'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <Select disabled>
              <Option value='always'>持续模式</Option>
              <Option value='trigger'>触发模式</Option>
              <Option value='switch'>开关模式</Option>
              <Option value='unmanned'>无人工作模式</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            name='ledDuration'
            label='持续时间'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <InputNumber style={{ width: '100%' }} suffix='s' disabled />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            name='ledOpenTime'
            label='开始时间'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <TimePicker format='HH:mm' style={{ width: '100%' }} disabled />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            name='ledCloseTime'
            label='结束时间'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <TimePicker format='HH:mm' style={{ width: '100%' }} disabled />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item
            name='soundWorkMode'
            label='喇叭工作模式'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <Select disabled>
              <Option value='always'>持续模式</Option>
              <Option value='trigger'>触发模式</Option>
              <Option value='switch'>开关模式</Option>
              <Option value='unmanned'>无人工作模式</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
    </Card>
  )

  const renderLEDSettings = () => (
    <Card title='灯光设置' style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name='ledColor'
            label='灯光颜色'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <Select disabled>
              <Option value={'1'}>白色</Option>
              <Option value={'2'}>蓝色</Option>
              <Option value={'3'}>蓝白混合</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='ledPower'
            label='灯光强度'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <Slider min={0} max={100} disabled tooltip={{ open: true }} />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='ledTime'
            label='持续点亮时长'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <InputNumber style={{ width: '100%' }} suffix='s' disabled />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='ledFrequency'
            label='间隔时长'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <InputNumber style={{ width: '100%' }} suffix='s' disabled />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  )

  const renderSpeakerSettings = () => (
    <Card title='可闻声设置' style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name='playFrequency'
            label='播放强度'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <Slider
              defaultValue={50}
              min={0}
              max={100}
              disabled
              tooltip={{ open: true }}
            />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='soundSleepTime'
            label='可闻声间隔时间'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <InputNumber style={{ width: '100%' }} suffix='s' disabled />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='soundRepetitions'
            label='重复播放次数'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <InputNumber style={{ width: '100%' }} min={1} disabled />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='fileName'
            label='播放文件'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <Input disabled />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  )

  return (
    <Form
      form={form}
      layout='horizontal'
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      initialValues={{
        ultrasoundDuration: 30,
        ultrasoundWorkMode: 'always',
        ledWorkMode: 'always',
        soundWorkMode: 'trigger',
        ledDuration: 30,
        ledPower: 50,
        ledFrequency: 1,
        ledTime: 1,
        playFrequency: 50,
        soundSleepTime: 10,
        powerMode: 1,
        thirdTime1: 30,
        firstTime1: 30,
        secondTime1: 30,
        thirdTime2: 30,
        firstTime2: 30,
        secondTime2: 30,
        thirdTime3: 30,
        firstTime3: 30,
        secondTime3: 30,
        thirdTime4: 30,
        firstTime4: 30,
        secondTime4: 30,
        thirdTime5: 30,
        firstTime5: 30,
        secondTime5: 30,
        thirdTime6: 30,
        firstTime6: 30,
        secondTime6: 30,
        frequency1: 1,
        frequency2: 1,
        frequency3: 1,
        frequency4: 1,
        frequency5: 1,
        frequency6: 1
      }}
    >
      {renderBasicInfo()}
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(4, 1fr)',
          gap: '16px',
          marginBottom: '16px'
        }}
      >
        <div>{renderChannelFields(1)}</div>
        <div>{renderChannelFields(2)}</div>
        <div>{renderChannelFields(3)}</div>
        <div>{renderChannelFields(4)}</div>
        <div>{renderChannelFields(5)}</div>
        <div>{renderChannelFields(6)}</div>
        <div>{renderLEDSettings()}</div>
        <div>{renderSpeakerSettings()}</div>
      </div>
      <Form.Item style={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button onClick={handleBackToList}>返回</Button>
      </Form.Item>
    </Form>
  )
}

export default DetailStrategy

DetailStrategy.propTypes = {
  handleBackToList: PropTypes.func.isRequired,
  configType: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string,
      key: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
    })
  ).isRequired,
  strategyData: PropTypes.object
}
