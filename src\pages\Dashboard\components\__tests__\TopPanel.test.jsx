import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import TopPanel from '../TopPanel';

// Mock react-router-dom
const MockRouter = ({ children }) => (
  <BrowserRouter>{children}</BrowserRouter>
);

describe('TopPanel Component', () => {
  const defaultProps = {
    onlineRate: 85.5,
    totalDevices: 100,
    healthScore: 92,
    alarms: []
  };

  it('renders without crashing with empty alarms array', () => {
    render(
      <MockRouter>
        <TopPanel {...defaultProps} />
      </MockRouter>
    );
    
    expect(screen.getByText('设备总数')).toBeInTheDocument();
    expect(screen.getByText('设备健康度')).toBeInTheDocument();
    expect(screen.getByText('本月告警概览')).toBeInTheDocument();
  });

  it('renders without crashing with null alarms', () => {
    const props = { ...defaultProps, alarms: null };
    
    render(
      <MockRouter>
        <TopPanel {...props} />
      </MockRouter>
    );
    
    expect(screen.getByText('暂无告警数据')).toBeInTheDocument();
  });

  it('renders without crashing with undefined alarms', () => {
    const props = { ...defaultProps, alarms: undefined };
    
    render(
      <MockRouter>
        <TopPanel {...props} />
      </MockRouter>
    );
    
    expect(screen.getByText('暂无告警数据')).toBeInTheDocument();
  });

  it('renders alarm data correctly with string levels', () => {
    const props = {
      ...defaultProps,
      alarms: [
        { id: 1, count: 5, level: '低' },
        { id: 2, count: 3, level: '中' },
        { id: 3, count: 1, level: '高' }
      ]
    };
    
    render(
      <MockRouter>
        <TopPanel {...props} />
      </MockRouter>
    );
    
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('低')).toBeInTheDocument();
    expect(screen.getByText('中')).toBeInTheDocument();
    expect(screen.getByText('高')).toBeInTheDocument();
  });

  it('renders alarm data correctly with numeric levels', () => {
    const props = {
      ...defaultProps,
      alarms: [
        { id: 1, count: 5, level: 0 },
        { id: 2, count: 3, level: 1 },
        { id: 3, count: 1, level: 2 },
        { id: 4, count: 2, level: 3 }
      ]
    };
    
    render(
      <MockRouter>
        <TopPanel {...props} />
      </MockRouter>
    );
    
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    // Should convert numeric levels to text
    expect(screen.getAllByText('低')).toHaveLength(1);
    expect(screen.getAllByText('中')).toHaveLength(1);
    expect(screen.getAllByText('高')).toHaveLength(1);
    expect(screen.getAllByText('紧急')).toHaveLength(1);
  });

  it('handles malformed alarm data gracefully', () => {
    const props = {
      ...defaultProps,
      alarms: [
        { count: null, level: null },
        { count: undefined, level: undefined },
        {},
        { count: 5 }, // missing level
        { level: '低' } // missing count
      ]
    };
    
    render(
      <MockRouter>
        <TopPanel {...props} />
      </MockRouter>
    );
    
    // Should render without crashing and show default values
    expect(screen.getAllByText('0')).toHaveLength(4); // Default count for missing/null counts
    expect(screen.getAllByText('未知')).toHaveLength(3); // Default level for missing/null levels
    expect(screen.getByText('5')).toBeInTheDocument(); // Valid count
    expect(screen.getByText('低')).toBeInTheDocument(); // Valid level
  });
});
