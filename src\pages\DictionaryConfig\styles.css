.dictionary-config {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left {
  margin-bottom: 16px;
}

.header-left h2 {
  margin: 8px 0 0;
  font-size: 24px;
  font-weight: 500;
}

.dict-content {
  margin-top: 24px;
  flex: 1;
  min-height: 0;
}

.dict-types {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dict-types-header {
  margin-bottom: 16px;
}

.dict-type-tree {
  max-height: calc(100vh - 300px);
}

.dict-type-tree .ant-tree {
  flex: 1;
  overflow: auto;
}

/* Tree node custom styles */
.ant-tree-title {
  display: inline-block;
  width: 100%;
}

.dict-type-tree .ant-tree-treenode {
  width: 100% !important;
}

.dict-type-tree .ant-tree-node-content-wrapper {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 24px);
}

.dict-type-tree .ant-tree-node-content-wrapper:hover .ant-space {
  opacity: 1;
}

/* 树节点样式 */
.tree-node-container {
  display: flex;
  width: 100%;
  position: relative;
  align-items: center;
}

.tree-node-title {
  flex-grow: 1;
  padding-right: 10px;
}

.tree-node-actions {
  position: absolute;
  right: -120px;
  white-space: nowrap;
  z-index: 1;
}

.dict-items {
  min-height: calc(100vh - 200px);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dict-preview {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
}

.dict-preview h4 {
  margin-bottom: 8px;
}

.dict-info {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
}

.dict-info h4 {
  margin-bottom: 8px;
}

/* Modal styles */
.ant-modal-body .ant-form-item {
  margin-bottom: 24px;
}

.ant-modal-body .ant-radio-group {
  width: 100%;
  display: flex;
  gap: 8px;
}

.ant-modal-body .ant-radio-button-wrapper {
  flex: 1;
  text-align: center;
}

/* Responsive styles */
@media (max-width: 768px) {
  .dictionary-config {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .dict-content {
    flex-direction: column;
  }

  .dict-types,
  .dict-items {
    margin-bottom: 16px;
  }

  .ant-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .ant-modal {
    max-width: 100vw;
    margin: 8px;
  }
}

.upload-area {
  width: 100%;
  margin-top: 16px;
}