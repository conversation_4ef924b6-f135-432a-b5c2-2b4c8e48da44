.upgrade-packages {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.page-header {
  margin-bottom: 24px;
}

.header-left {
  margin-bottom: 16px;
}

.header-left h2 {
  margin: 8px 0 0;
  font-size: 24px;
  font-weight: 500;
}

.upgrade-packages-filter-bar {
  margin-bottom: 24px;
}

.statistics-cards {
  margin-bottom: 24px;
}

.statistics-cards .ant-card {
  border-radius: 8px;
}

.package-list {
  flex: 1;
  /* min-height: 0; */
}

/* Modal styles */
.ant-modal-body .ant-form-item {
  margin-bottom: 24px;
}

.ant-modal-body .ant-radio-group {
  width: 100%;
  display: flex;
  gap: 8px;
}

.ant-modal-body .ant-radio-button-wrapper {
  flex: 1;
  text-align: center;
}

/* Upload styles */
.ant-upload-drag {
  padding: 24px;
}

.ant-upload-drag-icon {
  margin-bottom: 16px;
}

.ant-upload-text {
  margin-bottom: 8px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .upgrade-packages {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .statistics-cards .ant-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .ant-modal {
    max-width: 100vw;
    margin: 8px;
  }
}