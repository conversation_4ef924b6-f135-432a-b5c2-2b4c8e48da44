import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Table,
  Tag,
  Breadcrumb,
  Input,
  Select,
  DatePicker,
  Tooltip,
  Badge,
  Timeline,
  Drawer,
  Statistic,
  Radio,
  Tabs,
  List,
  Switch
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  DownloadOutlined,
  ReloadOutlined,
  UserOutlined,
  ClockCircleOutlined,
  ApiOutlined,
  EnvironmentOutlined,
  AuditOutlined,
  EyeOutlined,
  ExportOutlined,
  SettingOutlined,
  FileTextOutlined,
  WarningOutlined,
  ExclamationCircleOutlined,
  AlertOutlined,
  QuestionCircleOutlined,
  TableOutlined,
  HistoryOutlined,
  GlobalOutlined,
  BarChartOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import './styles.css';

const { Search } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;

const SecurityAudit = () => {
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [viewMode, setViewMode] = useState('list');
  const [timeRange, setTimeRange] = useState('today');
  const [dateRange, setDateRange] = useState([dayjs().startOf('day'), dayjs().endOf('day')]);

  // Mock data for statistics
  const statistics = [
    {
      title: '总日志',
      value: 1458,
      trend: 12,
      icon: <AuditOutlined style={{ fontSize: 24, color: '#1890ff' }} />
    },
    {
      title: '严重',
      value: 3,
      icon: <ExclamationCircleOutlined style={{ fontSize: 24, color: '#ff4d4f' }} />
    },
    {
      title: '错误',
      value: 17,
      icon: <WarningOutlined style={{ fontSize: 24, color: '#faad14' }} />
    },
    {
      title: '可疑操作',
      value: 8,
      icon: <AlertOutlined style={{ fontSize: 24, color: '#722ed1' }} />
    }
  ];

  // Mock data for audit logs
  const auditLogs = [
    {
      id: '1',
      severity: 'critical',
      timestamp: '2024-02-20 14:32:00',
      user: {
        name: 'admin',
        role: '系统管理员',
        network: '办公网络'
      },
      module: 'management',
      operation: 'delete',
      resource: '设备配置',
      status: 'success',
      ip: '********',
      location: '杭州',
      details: {
        action: '删除设备配置',
        target: 'device-config-001',
        changes: {
          before: { status: 'active' },
          after: 'deleted'
        }
      }
    },
    {
      id: '2',
      severity: 'warning',
      timestamp: '2024-02-20 14:30:00',
      user: {
        name: 'user123',
        role: '运维人员',
        network: '外部网络'
      },
      module: 'auth',
      operation: 'modify',
      resource: '用户组',
      status: 'success',
      ip: '************',
      location: '上海',
      details: {
        action: '修改权限',
        target: 'user-group-002',
        changes: {
          before: { permissions: ['read'] },
          after: { permissions: ['read', 'write'] }
        }
      }
    }
  ];

  // Table columns
  const columns = [
    {
      title: '',
      key: 'severity',
      width: 48,
      render: (_, record) => (
        <Tooltip title={
          record.severity === 'critical' ? '严重' :
          record.severity === 'warning' ? '警告' :
          record.severity === 'info' ? '信息' : '未知'
        }>
          {record.severity === 'critical' ? '🔴' :
           record.severity === 'warning' ? '🟠' :
           record.severity === 'info' ? '🔵' : '⚪'}
        </Tooltip>
      )
    },
    {
      title: '时间戳',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (text) => (
        <Space direction="vertical" size={0}>
          <span>{dayjs(text).format('MM-DD HH:mm:ss')}</span>
          <small style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
            {dayjs(text).fromNow()}
          </small>
        </Space>
      )
    },
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      render: (user) => (
        <Space direction="vertical" size={0}>
          <Space>
            <UserOutlined />
            <span>{user.name}</span>
            <Tag>{user.role}</Tag>
          </Space>
          <small style={{ color: 'rgba(0, 0, 0, 0.45)' }}>{user.network}</small>
        </Space>
      )
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      render: (ip, record) => (
        <Space direction="vertical" size={0}>
          <span>{ip}</span>
          <small style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
            <EnvironmentOutlined /> {record.location}
          </small>
        </Space>
      )
    },
    {
      title: '操作',
      key: 'operation',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Space>
            <Tag color={
              record.module === 'management' ? 'blue' :
              record.module === 'auth' ? 'purple' :
              record.module === 'system' ? 'cyan' : 'default'
            }>
              {record.module === 'management' ? '管理操作' :
               record.module === 'auth' ? '授权' :
               record.module === 'system' ? '系统操作' : '其他'}
            </Tag>
            <span>{record.operation === 'delete' ? '删除' :
                   record.operation === 'modify' ? '修改' : record.operation}</span>
          </Space>
          <small style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
            资源: {record.resource}
          </small>
        </Space>
      )
    },
    {
      title: '结果',
      key: 'result',
      width: 100,
      render: (_, record) => (
        <Tag color={record.status === 'success' ? 'success' : 'error'}>
          {record.status === 'success' ? '成功' : '失败'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          onClick={() => {
            setSelectedRecord(record);
            setDrawerVisible(true);
          }}
        >
          详情 ▼
        </Button>
      )
    }
  ];

  const handleTimeRangeChange = (value) => {
    setTimeRange(value);
    switch (value) {
      case 'today':
        setDateRange([dayjs().startOf('day'), dayjs().endOf('day')]);
        break;
      case 'yesterday':
        setDateRange([dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')]);
        break;
      case 'week':
        setDateRange([dayjs().startOf('week'), dayjs().endOf('week')]);
        break;
      case 'month':
        setDateRange([dayjs().startOf('month'), dayjs().endOf('month')]);
        break;
      default:
        break;
    }
  };

  return (
    <div className="security-audit">
      {/* Header */}
      <div className="page-header">
        <div className="header-left">
          <Breadcrumb
            items={[
              { title: '首页' },
              { title: '安全控制' },
              { title: '操作审计' }
            ]}
          />
          <h2>操作审计</h2>
        </div>
        <div className="header-right">
          <Space>
            <Button icon={<FileTextOutlined />}>报表</Button>
            <Button icon={<DownloadOutlined />}>导出</Button>
            <Button icon={<SettingOutlined />}>设置</Button>
            {/* <Button icon={<ReloadOutlined />}>刷新</Button> */}
          </Space>
        </div>
      </div>

      {/* Time Range Selector */}
      <Card className="time-selector">
        <Space>
          <Select
            value={timeRange}
            onChange={handleTimeRangeChange}
            style={{ width: 120 }}
          >
            <Option value="today">今天</Option>
            <Option value="yesterday">昨天</Option>
            <Option value="week">本周</Option>
            <Option value="month">本月</Option>
            <Option value="custom">自定义</Option>
          </Select>
          <RangePicker
            showTime
            value={dateRange}
            onChange={(dates) => setDateRange(dates)}
            style={{ width: 380 }}
          />
          <Button type="primary">应用</Button>
        </Space>
      </Card>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} className="statistics-cards">
        {statistics.map((stat, index) => (
          <Col span={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
                suffix={stat.trend && (
                  <span className="trend-value">
                    <ArrowUpOutlined style={{ color: '#52c41a' }} />
                    {stat.trend}%
                  </span>
                )}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Advanced Filter */}
      <Card className="advanced-filter">
        <div className="filter-section">
          <div className="filter-label">日志类型:</div>
          <Space wrap>
            <Tag.CheckableTag checked>认证</Tag.CheckableTag>
            <Tag.CheckableTag checked>授权</Tag.CheckableTag>
            <Tag.CheckableTag checked>系统操作</Tag.CheckableTag>
            <Tag.CheckableTag checked>数据访问</Tag.CheckableTag>
            <Tag.CheckableTag checked>管理操作</Tag.CheckableTag>
            <Tag.CheckableTag checked>系统警报</Tag.CheckableTag>
            <Tag.CheckableTag checked>全部</Tag.CheckableTag>
          </Space>
        </div>

        <Row gutter={[16, 16]}>
          <Col span={8}>
            <div className="filter-label">结果:</div>
            <Space wrap>
              <Tag.CheckableTag checked>全部</Tag.CheckableTag>
              <Tag.CheckableTag>成功</Tag.CheckableTag>
              <Tag.CheckableTag>失败</Tag.CheckableTag>
              <Tag.CheckableTag>拒绝</Tag.CheckableTag>
              <Tag.CheckableTag>异常</Tag.CheckableTag>
              <Tag.CheckableTag>超时</Tag.CheckableTag>
            </Space>
          </Col>
          <Col span={8}>
            <div className="filter-label">严重性:</div>
            <Space wrap>
              <Tag.CheckableTag checked>全部</Tag.CheckableTag>
              <Tag.CheckableTag>信息</Tag.CheckableTag>
              <Tag.CheckableTag>警告</Tag.CheckableTag>
              <Tag.CheckableTag>错误</Tag.CheckableTag>
              <Tag.CheckableTag>严重</Tag.CheckableTag>
            </Space>
          </Col>
          <Col span={8}>
            <Input.Search
              placeholder="用户/IP..."
              allowClear
              style={{ width: '100%' }}
            />
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={8}>
            <Select
              placeholder="资源类型"
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="device">设备</Option>
              <Option value="user">用户</Option>
              <Option value="config">配置</Option>
              <Option value="data">数据</Option>
            </Select>
          </Col>
          <Col span={8}>
            <Select
              placeholder="操作类型"
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="create">创建</Option>
              <Option value="read">读取</Option>
              <Option value="update">更新</Option>
              <Option value="delete">删除</Option>
            </Select>
          </Col>
          <Col span={8}>
            <Button icon={<FilterOutlined />}>
              高级筛选
            </Button>
          </Col>
        </Row>
      </Card>

      {/* View Mode Switch */}
      <Card className="view-switch">
        <Space>
          <Radio.Group value={viewMode} onChange={(e) => setViewMode(e.target.value)}>
            <Radio.Button value="list">
              <TableOutlined /> 列表视图
            </Radio.Button>
            <Radio.Button value="timeline">
              <HistoryOutlined /> 时间线视图
            </Radio.Button>
            <Radio.Button value="geo">
              <GlobalOutlined /> 地理视图
            </Radio.Button>
            <Radio.Button value="stats">
              <BarChartOutlined /> 统计分析
            </Radio.Button>
          </Radio.Group>
          <span style={{ marginLeft: 16 }}>实时监控</span>
          <Switch defaultChecked />
        </Space>
      </Card>

      {/* Audit Log Table */}
      <Card className="audit-table">
        <Table
          columns={columns}
          dataSource={auditLogs}
          pagination={{
            total: 100,
            pageSize: 10,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* Detail Drawer */}
      <Drawer
        title="审计日志详情"
        placement="right"
        width={600}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        {selectedRecord && (
          <Tabs
            items={[
              {
                key: 'basic',
                label: '基本信息',
                children: (
                  <Timeline
                    items={[
                      {
                        color: 'blue',
                        dot: <ClockCircleOutlined />,
                        children: (
                          <>
                            <div className="detail-label">时间信息</div>
                            <div>{selectedRecord.timestamp}</div>
                          </>
                        )
                      },
                      {
                        color: 'green',
                        dot: <UserOutlined />,
                        children: (
                          <>
                            <div className="detail-label">用户信息</div>
                            <Space direction="vertical">
                              <Space>
                                <span>{selectedRecord.user.name}</span>
                                <Tag>{selectedRecord.user.role}</Tag>
                              </Space>
                              <span>网络: {selectedRecord.user.network}</span>
                            </Space>
                          </>
                        )
                      },
                      {
                        color: 'orange',
                        dot: <ApiOutlined />,
                        children: (
                          <>
                            <div className="detail-label">操作信息</div>
                            <Space direction="vertical">
                              <Space>
                                <Tag>{selectedRecord.module}</Tag>
                                <span>{selectedRecord.operation}</span>
                              </Space>
                              <span>资源: {selectedRecord.resource}</span>
                            </Space>
                          </>
                        )
                      }
                    ]}
                  />
                )
              },
              {
                key: 'details',
                label: '详细信息',
                children: (
                  <Card size="small">
                    <pre style={{ margin: 0 }}>
                      {JSON.stringify(selectedRecord.details, null, 2)}
                    </pre>
                  </Card>
                )
              },
              {
                key: 'related',
                label: '关联日志',
                children: (
                  <List
                    size="small"
                    dataSource={[]}
                    renderItem={(item) => (
                      <List.Item>
                        {item}
                      </List.Item>
                    )}
                  />
                )
              }
            ]}
          />
        )}
      </Drawer>
    </div>
  );
};

export default SecurityAudit;