.maintenance-monitor {
  padding: 24px;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  gap: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left h2 {
  margin: 8px 0 0;
  font-size: 24px;
  font-weight: 500;
}

.health-score .ant-card {
  background: #fff;
  border-radius: 4px;
  height: 100%;
}

.alert-overview .ant-card {
  background: #fff;
  border-radius: 4px;
  text-align: center;
}

.maintenance-monitor-content {
  flex: 1;
  min-height: 0;
  background: #fff;
  border-radius: 4px;
}

.maintenance-monitor-content .ant-card-body{
  min-width: 500px;
}

.monitor-tabs .ant-tabs-nav {
  margin-bottom: 16px;
}

.monitor-tabs .ant-tabs-tab {
  padding: 12px 24px;
}

.monitor-tabs .ant-tabs-tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-control {
  background: #fff;
  border-radius: 4px;
}

.system-events {
  background: #fff;
  border-radius: 4px;
}

.resource-monitoring .ant-card {
  height: 100%;
}

/* Responsive styles */
@media (max-width: 768px) {
  .maintenance-monitor {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .alert-overview .ant-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .time-control .ant-space {
    flex-direction: column;
    width: 100%;
  }

  .time-control .ant-radio-group {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .time-control .ant-picker {
    width: 100%;
  }

  .time-control .ant-select {
    width: 100% !important;
  }
}