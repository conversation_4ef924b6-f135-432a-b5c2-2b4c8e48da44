.sensing-schedule {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.statistics-cards {
  margin-bottom: 24px;
}

.statistics-cards .ant-card {
  border-radius: 8px;
}

.trend-info {
  margin-top: 8px;
  font-size: 14px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-label {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.main-content {
  flex: 1;
  background: transparent;
  min-height: 0;
}

.group-sider {
  background: transparent !important;
  margin-right: 24px;
}

.tree-card {
  height: 100%;
}

.tree-search {
  margin: 16px 0;
}

.record-content {
  background: transparent !important;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-bar {
  background: #fff;
  border-radius: 4px;
}

.record-table {
  flex: 1;
  background: #fff;
  border-radius: 8px;
}

.execution-details .ant-descriptions {
  margin-bottom: 24px;
}

.execution-details .ant-card {
  margin-bottom: 24px;
}

.execution-details .ant-tabs-content {
  padding: 16px 0;
}

/* Modal styles */
.ant-modal-body .ant-form-item {
  margin-bottom: 24px;
}

.ant-modal-body .ant-radio-group {
  width: 100%;
  display: flex;
  gap: 8px;
}

.ant-modal-body .ant-radio-button-wrapper {
  flex: 1;
  text-align: center;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .statistics-cards .ant-col {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 768px) {
  .sensing-schedule {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .statistics-cards .ant-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .main-content {
    flex-direction: column;
  }

  .group-sider {
    width: 100% !important;
    max-width: 100% !important;
    margin-right: 0;
    margin-bottom: 24px;
  }

  .ant-modal {
    max-width: 100vw;
    margin: 8px;
  }
}