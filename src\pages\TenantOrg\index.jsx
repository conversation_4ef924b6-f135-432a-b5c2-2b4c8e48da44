import React, { useState } from 'react';
import {
  Layout,
  Card,
  Tree,
  Input,
  Button,
  Space,
  Breadcrumb,
  Dropdown,
  Tag,
  Tooltip,
  Form,
  Modal,
  Select,
  Descriptions,
  Avatar,
  Badge,
  message,
  Tabs
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  ReloadOutlined,
  DownloadOutlined,
  QuestionCircleOutlined,
  BuildOutlined,
  BankOutlined,
  TeamOutlined,
  ProjectOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  ExpandOutlined,
  CompressOutlined,
  GlobalOutlined,
  UserOutlined,
  EnvironmentOutlined,
  ApartmentOutlined
} from '@ant-design/icons';
import './styles.css';

const { Sider, Content } = Layout;
const { Search } = Input;
const { DirectoryTree } = Tree;

const TenantOrg = () => {
  const [selectedOrg, setSelectedOrg] = useState(null);
  const [expandedKeys, setExpandedKeys] = useState(['0-0']);
  const [searchValue, setSearchValue] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalType, setModalType] = useState('create'); // 'create' or 'edit'
  const [form] = Form.useForm();

  // Mock tenant list data
  const tenants = [
    {
      key: '0-0',
      title: '科技公司A',
      icon: <BankOutlined />,
      children: [
        {
          key: '0-0-0',
          title: '研发中心',
          icon: <BuildOutlined />,
          children: [
            {
              key: '0-0-0-0',
              title: '前端组',
              icon: <TeamOutlined />,
              isLeaf: true
            },
            {
              key: '0-0-0-1',
              title: '后端组',
              icon: <TeamOutlined />,
              isLeaf: true
            }
          ]
        },
        {
          key: '0-0-1',
          title: '产品部',
          icon: <ProjectOutlined />,
          children: [
            {
              key: '0-0-1-0',
              title: '设计组',
              icon: <TeamOutlined />,
              isLeaf: true
            }
          ]
        }
      ]
    }
  ];

  // Mock organization unit data
  const mockOrgData = {
    '0-0-0-0': {
      name: '前端组',
      code: 'FE-TEAM',
      type: 'team',
      status: 'active',
      path: '/科技公司A/研发中心/前端组',
      manager: {
        name: '张三',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=John',
        title: '前端负责人'
      },
      location: '杭州总部 3楼',
      businessUnit: '研发中心',
      members: 8,
      devices: 12,
      createTime: '2024-01-15',
      updateTime: '2024-02-20',
      description: '负责公司前端开发和维护工作'
    }
  };

  const handleSearch = (value) => {
    setSearchValue(value);
  };

  const handleTreeSelect = (selectedKeys, info) => {
    setSelectedOrg(mockOrgData[selectedKeys[0]] || null);
  };

  const handleExpand = (keys) => {
    setExpandedKeys(keys);
  };

  const showModal = (type) => {
    setModalType(type);
    setIsModalVisible(true);
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      console.log('Form values:', values);
      setIsModalVisible(false);
      form.resetFields();
    });
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const getStatusBadge = (status) => {
    const statusMap = {
      active: { color: 'green', text: '活跃' },
      inactive: { color: 'gray', text: '禁用' },
      pending: { color: 'gold', text: '待激活' }
    };
    return statusMap[status] || statusMap.active;
  };

  return (
    <div className="tenant-org">
      {/* Page Header */}
      <div className="page-header">
        <div className="header-left">
          <Breadcrumb
            items={[
              { title: '首页' },
              { title: '租户管理' },
              { title: '组织架构' }
            ]}
          />
          <h2>组织架构管理</h2>
        </div>
        <div className="header-right">
          <Space>
            <Dropdown
              menu={{
                items: [
                  { key: 'excel', label: '导出到Excel' },
                  { key: 'pdf', label: '导出到PDF' }
                ]
              }}
            >
              <Button icon={<DownloadOutlined />}>导出组织架构</Button>
            </Dropdown>
            <Tooltip title="查看帮助文档">
              <Button icon={<QuestionCircleOutlined />}>帮助</Button>
            </Tooltip>
          </Space>
        </div>
      </div>

      {/* Main Content */}
      <Layout className="main-content">
        {/* Left Sider */}
        <Sider width={320} className="org-sider">
          <Card className="tree-card">
            <div className="tree-header">
              <Search
                placeholder="搜索组织单元..."
                allowClear
                onChange={(e) => handleSearch(e.target.value)}
              />
              <Space className="tree-tools">
                <Button icon={<ReloadOutlined />} size="small" />
                <Button icon={<ExpandOutlined />} size="small" />
              </Space>
            </div>
            <DirectoryTree
              treeData={tenants}
              onSelect={handleTreeSelect}
              onExpand={handleExpand}
              expandedKeys={expandedKeys}
              titleRender={(nodeData) => (
                <div className="tree-node">
                  <span className="node-title">{nodeData.title}</span>
                  <Space className="node-actions">
                    <Button
                      type="text"
                      size="small"
                      icon={<PlusOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        showModal('create');
                      }}
                    />
                    <Button
                      type="text"
                      size="small"
                      icon={<MoreOutlined />}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Space>
                </div>
              )}
            />
          </Card>
        </Sider>

        {/* Right Content */}
        <Content className="org-content">
          {selectedOrg ? (
            <div className="org-detail">
              {/* Action Bar */}
              <Card className="action-bar">
                <Space>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => showModal('create')}
                  >
                    添加子组织单元
                  </Button>
                  <Button
                    icon={<EditOutlined />}
                    onClick={() => showModal('edit')}
                  >
                    编辑
                  </Button>
                  <Button icon={<DeleteOutlined />} danger>
                    删除
                  </Button>
                </Space>
              </Card>

              {/* Detail Cards */}
              <div className="detail-cards">
                <Card title="基本信息" className="info-card">
                  <Descriptions column={2}>
                    <Descriptions.Item label="组织名称">
                      {selectedOrg.name}
                    </Descriptions.Item>
                    <Descriptions.Item label="组织代码">
                      {selectedOrg.code}
                    </Descriptions.Item>
                    <Descriptions.Item label="组织类型">
                      <Tag icon={<ApartmentOutlined />}>{selectedOrg.type}</Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="状态">
                      <Badge
                        status={getStatusBadge(selectedOrg.status).color}
                        text={getStatusBadge(selectedOrg.status).text}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="组织路径" span={2}>
                      {selectedOrg.path}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>

                <Card title="管理信息" className="info-card">
                  <Descriptions column={2}>
                    <Descriptions.Item label="负责人">
                      <Space>
                        <Avatar src={selectedOrg.manager.avatar} />
                        <span>{selectedOrg.manager.name}</span>
                        <Tag>{selectedOrg.manager.title}</Tag>
                      </Space>
                    </Descriptions.Item>
                    <Descriptions.Item label="位置">
                      <Space>
                        <EnvironmentOutlined />
                        {selectedOrg.location}
                      </Space>
                    </Descriptions.Item>
                    <Descriptions.Item label="业务单元">
                      {selectedOrg.businessUnit}
                    </Descriptions.Item>
                    <Descriptions.Item label="成员数量">
                      {selectedOrg.members} 人
                    </Descriptions.Item>
                    <Descriptions.Item label="设备数量">
                      {selectedOrg.devices} 台
                    </Descriptions.Item>
                  </Descriptions>
                </Card>

                <Card title="元数据信息" className="info-card">
                  <Descriptions column={2}>
                    <Descriptions.Item label="创建时间">
                      {selectedOrg.createTime}
                    </Descriptions.Item>
                    <Descriptions.Item label="最后更新">
                      {selectedOrg.updateTime}
                    </Descriptions.Item>
                    <Descriptions.Item label="描述" span={2}>
                      {selectedOrg.description}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </div>
            </div>
          ) : (
            <div className="empty-state">
              <GlobalOutlined style={{ fontSize: 48, color: '#1890ff' }} />
              <h3>请选择组织单元</h3>
              <p>在左侧选择一个组织单元以查看详细信息</p>
            </div>
          )}
        </Content>
      </Layout>

      {/* Create/Edit Modal */}
      <Modal
        title={modalType === 'create' ? '创建组织单元' : '编辑组织单元'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={720}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={modalType === 'edit' ? selectedOrg : {}}
        >
          <Form.Item
            name="name"
            label="组织名称"
            rules={[{ required: true, message: '请输入组织名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="code"
            label="组织代码"
            rules={[{ required: true, message: '请输入组织代码' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="type"
            label="组织类型"
            rules={[{ required: true, message: '请选择组织类型' }]}
          >
            <Select>
              <Select.Option value="department">部门</Select.Option>
              <Select.Option value="team">团队</Select.Option>
              <Select.Option value="project">项目组</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="manager"
            label="负责人"
            rules={[{ required: true, message: '请选择负责人' }]}
          >
            <Select
              showSearch
              placeholder="选择负责人"
              optionFilterProp="children"
            >
              <Select.Option value="1">张三</Select.Option>
              <Select.Option value="2">李四</Select.Option>
              <Select.Option value="3">王五</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="location"
            label="位置"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={4} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TenantOrg;