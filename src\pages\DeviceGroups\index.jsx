import { useState, useEffect } from "react";
import {
  Tabs,
  Layout,
  Input,
  Button,
  Space,
  Breadcrumb,
  Tree,
  Modal,
  Form,
  message,
  Select,
  TreeSelect,
  Dropdown,
} from "antd";
import {
  EditOutlined,
  PlusOutlined,
  DeleteOutlined,
  MoreOutlined,
} from "@ant-design/icons";
import LocationGroupDetail from "./components/LocationGroupDetail";
import CustomerGroupDetail from "./components/CustomerGroupDetail";
import {
  getDeviceGroupsByLocation,
  getLocationDetail,
  getAdministrativeGroupsTree,
  getDeviceLocationTree,
  getCustomerGroupsTree,
  getCustomerDetail,
  addDeviceLocation,
  delCustomerGroup,
  delDeviceLocation,
  updateDeviceLocation,
  updateCustomerGroup,
  createCustomerGroup,
} from "../../service/deviceGroups";
import { getCustomerType } from "./../../service/dictionary";
import "./styles.css";
import useSelect from "../../hooks/useSelect";

const { Sider, Content } = Layout;

const DeviceGroups = () => {
  const [activeTab, setActiveTab] = useState("location");
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [treeData, setTreeData] = useState(null);
  const [administrativeGroupsTree, setAdministrativeGroupsTree] =
    useState(null);
  const [deviceLocationTree, setDeviceLocationTree] = useState(null);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [currentNode, setCurrentNode] = useState(null);
  const [customerType, setCustomerType] = useState(null); // 组织类型数据
  const [loading,setLoading] = useState(false);
  const [
    node,
    searchValue,
    autoExpandParent,
    filteredTreeData,
    setAutoExpandParent,
  ] = useSelect(treeData, setExpandedKeys);

  // 获取位置树
  const getLocationTree = async () => {
    setLoading(true);
    try {
      const res = await getDeviceGroupsByLocation(1);
      const key = [];
      const transformNode = (item) => {
        key.push(item.id || item.locationId);
        return {
          title: item.groupName || item.description,
          key: item.id || item.locationId,
          isLocal: item.isLocal,
          selectable: !!item.isLocal,
          children:
            item.children && item.children.length > 0
              ? item.children.map((child) => transformNode(child))
              : item.deviceLocationTreeVos &&
                item.deviceLocationTreeVos.length > 0
              ? item.deviceLocationTreeVos.map((child) => transformNode(child))
              : undefined,
        };
      };

      const transformedData = res.map((item) => transformNode(item));
      setExpandedKeys(key);
      setTreeData(transformedData);

      // 查找第一个可选的节点并自动选择
      const findFirstSelectableNode = (nodes) => {
        if (!nodes || nodes.length === 0) return null;

        for (const node of nodes) {
          if (node.selectable) {
            return node.key;
          }

          if (node.children) {
            const found = findFirstSelectableNode(node.children);
            if (found) return found;
          }
        }
        return null;
      };

      const firstSelectableKey = findFirstSelectableNode(transformedData);
      if (firstSelectableKey) {
        handleTreeSelect([firstSelectableKey]);
      }
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch location tree:", error);
    }
  };

  // 获取客户分组树
  const fetchCustomerGroupsTree = async () => {
    setLoading(true);
    try {
      const res = await getCustomerGroupsTree();
      const transformNode = (item) => ({
        title: item.ownershipName,
        key: item.ownershipId,
        value: item.ownershipId,
        isLocal: 1,
        children:
          item.children && item.children.length > 0
            ? item.children.map((child) => transformNode(child))
            : undefined,
      });
      const transformedData = res.map((item) => transformNode(item));
      setTreeData(transformedData);
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch customer groups tree:", error);
    }
  };

  // 获取行政分组树
  const fetchAdministrativeGroupsTree = async () => {
    try {
      const res = await getAdministrativeGroupsTree();
      const transformNode = (item) => ({
        title: item.groupName,
        value: item.id,
        key: item.id,
        selectable: !item.children || item.children.length === 0,
        children: item.children
          ? item.children.map((child) => transformNode(child))
          : undefined,
      });

      const transformedData = res.map((item) => transformNode(item));
      setAdministrativeGroupsTree(transformedData);
    } catch (error) {
      console.error("Failed to fetch administrative groups tree:", error);
    }
  };

  // 获取设备位置树
  const fetchDeviceLocationTree = async (groupId) => {
    try {
      const res = await getDeviceLocationTree(groupId);
      const transformNode = (item) => ({
        title: item.description,
        value: item.locationId,
        key: item.locationId,
        selectable: true,
        children: item.children
          ? item.children.map((child) => transformNode(child))
          : undefined,
      });

      const transformedData = res.map((item) => transformNode(item));
      setDeviceLocationTree(transformedData);
    } catch (error) {
      console.error("Failed to fetch device location tree:", error);
    }
  };

  // 获取组织类型
  const fetchCusCustomerType = async () => {
    try {
      const res = await getCustomerType();
      setCustomerType(res);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getLocationTree();
    // 清理函数，在组件卸载时取消请求
    return () => {
      if (window.cancelToken) {
        window.cancelToken(); // 取消请求
      }
    };
  }, []);

  // 选中树节点，获取详情
  const handleTreeSelect = async (selectedKeys) => {
    if (selectedKeys.length == 0) {
      setSelectedGroup(null);
      return;
    }
    setLoading(true);
    try {
      if (activeTab === "location") {
        const res = await getLocationDetail(selectedKeys[0]);
        setSelectedGroup(res);
      } else {
        const res = await getCustomerDetail(selectedKeys[0]);
        setSelectedGroup(res);
      }
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch location detail:", error);
    }
  };

  const handleTabChange = async (newTab) => {
    if (window.cancelToken) {
      window.cancelToken(); // 取消请求
    }
    setActiveTab(newTab);
    setTreeData(null);
    setSelectedGroup(null); // Clear selection when switching tabs
    if (newTab === "customer") {
      await fetchCustomerGroupsTree();
      if (customerType === null) {
        fetchCusCustomerType();
      }
    } else {
      getLocationTree();
    }
  };

  const handleEdit = async (node) => {
    // 如果当前是地理分组，并且行政分组树为空，则获取行政分组树
    if (activeTab === "location" && !administrativeGroupsTree) {
      // 获取行政分组树
      fetchAdministrativeGroupsTree();
    }

    // 获取当前节点详情
    const res =
      activeTab === "location"
        ? await getLocationDetail(node.key)
        : await getCustomerDetail(node.key);
    setCurrentNode(res);

    // 根据行政分组树，获取设备分组树
    if (activeTab === "location") {
      fetchDeviceLocationTree(res.groupId);
    }

    if (activeTab === "location") {
      form.setFieldsValue({
        groupId: res.groupId,
        locationGroupId: res.locationGroupId,
        description: res.description,
        locationType: res.locationType,
        environmentInfo: res.environmentInfo,
        installationInfo: res.installationInfo,
      });
    } else {
      let findItem = null;
      if (customerType) {
        findItem = customerType.find(
          (item) => item.itemCode === res.ownershipType
        );
      }
      form.setFieldsValue({
        parentId: res.parentId,
        ownershipName: res.ownershipName,
        ownershipType: findItem ? res.ownershipType : "-",
        contacts: res.contacts,
        contactNumber: res.contactNumber,
        assignmentNotes: res.assignmentNotes,
      });
    }
    setIsEditModalVisible(true);
  };

  const handleDelete = (node) => {
    setCurrentNode(node);
    setIsDeleteModalVisible(true);
  };

  // 新增按钮
  const handleAdd = () => {
    if (activeTab === "location" && !administrativeGroupsTree) {
      // 获取行政分组树
      fetchAdministrativeGroupsTree();
    }
    // 重置表单
    form.resetFields();

    // 为客户分组设置组织类型默认值
    if (activeTab === "customer" && customerType && customerType.length > 0) {
      form.setFieldsValue({
        ownershipType: customerType[0].itemCode
      });
    }

    setIsAddModalVisible(true);
  };

  const handleEditSubmit = async () => {
    try {
      if (activeTab === "location") {
        const values = await form.validateFields();
        const res = await updateDeviceLocation(currentNode.locationId, {
          ...values,
          lockVersion: currentNode.lockVersion,
        });
        if (res.locationId == currentNode.locationId) {
          // 修改的为当前选中节点，则刷新获取详情
          handleTreeSelect([currentNode.locationId]);
        }
        message.success("更新成功");
        setIsEditModalVisible(false);
        getLocationTree(); // Refresh tree data
      } else {
        const values = await form.validateFields();
        const res = await updateCustomerGroup(currentNode.ownershipId, {
          ...values,
          ownershipType: currentNode.ownershipType,
          lockVersion: currentNode.lockVersion,
        });
        if (res.ownershipId == currentNode.ownershipId) {
          // 修改的为当前选中节点，则刷新获取详情
          handleTreeSelect([currentNode.ownershipId]);
        }
        message.success("更新成功");
        setIsEditModalVisible(false);
        fetchCustomerGroupsTree(); // Refresh tree data
      }
      setCurrentNode(null);
    } catch (error) {
      console.error("Validation failed:", error);
    }
  };

  const handleDeleteConfirm = async () => {
    try {
      if (activeTab === "location") {
        await delDeviceLocation(currentNode.key);
        // 若删除的是当前选中的节点
        if (currentNode.key == selectedGroup?.locationId) {
          setSelectedGroup(null);
        }
        message.success("删除成功");
        setIsDeleteModalVisible(false);
        getLocationTree(); // Refresh tree data
      } else {
        await delCustomerGroup(currentNode.key);
        // 若删除的是当前选中的节点
        if (currentNode.key == selectedGroup?.ownershipId) {
          setSelectedGroup(null);
        }
        message.success("删除成功");
        setIsDeleteModalVisible(false);
        fetchCustomerGroupsTree(); // Refresh tree data
      }

      setCurrentNode(null);
    } catch (error) {
      console.error("Delete failed:", error);
    }
  };

  const handleAddSubmit = async () => {
    try {
      if (activeTab === "location") {
        const values = await form.validateFields();
        await addDeviceLocation(values);
        message.success("创建成功");
        setIsAddModalVisible(false);
        getLocationTree(); // Refresh tree data
      } else {
        const values = await form.validateFields();
        await createCustomerGroup(values);
        message.success("创建成功");
        setIsAddModalVisible(false);
        fetchCustomerGroupsTree(); // Refresh tree data
      }
      setCurrentNode(null);
    } catch (error) {
      console.error("Validation failed:", error);
    }
  };

  // 新增子节点
  const handleSubNode = async (nodeData) => {
    form.resetFields();
    const res =
      activeTab === "location"
        ? await getLocationDetail(nodeData.key)
        : await getCustomerDetail(nodeData.key);
    setCurrentNode(res);
    if (activeTab === "location") {
      form.setFieldsValue({
        groupId: res.groupId,
        locationGroupId: res.locationId,
      });
    } else {
      form.setFieldsValue({
        parentId: res.ownershipId,
      });
    }

    setIsAddModalVisible(true);
  };

  return (
    <div className="device-groups-container">
      {/* Top Function Area */}
      <div className="function-area">
        <div className="tab-area">
          <Tabs
            activeKey={activeTab}
            onChange={handleTabChange}
            items={[
              { key: "location", label: "地理分组" },
              { key: "customer", label: "客户分组" },
            ]}
          />
        </div>
      </div>

      <div className="device-groups-breadcrumb">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { title: "设备管理" },
            { title: "设备分组" },
            { title: activeTab === "location" ? "地理分组" : "客户分组" },
          ]}
          className="breadcrumb"
        />
        {/* <Button icon={<PlusOutlined />} size='small' onClick={handleAdd} /> */}
      </div>

      {/* Main Content Area */}
      <Layout className="main-content">
        <Sider width={500} className="tree-sider">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div style={{ width: "calc(100% - 100px)" }}>{node()}</div>
            <Button icon={<PlusOutlined />} onClick={handleAdd}>
              新建
            </Button>
          </div>

          {treeData && (
            <div className="device-group-tree-scroll">
              <Tree
                showIcon
                loading={loading}
                expandedKeys={expandedKeys}
                onExpand={(keys) => {
                  setExpandedKeys(keys);
                  setAutoExpandParent(false); // 用户手动展开后关闭自动展开
                }}
                treeData={searchValue ? filteredTreeData : treeData}
                titleRender={(nodeData) => (
                  <div className="device-groups-tree-node">
                    <span className="node-title">{nodeData.title}</span>
                    {nodeData.isLocal === 1 && (
                      <Space size="small">
                        <Dropdown
                          menu={{
                            items: [
                              {
                                key: "add",
                                icon: <PlusOutlined />,
                                label: "新建子节点",
                                onClick: async (e) => {
                                  e.domEvent.stopPropagation();
                                  handleSubNode(nodeData);
                                },
                              },
                              {
                                key: "edit",
                                icon: <EditOutlined />,
                                label: "编辑",
                                onClick: (e) => {
                                  e.domEvent.stopPropagation();
                                  handleEdit(nodeData);
                                },
                              },
                              {
                                key: "delete",
                                icon: <DeleteOutlined />,
                                label: "删除",
                                onClick: (e) => {
                                  e.domEvent.stopPropagation();
                                  handleDelete(nodeData);
                                },
                              },
                            ],
                          }}
                          trigger={["click"]}
                        >
                          <Button
                            type="text"
                            icon={<MoreOutlined />}
                            size="small"
                            onClick={(e) => e.stopPropagation()}
                          />
                        </Dropdown>
                      </Space>
                    )}
                  </div>
                )}
                autoExpandParent={autoExpandParent}
                onSelect={handleTreeSelect}
                blockNode
              />
            </div>
          )}
        </Sider>
        <Content className="detail-content">
          {activeTab === "location" ? (
            <LocationGroupDetail selectedGroup={selectedGroup} />
          ) : (
            <CustomerGroupDetail
              selectedGroup={selectedGroup}
              customerType={customerType}
            />
          )}
        </Content>
      </Layout>

      {/* Edit Modal */}
      <Modal
        title={
          activeTab === "location"
            ? `编辑地理分组-${currentNode?.description}`
            : `编辑客户分组-${currentNode?.ownershipName}`
        }
        open={isEditModalVisible}
        okText={"编辑"}
        onOk={handleEditSubmit}
        destroyOnClose={true}
        onCancel={() => {
          setIsEditModalVisible(false);
          setCurrentNode(null);
        }}
      >
        <Form form={form} layout="vertical">
          {activeTab === "location" ? (
            <>
              <Form.Item
                name="groupId"
                label="分组区域"
                rules={[{ required: true, message: "请选择分组区域" }]}
              >
                <TreeSelect
                  showSearch
                  treeData={administrativeGroupsTree}
                  placeholder="请选择分组区域"
                  treeNodeFilterProp="title"
                  showCheckedStrategy={TreeSelect.SHOW_CHILD}
                  treeDefaultExpandAll
                  allowClear
                  treeNodeLabelProp="title"
                  onChange={(value) => {
                    fetchDeviceLocationTree(value);
                    form.setFieldValue("locationGroupId", undefined);
                  }}
                />
              </Form.Item>
              <Form.Item name="locationGroupId" label="设备分组">
                <TreeSelect
                  showSearch
                  treeData={deviceLocationTree}
                  placeholder="请选择设备分组"
                  treeNodeFilterProp="title"
                  showCheckedStrategy={TreeSelect.SHOW_CHILD}
                  treeDefaultExpandAll
                  allowClear
                  treeNodeLabelProp="title"
                />
              </Form.Item>
              <Form.Item
                name="description"
                label="地理名称"
                rules={[{ required: true, message: "请输入地理名称" }]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                name="locationType"
                label="位置类型"
                rules={[{ required: true, message: "请选择位置类型" }]}
              >
                <Select>
                  <Select.Option value="indoor">室内</Select.Option>
                  <Select.Option value="outdoor">室外</Select.Option>
                  <Select.Option value="semi_outdoor">半室外</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item name="geoLocation" label="经纬度">
                <Input />
              </Form.Item>
              <Form.Item name="environmentInfo" label="环境信息">
                <Input.TextArea />
              </Form.Item>
              <Form.Item name="installationInfo" label="安装信息">
                <Input.TextArea />
              </Form.Item>
            </>
          ) : (
            <>
              <Form.Item name="parentId" label="上级单位">
                <TreeSelect
                  showSearch
                  treeData={treeData}
                  placeholder="请选择上级单位"
                  treeNodeFilterProp="title"
                  showCheckedStrategy={TreeSelect.SHOW_CHILD}
                  treeDefaultExpandAll
                  allowClear
                  treeNodeLabelProp="title"
                />
              </Form.Item>
              <Form.Item
                name="ownershipName"
                label="客户名称"
                rules={[{ required: true, message: "请输入客户名称" }]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                name="ownershipType"
                label="组织类型"
                rules={[{ required: true, message: "请选择组织类型" }]}
              >
                <Select>
                  {customerType
                    ? customerType.map((item) => (
                        <Select.Option
                          value={item.itemCode}
                          key={item.itemCode}
                        >
                          {item.itemName}
                        </Select.Option>
                      ))
                    : ""}

                  {/* {form.getFieldValue('ownershipType')?.map(scene => {
                    const exists = customerType.some(
                      option => option.itemCode === scene
                    )
                    if (!exists) {
                      return (
                        <Select.Option value={scene} key={scene}>
                          -
                        </Select.Option>
                      )
                    }
                    return null
                  })} */}
                </Select>
              </Form.Item>
              <Form.Item
                name="contacts"
                label="联系人"
                rules={[{ required: true, message: "请输入联系人" }]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                name="contactNumber"
                label="联系电话"
                rules={[
                  { required: true, message: "请输入联系电话" },
                  { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码" },
                ]}
              >
                <Input />
              </Form.Item>
              <Form.Item name="assignmentNotes" label="备注">
                <Input.TextArea />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>

      {/* Delete Modal */}
      <Modal
        title="删除分组"
        open={isDeleteModalVisible}
        onOk={handleDeleteConfirm}
        onCancel={() => {
          setIsDeleteModalVisible(false);
          setCurrentNode(null);
        }}
      >
        <p>确定要删除分组 "{currentNode?.title}" 吗？此操作不可恢复。</p>
      </Modal>

      {/* Add Modal */}
      <Modal
        title={
          activeTab === "location"
            ? `新建地理分组${currentNode ? "-" + currentNode.description : ""}`
            : `新建客户分组${
                currentNode ? "-" + currentNode.ownershipName : ""
              }`
        }
        okText={"新建"}
        open={isAddModalVisible}
        onOk={handleAddSubmit}
        destroyOnClose={true}
        centered
        onCancel={() => {
          setIsAddModalVisible(false);
          setCurrentNode(null);
        }}
      >
        <Form form={form} layout="vertical">
          {activeTab === "location" ? (
            <>
              <Form.Item
                name="groupId"
                label="分组区域"
                hidden={currentNode}
                rules={[{ required: true, message: "请选择分组区域" }]}
              >
                <TreeSelect
                  showSearch
                  treeData={administrativeGroupsTree}
                  placeholder="请选择分组区域"
                  treeNodeFilterProp="title"
                  showCheckedStrategy={TreeSelect.SHOW_CHILD}
                  treeDefaultExpandAll
                  allowClear
                  treeNodeLabelProp="title"
                  onChange={(value) => {
                    fetchDeviceLocationTree(value);
                    form.setFieldValue("locationGroupId", undefined);
                  }}
                />
              </Form.Item>
              <Form.Item
                name="locationGroupId"
                label="设备分组"
                hidden={currentNode}
              >
                <TreeSelect
                  showSearch
                  treeData={deviceLocationTree}
                  placeholder="请选择设备分组"
                  treeNodeFilterProp="title"
                  showCheckedStrategy={TreeSelect.SHOW_CHILD}
                  treeDefaultExpandAll
                  allowClear
                  treeNodeLabelProp="title"
                />
              </Form.Item>
              <Form.Item
                name="description"
                label="地理名称"
                rules={[{ required: true, message: "请输入地理名称" }]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                name="locationType"
                label="位置类型"
                rules={[{ required: true, message: "请选择位置类型" }]}
              >
                <Select>
                  <Select.Option value="indoor">室内</Select.Option>
                  <Select.Option value="outdoor">室外</Select.Option>
                  <Select.Option value="semi_outdoor">半室外</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item name="geoLocation" label="经纬度">
                <Input />
              </Form.Item>
              <Form.Item name="environmentInfo" label="环境信息">
                <Input.TextArea />
              </Form.Item>
              <Form.Item name="installationInfo" label="安装信息">
                <Input.TextArea />
              </Form.Item>
            </>
          ) : (
            <>
              <Form.Item name="parentId" label="上级单位" hidden={currentNode}>
                <TreeSelect
                  showSearch
                  treeData={treeData}
                  placeholder="请选择上级单位"
                  treeNodeFilterProp="title"
                  showCheckedStrategy={TreeSelect.SHOW_CHILD}
                  treeDefaultExpandAll
                  allowClear
                  treeNodeLabelProp="title"
                />
              </Form.Item>
              <Form.Item
                name="ownershipName"
                label="客户名称"
                rules={[{ required: true, message: "请输入客户名称" }]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                name="ownershipType"
                label="组织类型"
                rules={[{ required: true, message: "请选择组织类型" }]}
              >
                {/* {console.log('resss',customerType[0].itemName)} */}
                <Select>
                  {customerType
                    ? customerType.map((item) => (
                        <Select.Option
                          value={item.itemCode}
                          key={item.itemCode}
                          
                        >
                          {item.itemName}
                        </Select.Option>
                      ))
                    : ""}
                </Select>
              </Form.Item>
              <Form.Item
                name="contacts"
                label="联系人"
                rules={[{ message: "请输入联系人" }]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                name="contactNumber"
                label="联系电话"
                rules={[
                  { message: "请输入联系电话" },
                  { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码" },
                ]}
              >
                <Input />
              </Form.Item>
              <Form.Item name="assignmentNotes" label="备注">
                <Input.TextArea />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default DeviceGroups;
