import { useState } from 'react'
import {
  Modal,
  Select,
  Input,
  Button,
  Table,
  message
} from 'antd'
import PropTypes from 'prop-types'

export default function AdvancedSearch ({
  data = [],
  onCancel,
  advancedSearchVisible,
  getSearchData
}) {
  const [selectedField, setSelectedField] = useState()
  const [inputValue, setInputValue] = useState('')
  const [tableData, setTableData] = useState([])
  const [nodeFun, setNodeFun] = useState(null)

  // 新增
  const handleAdd = () => {
    if (!selectedField) {
      message.warning('请选择字段')
      return
    }
    if (inputValue === "" ) {
      message.warning('请输入内容')
      return
    }
    // 检查是否已存在，如果存在则更新值
    const existingIndex = tableData.findIndex(item => item.field === selectedField)
    if (existingIndex !== -1) {
      const newTableData = [...tableData]
      newTableData[existingIndex] = {
        ...newTableData[existingIndex],
        value: inputValue
      }
      setTableData(newTableData)
    } else {
      setTableData([
        ...tableData,
        {
          key: selectedField,
          field: selectedField,
          value: inputValue
        }
      ])
    }
    setInputValue('')
    setSelectedField(undefined)
    setNodeFun(null)
  }

  // 删除
  const handleDelete = (key) => {
    setTableData(tableData.filter(item => item.key !== key))
  }

  // 搜索
  const handleSearch = () => {
    if (getSearchData) {
      getSearchData(tableData)
    }
  }

  const columns = [
    {
      title: '字段名',
      dataIndex: 'field',
      key: 'field',
      render: (text) => {
        const field = data.find(item => item.value === text)
        return field ? field.label : text
      }
    },
    {
      title: '字段值',
      dataIndex: 'value',
      key: 'value',
      render: (text, record) => {
        const field = data.find(item => item.value === record.field);
        if (field?.options) {
          const option = field.options.find(opt => opt.value === text);
          return option ? option.label : text;
        }
        return text;
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button danger size="small" onClick={() => handleDelete(record.key)}>
          删除
        </Button>
      ),
    },
  ]

  return (
    <Modal
      title='高级搜索'
      open={advancedSearchVisible}
      onCancel={onCancel}
      width={1000}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="search" type="primary" onClick={handleSearch}>
          搜索
        </Button>
      ]}
    >
      <div style={{ display: 'flex', gap: 16, marginBottom: 16 }}>
        <Select
          style={{ width: 300 }}
          placeholder='请选择'
          value={selectedField}
          onChange={(value) => {
            setSelectedField(value);
            const selectedItem = data.find(item => item.value === value);
            if (selectedItem?.children) {
              setNodeFun(selectedItem.children(setInputValue));
            } else {
              setNodeFun(null);
              setInputValue(''); // 清空输入值
            }
          }}
          options={data.map(item => ({ label: item.label, value: item.value }))}
          allowClear
        />
        {!nodeFun ? (
          <Input
            style={{ width: 300 }}
            placeholder='请输入查询内容'
            value={inputValue}
            onChange={e => setInputValue(e.target.value)}
          />
        ) : nodeFun}
        <Button type='primary' onClick={handleAdd} style={{ width: 100 }}>
          新增
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={tableData}
        pagination={false}
        locale={{ emptyText: '暂无数据' }}
        style={{ marginTop: 16 }}
      />
    </Modal>
  )
}

AdvancedSearch.propTypes = {
  advancedSearchVisible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  data: PropTypes.array,
  getSearchData: PropTypes.func
}