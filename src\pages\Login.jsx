import { useState, useRef, useEffect } from 'react'
import { observer } from 'mobx-react-lite'
import { Form, Input, Button, message, Space, Tabs, Modal } from 'antd'
import {
  UserOutlined,
  LockOutlined,
  SafetyCertificateOutlined,
  MobileOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { loginApi } from '../service/login'
import { layoutApi } from '../service/layout'
import { handleLoginResponse, clearStoredTokens } from '../utils/loginResponseHandler'
import { saveLoginResponse } from '../utils/loginLogger'

const Login = observer(() => {
  const [form] = Form.useForm()
  const [registerForm] = Form.useForm()
  const navigate = useNavigate()
  const [captcha, setCaptcha] = useState('')
  const captchaRef = useRef(null)
  const [messageApi, contextHolder] = message.useMessage()
  const [loginType, setLoginType] = useState('acount')
  const [smsCountdown, setSmsCountdown] = useState(0)
  const countdownTimer = useRef(null)
  const [registerModalVisible, setRegisterModalVisible] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  

  // 组件挂载时清空所有token和用户数据
  useEffect(() => {
    clearStoredTokens()
  }, [])

  useEffect(() => {
    // 切换登录类型时重新生成验证码
    if (loginType === 'acount') {
      generateCaptcha()
    }
  }, [loginType])

  useEffect(() => {
    return () => {
      if (countdownTimer.current) {
        clearInterval(countdownTimer.current)
      }
    }
  }, [])

  const generateCaptcha = async () => {
    try {
      const response = await loginApi.getCaptcha()
      if (response && response.code === 200) {
        setCaptcha(response.data.captchaText)

        const captchaImage = response.data.captchaImage
        if (!captchaImage) return

        const canvas = captchaRef.current
        if (!canvas) return

        const ctx = canvas.getContext('2d')
        ctx.fillStyle = '#f0f2f5'
        ctx.fillRect(0, 0, canvas.width, canvas.height)

        const img = new Image()
        img.onload = () => ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        img.onerror = () => {
          ctx.fillStyle = '#f0f2f5'
          ctx.fillRect(0, 0, canvas.width, canvas.height)
          ctx.fillStyle = '#ff4d4f'
          ctx.font = '14px Arial'
          ctx.textAlign = 'center'
          ctx.textBaseline = 'middle'
          ctx.fillText('验证码加载失败', canvas.width / 2, canvas.height / 2)
        }
        img.src = captchaImage
      } else {
        message.error('获取验证码失败，请刷新重试')
      }
    } catch (error) {
      message.error('获取验证码失败，请刷新重试')
    }
  }

  const startSmsCountdown = () => {
    setSmsCountdown(60)
    countdownTimer.current = setInterval(() => {
      setSmsCountdown(prev => {
        if (prev <= 1) {
          clearInterval(countdownTimer.current)
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }

  const handleSendSms = async () => {
    try {
      await form.validateFields(['phone'])
      const phone = form.getFieldValue('phone')
      message.success(`验证码已发送至 ${phone}`)
      startSmsCountdown()
    } catch (error) {
      message.error('请输入正确的手机号码')
    }
  }

  const navigateAfterLogin = (redirectPath = '/') => {
    localStorage.removeItem('lastPage')
    navigate(redirectPath)
  }

  const onFinish = async values => {
    if (loginType === 'acount') {
      if (!values.captcha || values.captcha.length !== 4) {
        form.setFieldValue('captcha', '')
        messageApi.error('请输入4位验证码！')
        generateCaptcha()
        return
      }

      try {
        setIsLoading(true)
        const response = await loginApi.login(
          // values.acount + "@seadee.com.cn",
          values.acount ,
          values.password,
          captcha,
          values.captcha
        )
        saveLoginResponse(response.data)

        const loginResult = handleLoginResponse(response)
        const userMenu = await layoutApi.getUserMenu()
        if (userMenu?.data?.length == 0) {
          throw new Error(loginResult.error || '登录失败，该用户没有权限！')
        }

        if (loginResult.success) {
          localStorage.setItem('token', loginResult.data)
          navigateAfterLogin('/')
        } else {
          throw new Error(loginResult.error || '登录失败，请检查输入！')
        }
      } catch (error) {
        messageApi.error(error.message || '登录失败，请检查输入！')
        generateCaptcha()
        form.setFieldValue('captcha', '')
      } finally {
        setIsLoading(false)
      }
    } else {
      try {
        setIsLoading(true)
        const response = await loginApi.loginWithPhone(
          values.phone,
          values.smsCode
        )

        if (response && response.code === 200) {
          const loginResult = handleLoginResponse(response)
          if (loginResult.success) {
            localStorage.setItem('token', loginResult.data)
            messageApi.success('登录成功！')

            const lastPage = localStorage.getItem('lastPage')
            navigateAfterLogin(
              lastPage && lastPage !== '/login' ? lastPage : '/'
            )
          } else {
            throw new Error(loginResult.error || '登录失败，请检查验证码！')
          }
        } else {
          throw new Error(response?.message || '登录失败，请检查验证码！')
        }
      } catch (error) {
        messageApi.error(error.message || '登录失败，请检查验证码！')
      } finally {
        setIsLoading(false)
      }
    }
  }

  const handleRegister = async () => {
    try {
      const values = await registerForm.validateFields()

      const response = await loginApi.register(values.acount, values.password)

      if (response && response.code === 200) {
        messageApi.success('注册成功！')
        setRegisterModalVisible(false)
        registerForm.resetFields()
      } else {
        messageApi.error(response.message || '注册失败，请稍后重试！')
      }
    } catch (error) {
      messageApi.error('注册失败：' + (error.message || '未知错误'))
    }
  }

  const items = [
    {
      key: 'acount',
      label: '账号登录',
      children: (
        <Form
          form={form}
          name='login-acount'
          onFinish={onFinish}
          autoComplete='off'
          size='large'
        >
          <Form.Item
            name='acount'
            rules={[
              { required: true, message: '请输入邮箱/用户名/手机号！！' },
              {
                type: 'acount',
                message: '请输入有效的请输入邮箱/用户名/手机号！！'
              }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder='请输入邮箱/用户名/手机号！'
              onPressEnter={() => form.getFieldInstance('password').focus()}
              
            />
          </Form.Item>

          <Form.Item
            name='password'
            rules={[
              { required: true, message: '请输入密码！' },
              { min: 6, message: '密码长度不能小于6位！' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder='密码'
              onPressEnter={() => form.getFieldInstance('captcha').focus()}
            />
          </Form.Item>

          <Form.Item
            name='captcha'
            rules={[
              { required: true, message: '请输入验证码！' },
              { len: 4, message: '验证码必须是4位！' }
            ]}
          >
            <Space>
              <Input
                prefix={<SafetyCertificateOutlined />}
                placeholder='验证码'
                style={{ width: '200px' }}
              />
              <canvas
                ref={captchaRef}
                width='140'
                height='40'
                onClick={generateCaptcha}
                style={{ cursor: 'pointer' }}
              />
            </Space>
          </Form.Item>

          <Form.Item>
            <Button type='primary' htmlType='submit' block loading={isLoading}>
              登录
            </Button>
          </Form.Item>

          {/* <Form.Item>
            <Button
              type="default"
              block
              onClick={() => setRegisterModalVisible(true)}
            >
              注册账号
            </Button>
          </Form.Item> */}
        </Form>
      )
    }
    // {
    //   key: 'phone',
    //   label: '短信登录',
    //   children: (
    //     <Form
    //       form={form}
    //       name="login-phone"
    //       onFinish={onFinish}
    //       autoComplete="off"
    //       size="large"
    //     >
    //       <Form.Item
    //         name="phone"
    //         rules={[
    //           { required: true, message: '请输入手机号！' },
    //           { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号！' }
    //         ]}
    //       >
    //         <Input
    //           prefix={<MobileOutlined />}
    //           placeholder="手机号"
    //           maxLength={11}
    //         />
    //       </Form.Item>

    //       <Form.Item
    //         name="smsCode"
    //         rules={[
    //           { required: true, message: '请输入验证码！' },
    //           { pattern: /^\d{6}$/, message: '请输入6位数字验证码！' }
    //         ]}
    //       >
    //         <Space>
    //           <Input
    //             prefix={<SafetyCertificateOutlined />}
    //             placeholder="验证码"
    //             style={{ width: '200px' }}
    //             maxLength={6}
    //           />
    //           <Button
    //             type="primary"
    //             onClick={handleSendSms}
    //             disabled={smsCountdown > 0}
    //           >
    //             {smsCountdown > 0 ? `${smsCountdown}秒后重试` : '获取验证码'}
    //           </Button>
    //         </Space>
    //       </Form.Item>

    //       <Form.Item>
    //         <Button
    //           type="primary"
    //           htmlType="submit"
    //           block
    //           loading={isLoading}
    //         >
    //           登录
    //         </Button>
    //       </Form.Item>

    //       <Form.Item>
    //         <Button
    //           type="default"
    //           block
    //           onClick={() => setRegisterModalVisible(true)}
    //         >
    //           注册账号
    //         </Button>
    //       </Form.Item>
    //     </Form>
    //   )
    // }
  ]

  const accountDom = () => (
    <Form
      form={form}
      name='login-acount'
      onFinish={onFinish}
      autoComplete='off'
      size='large'
    >
      <Form.Item
        name='acount'
        rules={[
          { required: true, message: '请输入邮箱/用户名/手机号！！' },
          {
            type: 'acount',
            message: '请输入有效的请输入邮箱/用户名/手机号！！'
          }
        ]}
      >
        <Input
          // prefix={<UserOutlined />}
          style={{ height: 48 }}
          placeholder='请输入邮箱/用户名/手机号！'
          onPressEnter={() => form.getFieldInstance('password').focus()}
          // addonAfter='@seadee.com.cn'
        />
      </Form.Item>

      <Form.Item
        name='password'
        rules={[
          { required: true, message: '请输入密码！' },
          { min: 6, message: '密码长度不能小于6位！' }
        ]}
      >
        <Input.Password
          style={{ height: 48 }}
          // prefix={<LockOutlined />}
          placeholder='密码'
          onPressEnter={() => form.getFieldInstance('captcha').focus()}
        />
      </Form.Item>

      <Form.Item
        name='captcha'
        rules={[
          { required: true, message: '请输入验证码！' },
          { len: 4, message: '验证码必须是4位！' }
        ]}
      >
        <Space>
          <Input
            // prefix={<SafetyCertificateOutlined />}
            placeholder='验证码'
            style={{ width: '180px', height: 48 }}
          />
          <canvas
            ref={captchaRef}
            width='130'
            height='48'
            onClick={generateCaptcha}
            style={{ cursor: 'pointer',borderRadius: 5,marginTop:7 }}
          />
        </Space>
      </Form.Item>

      <Form.Item>
        <Button
          type='primary'
          htmlType='submit'
          block
          loading={isLoading}
          className='login_btn'
        >
          登录
        </Button>
      </Form.Item>

      {/* <Form.Item>
    <Button
      type="default"
      block
      onClick={() => setRegisterModalVisible(true)}
    >
      注册账号
    </Button>
  </Form.Item> */}
    </Form>
  )

  const phoneDom = () => <div>无</div>
  return (
    <>
      <div className='login-container'>
        {contextHolder}
        <div className='login-box'>
          <div className='login-header'>
            {/* <img src='/qsq.png' alt='Logo' className='login-logo' /> */}
            <div>生物驱离系统</div>
            <div>欢迎登录</div>
          </div>

          <div className='login-content'>
            {/* <div className='login-tab-switch'>
              <div
                className={
                  loginType === 'acount' ? 'tab-active' : 'tab-inactive'
                }
                onClick={() => setLoginType('acount')}
              >
                账号登录
              </div>
              <div
                className={
                  loginType === 'phone' ? 'tab-active' : 'tab-inactive'
                }
                onClick={() => setLoginType('phone')}
              >
                短信登录
              </div>
            </div> */}
            {loginType === 'acount' ? accountDom() : phoneDom()}
          </div>

          {/* <Tabs
          activeKey={loginType}
          onChange={setLoginType}
          centered
          items={items}
        /> */}
        </div>

        <Modal
          title='注册新账号'
          open={registerModalVisible}
          onCancel={() => {
            setRegisterModalVisible(false)
            registerForm.resetFields()
          }}
          centered
          footer={[
            <Button
              key='back'
              onClick={() => {
                setRegisterModalVisible(false)
                registerForm.resetFields()
              }}
            >
              取消
            </Button>,
            <Button key='submit' type='primary' onClick={handleRegister}>
              注册
            </Button>
          ]}
        >
          <Form form={registerForm} layout='vertical' name='register_form'>
            <Form.Item
              name='acount'
              label='邮箱'
              rules={[
                { required: true, message: '请输入邮箱！' },
                { type: 'acount', message: '请输入有效的邮箱地址！' }
              ]}
            >
              <Input prefix={<UserOutlined />} placeholder='请输入邮箱' />
            </Form.Item>
            <Form.Item
              name='password'
              label='密码'
              rules={[
                { required: true, message: '请输入密码！' },
                { min: 6, message: '密码长度不能小于6位！' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder='请输入密码'
              />
            </Form.Item>
            <Form.Item
              name='confirmPassword'
              label='确认密码'
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码！' },
                ({ getFieldValue }) => ({
                  validator (_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve()
                    }
                    return Promise.reject(new Error('两次输入的密码不一致！'))
                  }
                })
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder='请确认密码'
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
      <div className='beian-fixed'>
        <a
          href='https://beian.miit.gov.cn'
          target='_blank'
          style={{ color: '#4D4D4D', textDecoration: 'none' }}
        >
          蜀ICP备2022004311号-2
        </a>
      </div>
    </>
  )
})

export default Login
