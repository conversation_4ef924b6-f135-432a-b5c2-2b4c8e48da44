import fetch from './fetch';
import {API_PREFIX} from "./constant"

// 分页获取摄像头列表
export const getCameraList = (params) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/cameras`,
    method: 'GET',
    params
  })
}

// 获取摄像头详情
export const getCameraDetail = (cameraId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/cameras/${cameraId}`,
    method: 'GET'
  })
}

// 创建摄像头
export const createCamera = (data) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/cameras`,
    method: 'POST',
    data
  })
}

// 更新摄像头
export const updateCamera = (data) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/cameras`,
    method: 'PUT',
    data
  })
}

// 单个删除摄像头
export const deleteCamera = (cameraId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/cameras/${cameraId}`,
    method: 'DELETE'
  })
} 

// 批量删除摄像头
export const deleteCamerabyIds = (cameraIds) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/cameras/deleteByIds`,
    method: 'post',
    data: cameraIds
  })
}

// 绑定驱鼠器
export const bindDevice = ({cameraId,deviceId}) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/cameras/bindingDevices`,
    method: 'post',
    data:{
      cameraId:cameraId,
      deviceIds: [deviceId]
    }
  })
}


// 模拟鼠患触发
export const simulateRatEvent = (cameraId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/cameras/simulateRatEvent`,
    method: 'get',
    params:{
      cameraId:cameraId,
    }
  })
}

//模糊查询摄像头
export const getCameraLikeCameraNo = (cameraNo) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/cameras/getCameraLikeCameraNo`,
    method: 'get',
    params:{
      cameraNo:cameraNo
    }
  })
}
