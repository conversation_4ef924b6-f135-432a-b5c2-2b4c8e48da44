import React from 'react'
import { Card, Row, Col, Progress, Statistic, List, Space, Tag } from 'antd'

const Alerts = ({ unresolvedAlarms, levelStats, addToday, totalAlarms }) => {

  const alertsColor = {
    0: 'blue',
    1: 'orange',
    2: 'gold',
    3: 'red'
  }

  const alarmLevelMap = {
    0: '低',
    1: '中',
    2: '高',
    3: '紧急'
  }
  return (
    <div className='alerts-content'>
      {/* Alert Statistics */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title='总告警数'
              value={totalAlarms}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title='未解决告警数'
              value={unresolvedAlarms}
              valueStyle={{ color: 'orange' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic title='当日新增告警数' value={addToday} />
          </Card>
        </Col>
        {/* <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="告警处理率"
              value={80}
              suffix="%"
            />
          </Card>
        </Col> */}
      </Row>

      {/* Alert Types Analysis */}
      <Card title='告警分析' style={{ marginTop: '16px' }}>
        <List
          dataSource={levelStats}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta title={item.title} description={item.content} />
              <div>
                <Tag color={alertsColor[item.alarmLevel]}>
                  {alarmLevelMap[item.alarmLevel]}
                </Tag>
              </div>
            </List.Item>
          )}
        />
      </Card>
    </div>
  )
}

export default Alerts
