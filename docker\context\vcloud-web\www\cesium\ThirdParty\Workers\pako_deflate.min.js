/*! pako 2.1.0 https://github.com/nodeca/pako @license (MIT AND Zlib) */(function(v,z){typeof exports=="object"&&typeof module<"u"?z(exports):typeof define=="function"&&define.amd?define(["exports"],z):z((v=typeof globalThis<"u"?globalThis:v||self).pako={})})(this,function(v){"use strict";function z(e){let s=e.length;for(;--s>=0;)e[s]=0}const oe=256,Ae=286,H=30,M=15,de=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),ae=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),ra=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),Ee=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Z=new Array(576);z(Z);const P=new Array(60);z(P);const j=new Array(512);z(j);const K=new Array(256);z(K);const ue=new Array(29);z(ue);const se=new Array(H);function fe(e,s,a,r,n){this.static_tree=e,this.extra_bits=s,this.extra_base=a,this.elems=r,this.max_length=n,this.has_stree=e&&e.length}let Ze,Ue,Re;function ce(e,s){this.dyn_tree=e,this.max_code=0,this.stat_desc=s}z(se);const Se=e=>e<256?j[e]:j[256+(e>>>7)],Y=(e,s)=>{e.pending_buf[e.pending++]=255&s,e.pending_buf[e.pending++]=s>>>8&255},p=(e,s,a)=>{e.bi_valid>16-a?(e.bi_buf|=s<<e.bi_valid&65535,Y(e,e.bi_buf),e.bi_buf=s>>16-e.bi_valid,e.bi_valid+=a-16):(e.bi_buf|=s<<e.bi_valid&65535,e.bi_valid+=a)},k=(e,s,a)=>{p(e,a[2*s],a[2*s+1])},Te=(e,s)=>{let a=0;do a|=1&e,e>>>=1,a<<=1;while(--s>0);return a>>>1},Le=(e,s,a)=>{const r=new Array(16);let n,i,_=0;for(n=1;n<=M;n++)_=_+a[n-1]<<1,r[n]=_;for(i=0;i<=s;i++){let t=e[2*i+1];t!==0&&(e[2*i]=Te(r[t]++,t))}},Fe=e=>{let s;for(s=0;s<Ae;s++)e.dyn_ltree[2*s]=0;for(s=0;s<H;s++)e.dyn_dtree[2*s]=0;for(s=0;s<19;s++)e.bl_tree[2*s]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.sym_next=e.matches=0},Oe=e=>{e.bi_valid>8?Y(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0},De=(e,s,a,r)=>{const n=2*s,i=2*a;return e[n]<e[i]||e[n]===e[i]&&r[s]<=r[a]},pe=(e,s,a)=>{const r=e.heap[a];let n=a<<1;for(;n<=e.heap_len&&(n<e.heap_len&&De(s,e.heap[n+1],e.heap[n],e.depth)&&n++,!De(s,r,e.heap[n],e.depth));)e.heap[a]=e.heap[n],a=n,n<<=1;e.heap[a]=r},Ne=(e,s,a)=>{let r,n,i,_,t=0;if(e.sym_next!==0)do r=255&e.pending_buf[e.sym_buf+t++],r+=(255&e.pending_buf[e.sym_buf+t++])<<8,n=e.pending_buf[e.sym_buf+t++],r===0?k(e,n,s):(i=K[n],k(e,i+oe+1,s),_=de[i],_!==0&&(n-=ue[i],p(e,n,_)),r--,i=Se(r),k(e,i,a),_=ae[i],_!==0&&(r-=se[i],p(e,r,_)));while(t<e.sym_next);k(e,256,s)},ge=(e,s)=>{const a=s.dyn_tree,r=s.stat_desc.static_tree,n=s.stat_desc.has_stree,i=s.stat_desc.elems;let _,t,h,l=-1;for(e.heap_len=0,e.heap_max=573,_=0;_<i;_++)a[2*_]!==0?(e.heap[++e.heap_len]=l=_,e.depth[_]=0):a[2*_+1]=0;for(;e.heap_len<2;)h=e.heap[++e.heap_len]=l<2?++l:0,a[2*h]=1,e.depth[h]=0,e.opt_len--,n&&(e.static_len-=r[2*h+1]);for(s.max_code=l,_=e.heap_len>>1;_>=1;_--)pe(e,a,_);h=i;do _=e.heap[1],e.heap[1]=e.heap[e.heap_len--],pe(e,a,1),t=e.heap[1],e.heap[--e.heap_max]=_,e.heap[--e.heap_max]=t,a[2*h]=a[2*_]+a[2*t],e.depth[h]=(e.depth[_]>=e.depth[t]?e.depth[_]:e.depth[t])+1,a[2*_+1]=a[2*t+1]=h,e.heap[1]=h++,pe(e,a,1);while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],((u,d)=>{const m=d.dyn_tree,V=d.max_code,_e=d.stat_desc.static_tree,Na=d.stat_desc.has_stree,Ia=d.stat_desc.extra_bits,na=d.stat_desc.extra_base,$=d.stat_desc.max_length;let ee,y,B,f,le,xe,he=0;for(f=0;f<=M;f++)u.bl_count[f]=0;for(m[2*u.heap[u.heap_max]+1]=0,ee=u.heap_max+1;ee<573;ee++)y=u.heap[ee],f=m[2*m[2*y+1]+1]+1,f>$&&(f=$,he++),m[2*y+1]=f,y>V||(u.bl_count[f]++,le=0,y>=na&&(le=Ia[y-na]),xe=m[2*y],u.opt_len+=xe*(f+le),Na&&(u.static_len+=xe*(_e[2*y+1]+le)));if(he!==0){do{for(f=$-1;u.bl_count[f]===0;)f--;u.bl_count[f]--,u.bl_count[f+1]+=2,u.bl_count[$]--,he-=2}while(he>0);for(f=$;f!==0;f--)for(y=u.bl_count[f];y!==0;)B=u.heap[--ee],B>V||(m[2*B+1]!==f&&(u.opt_len+=(f-m[2*B+1])*m[2*B],m[2*B+1]=f),y--)}})(e,s),Le(a,l,e.bl_count)},Ie=(e,s,a)=>{let r,n,i=-1,_=s[1],t=0,h=7,l=4;for(_===0&&(h=138,l=3),s[2*(a+1)+1]=65535,r=0;r<=a;r++)n=_,_=s[2*(r+1)+1],++t<h&&n===_||(t<l?e.bl_tree[2*n]+=t:n!==0?(n!==i&&e.bl_tree[2*n]++,e.bl_tree[32]++):t<=10?e.bl_tree[34]++:e.bl_tree[36]++,t=0,i=n,_===0?(h=138,l=3):n===_?(h=6,l=3):(h=7,l=4))},Ce=(e,s,a)=>{let r,n,i=-1,_=s[1],t=0,h=7,l=4;for(_===0&&(h=138,l=3),r=0;r<=a;r++)if(n=_,_=s[2*(r+1)+1],!(++t<h&&n===_)){if(t<l)do k(e,n,e.bl_tree);while(--t!=0);else n!==0?(n!==i&&(k(e,n,e.bl_tree),t--),k(e,16,e.bl_tree),p(e,t-3,2)):t<=10?(k(e,17,e.bl_tree),p(e,t-3,3)):(k(e,18,e.bl_tree),p(e,t-11,7));t=0,i=n,_===0?(h=138,l=3):n===_?(h=6,l=3):(h=7,l=4)}};let Be=!1;const He=(e,s,a,r)=>{p(e,0+(r?1:0),3),Oe(e),Y(e,a),Y(e,~a),a&&e.pending_buf.set(e.window.subarray(s,s+a),e.pending),e.pending+=a};var ia=(e,s,a,r)=>{let n,i,_=0;e.level>0?(e.strm.data_type===2&&(e.strm.data_type=(t=>{let h,l=4093624447;for(h=0;h<=31;h++,l>>>=1)if(1&l&&t.dyn_ltree[2*h]!==0)return 0;if(t.dyn_ltree[18]!==0||t.dyn_ltree[20]!==0||t.dyn_ltree[26]!==0)return 1;for(h=32;h<oe;h++)if(t.dyn_ltree[2*h]!==0)return 1;return 0})(e)),ge(e,e.l_desc),ge(e,e.d_desc),_=(t=>{let h;for(Ie(t,t.dyn_ltree,t.l_desc.max_code),Ie(t,t.dyn_dtree,t.d_desc.max_code),ge(t,t.bl_desc),h=18;h>=3&&t.bl_tree[2*Ee[h]+1]===0;h--);return t.opt_len+=3*(h+1)+5+5+4,h})(e),n=e.opt_len+3+7>>>3,i=e.static_len+3+7>>>3,i<=n&&(n=i)):n=i=a+5,a+4<=n&&s!==-1?He(e,s,a,r):e.strategy===4||i===n?(p(e,2+(r?1:0),3),Ne(e,Z,P)):(p(e,4+(r?1:0),3),((t,h,l,u)=>{let d;for(p(t,h-257,5),p(t,l-1,5),p(t,u-4,4),d=0;d<u;d++)p(t,t.bl_tree[2*Ee[d]+1],3);Ce(t,t.dyn_ltree,h-1),Ce(t,t.dyn_dtree,l-1)})(e,e.l_desc.max_code+1,e.d_desc.max_code+1,_+1),Ne(e,e.dyn_ltree,e.dyn_dtree)),Fe(e),r&&Oe(e)},ta={_tr_init:e=>{Be||((()=>{let s,a,r,n,i;const _=new Array(16);for(r=0,n=0;n<28;n++)for(ue[n]=r,s=0;s<1<<de[n];s++)K[r++]=n;for(K[r-1]=n,i=0,n=0;n<16;n++)for(se[n]=i,s=0;s<1<<ae[n];s++)j[i++]=n;for(i>>=7;n<H;n++)for(se[n]=i<<7,s=0;s<1<<ae[n]-7;s++)j[256+i++]=n;for(a=0;a<=M;a++)_[a]=0;for(s=0;s<=143;)Z[2*s+1]=8,s++,_[8]++;for(;s<=255;)Z[2*s+1]=9,s++,_[9]++;for(;s<=279;)Z[2*s+1]=7,s++,_[7]++;for(;s<=287;)Z[2*s+1]=8,s++,_[8]++;for(Le(Z,287,_),s=0;s<H;s++)P[2*s+1]=5,P[2*s]=Te(s,5);Ze=new fe(Z,de,257,Ae,M),Ue=new fe(P,ae,0,H,M),Re=new fe(new Array(0),ra,0,19,7)})(),Be=!0),e.l_desc=new ce(e.dyn_ltree,Ze),e.d_desc=new ce(e.dyn_dtree,Ue),e.bl_desc=new ce(e.bl_tree,Re),e.bi_buf=0,e.bi_valid=0,Fe(e)},_tr_stored_block:He,_tr_flush_block:ia,_tr_tally:(e,s,a)=>(e.pending_buf[e.sym_buf+e.sym_next++]=s,e.pending_buf[e.sym_buf+e.sym_next++]=s>>8,e.pending_buf[e.sym_buf+e.sym_next++]=a,s===0?e.dyn_ltree[2*a]++:(e.matches++,s--,e.dyn_ltree[2*(K[a]+oe+1)]++,e.dyn_dtree[2*Se(s)]++),e.sym_next===e.sym_end),_tr_align:e=>{p(e,2,3),k(e,256,Z),(s=>{s.bi_valid===16?(Y(s,s.bi_buf),s.bi_buf=0,s.bi_valid=0):s.bi_valid>=8&&(s.pending_buf[s.pending++]=255&s.bi_buf,s.bi_buf>>=8,s.bi_valid-=8)})(e)}},Me=(e,s,a,r)=>{let n=65535&e|0,i=e>>>16&65535|0,_=0;for(;a!==0;){_=a>2e3?2e3:a,a-=_;do n=n+s[r++]|0,i=i+n|0;while(--_);n%=65521,i%=65521}return n|i<<16|0};const _a=new Uint32Array((()=>{let e,s=[];for(var a=0;a<256;a++){e=a;for(var r=0;r<8;r++)e=1&e?3988292384^e>>>1:e>>>1;s[a]=e}return s})());var U=(e,s,a,r)=>{const n=_a,i=r+a;e^=-1;for(let _=r;_<i;_++)e=e>>>8^n[255&(e^s[_])];return-1^e},ne={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},we={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};const{_tr_init:la,_tr_stored_block:me,_tr_flush_block:ha,_tr_tally:R,_tr_align:oa}=ta,{Z_NO_FLUSH:S,Z_PARTIAL_FLUSH:da,Z_FULL_FLUSH:ua,Z_FINISH:b,Z_BLOCK:Pe,Z_OK:c,Z_STREAM_END:je,Z_STREAM_ERROR:x,Z_DATA_ERROR:fa,Z_BUF_ERROR:be,Z_DEFAULT_COMPRESSION:ca,Z_FILTERED:pa,Z_HUFFMAN_ONLY:re,Z_RLE:ga,Z_FIXED:wa,Z_DEFAULT_STRATEGY:ma,Z_UNKNOWN:ba,Z_DEFLATED:ie}=we,F=258,A=262,N=42,O=113,G=666,D=(e,s)=>(e.msg=ne[s],s),Ke=e=>2*e-(e>4?9:0),T=e=>{let s=e.length;for(;--s>=0;)e[s]=0},ya=e=>{let s,a,r,n=e.w_size;s=e.hash_size,r=s;do a=e.head[--r],e.head[r]=a>=n?a-n:0;while(--s);s=n,r=s;do a=e.prev[--r],e.prev[r]=a>=n?a-n:0;while(--s)};let L=(e,s,a)=>(s<<e.hash_shift^a)&e.hash_mask;const g=e=>{const s=e.state;let a=s.pending;a>e.avail_out&&(a=e.avail_out),a!==0&&(e.output.set(s.pending_buf.subarray(s.pending_out,s.pending_out+a),e.next_out),e.next_out+=a,s.pending_out+=a,e.total_out+=a,e.avail_out-=a,s.pending-=a,s.pending===0&&(s.pending_out=0))},w=(e,s)=>{ha(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,s),e.block_start=e.strstart,g(e.strm)},o=(e,s)=>{e.pending_buf[e.pending++]=s},X=(e,s)=>{e.pending_buf[e.pending++]=s>>>8&255,e.pending_buf[e.pending++]=255&s},ye=(e,s,a,r)=>{let n=e.avail_in;return n>r&&(n=r),n===0?0:(e.avail_in-=n,s.set(e.input.subarray(e.next_in,e.next_in+n),a),e.state.wrap===1?e.adler=Me(e.adler,s,n,a):e.state.wrap===2&&(e.adler=U(e.adler,s,n,a)),e.next_in+=n,e.total_in+=n,n)},Ye=(e,s)=>{let a,r,n=e.max_chain_length,i=e.strstart,_=e.prev_length,t=e.nice_match;const h=e.strstart>e.w_size-A?e.strstart-(e.w_size-A):0,l=e.window,u=e.w_mask,d=e.prev,m=e.strstart+F;let V=l[i+_-1],_e=l[i+_];e.prev_length>=e.good_match&&(n>>=2),t>e.lookahead&&(t=e.lookahead);do if(a=s,l[a+_]===_e&&l[a+_-1]===V&&l[a]===l[i]&&l[++a]===l[i+1]){i+=2,a++;do;while(l[++i]===l[++a]&&l[++i]===l[++a]&&l[++i]===l[++a]&&l[++i]===l[++a]&&l[++i]===l[++a]&&l[++i]===l[++a]&&l[++i]===l[++a]&&l[++i]===l[++a]&&i<m);if(r=F-(m-i),i=m-F,r>_){if(e.match_start=s,_=r,r>=t)break;V=l[i+_-1],_e=l[i+_]}}while((s=d[s&u])>h&&--n!=0);return _<=e.lookahead?_:e.lookahead},I=e=>{const s=e.w_size;let a,r,n;do{if(r=e.window_size-e.lookahead-e.strstart,e.strstart>=s+(s-A)&&(e.window.set(e.window.subarray(s,s+s-r),0),e.match_start-=s,e.strstart-=s,e.block_start-=s,e.insert>e.strstart&&(e.insert=e.strstart),ya(e),r+=s),e.strm.avail_in===0)break;if(a=ye(e.strm,e.window,e.strstart+e.lookahead,r),e.lookahead+=a,e.lookahead+e.insert>=3)for(n=e.strstart-e.insert,e.ins_h=e.window[n],e.ins_h=L(e,e.ins_h,e.window[n+1]);e.insert&&(e.ins_h=L(e,e.ins_h,e.window[n+3-1]),e.prev[n&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=n,n++,e.insert--,!(e.lookahead+e.insert<3)););}while(e.lookahead<A&&e.strm.avail_in!==0)},Ge=(e,s)=>{let a,r,n,i=e.pending_buf_size-5>e.w_size?e.w_size:e.pending_buf_size-5,_=0,t=e.strm.avail_in;do{if(a=65535,n=e.bi_valid+42>>3,e.strm.avail_out<n||(n=e.strm.avail_out-n,r=e.strstart-e.block_start,a>r+e.strm.avail_in&&(a=r+e.strm.avail_in),a>n&&(a=n),a<i&&(a===0&&s!==b||s===S||a!==r+e.strm.avail_in)))break;_=s===b&&a===r+e.strm.avail_in?1:0,me(e,0,0,_),e.pending_buf[e.pending-4]=a,e.pending_buf[e.pending-3]=a>>8,e.pending_buf[e.pending-2]=~a,e.pending_buf[e.pending-1]=~a>>8,g(e.strm),r&&(r>a&&(r=a),e.strm.output.set(e.window.subarray(e.block_start,e.block_start+r),e.strm.next_out),e.strm.next_out+=r,e.strm.avail_out-=r,e.strm.total_out+=r,e.block_start+=r,a-=r),a&&(ye(e.strm,e.strm.output,e.strm.next_out,a),e.strm.next_out+=a,e.strm.avail_out-=a,e.strm.total_out+=a)}while(_===0);return t-=e.strm.avail_in,t&&(t>=e.w_size?(e.matches=2,e.window.set(e.strm.input.subarray(e.strm.next_in-e.w_size,e.strm.next_in),0),e.strstart=e.w_size,e.insert=e.strstart):(e.window_size-e.strstart<=t&&(e.strstart-=e.w_size,e.window.set(e.window.subarray(e.w_size,e.w_size+e.strstart),0),e.matches<2&&e.matches++,e.insert>e.strstart&&(e.insert=e.strstart)),e.window.set(e.strm.input.subarray(e.strm.next_in-t,e.strm.next_in),e.strstart),e.strstart+=t,e.insert+=t>e.w_size-e.insert?e.w_size-e.insert:t),e.block_start=e.strstart),e.high_water<e.strstart&&(e.high_water=e.strstart),_?4:s!==S&&s!==b&&e.strm.avail_in===0&&e.strstart===e.block_start?2:(n=e.window_size-e.strstart,e.strm.avail_in>n&&e.block_start>=e.w_size&&(e.block_start-=e.w_size,e.strstart-=e.w_size,e.window.set(e.window.subarray(e.w_size,e.w_size+e.strstart),0),e.matches<2&&e.matches++,n+=e.w_size,e.insert>e.strstart&&(e.insert=e.strstart)),n>e.strm.avail_in&&(n=e.strm.avail_in),n&&(ye(e.strm,e.window,e.strstart,n),e.strstart+=n,e.insert+=n>e.w_size-e.insert?e.w_size-e.insert:n),e.high_water<e.strstart&&(e.high_water=e.strstart),n=e.bi_valid+42>>3,n=e.pending_buf_size-n>65535?65535:e.pending_buf_size-n,i=n>e.w_size?e.w_size:n,r=e.strstart-e.block_start,(r>=i||(r||s===b)&&s!==S&&e.strm.avail_in===0&&r<=n)&&(a=r>n?n:r,_=s===b&&e.strm.avail_in===0&&a===r?1:0,me(e,e.block_start,a,_),e.block_start+=a,g(e.strm)),_?3:1)},ve=(e,s)=>{let a,r;for(;;){if(e.lookahead<A){if(I(e),e.lookahead<A&&s===S)return 1;if(e.lookahead===0)break}if(a=0,e.lookahead>=3&&(e.ins_h=L(e,e.ins_h,e.window[e.strstart+3-1]),a=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),a!==0&&e.strstart-a<=e.w_size-A&&(e.match_length=Ye(e,a)),e.match_length>=3)if(r=R(e,e.strstart-e.match_start,e.match_length-3),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=3){e.match_length--;do e.strstart++,e.ins_h=L(e,e.ins_h,e.window[e.strstart+3-1]),a=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart;while(--e.match_length!=0);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=L(e,e.ins_h,e.window[e.strstart+1]);else r=R(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(r&&(w(e,!1),e.strm.avail_out===0))return 1}return e.insert=e.strstart<2?e.strstart:2,s===b?(w(e,!0),e.strm.avail_out===0?3:4):e.sym_next&&(w(e,!1),e.strm.avail_out===0)?1:2},C=(e,s)=>{let a,r,n;for(;;){if(e.lookahead<A){if(I(e),e.lookahead<A&&s===S)return 1;if(e.lookahead===0)break}if(a=0,e.lookahead>=3&&(e.ins_h=L(e,e.ins_h,e.window[e.strstart+3-1]),a=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=2,a!==0&&e.prev_length<e.max_lazy_match&&e.strstart-a<=e.w_size-A&&(e.match_length=Ye(e,a),e.match_length<=5&&(e.strategy===pa||e.match_length===3&&e.strstart-e.match_start>4096)&&(e.match_length=2)),e.prev_length>=3&&e.match_length<=e.prev_length){n=e.strstart+e.lookahead-3,r=R(e,e.strstart-1-e.prev_match,e.prev_length-3),e.lookahead-=e.prev_length-1,e.prev_length-=2;do++e.strstart<=n&&(e.ins_h=L(e,e.ins_h,e.window[e.strstart+3-1]),a=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart);while(--e.prev_length!=0);if(e.match_available=0,e.match_length=2,e.strstart++,r&&(w(e,!1),e.strm.avail_out===0))return 1}else if(e.match_available){if(r=R(e,0,e.window[e.strstart-1]),r&&w(e,!1),e.strstart++,e.lookahead--,e.strm.avail_out===0)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(r=R(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<2?e.strstart:2,s===b?(w(e,!0),e.strm.avail_out===0?3:4):e.sym_next&&(w(e,!1),e.strm.avail_out===0)?1:2};function E(e,s,a,r,n){this.good_length=e,this.max_lazy=s,this.nice_length=a,this.max_chain=r,this.func=n}const W=[new E(0,0,0,0,Ge),new E(4,4,8,4,ve),new E(4,5,16,8,ve),new E(4,6,32,32,ve),new E(4,4,16,16,C),new E(8,16,32,32,C),new E(8,16,128,128,C),new E(8,32,128,256,C),new E(32,128,258,1024,C),new E(32,258,258,4096,C)];function va(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=ie,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),T(this.dyn_ltree),T(this.dyn_dtree),T(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),T(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),T(this.depth),this.sym_buf=0,this.lit_bufsize=0,this.sym_next=0,this.sym_end=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}const q=e=>{if(!e)return 1;const s=e.state;return!s||s.strm!==e||s.status!==N&&s.status!==57&&s.status!==69&&s.status!==73&&s.status!==91&&s.status!==103&&s.status!==O&&s.status!==G?1:0},Xe=e=>{if(q(e))return D(e,x);e.total_in=e.total_out=0,e.data_type=ba;const s=e.state;return s.pending=0,s.pending_out=0,s.wrap<0&&(s.wrap=-s.wrap),s.status=s.wrap===2?57:s.wrap?N:O,e.adler=s.wrap===2?0:1,s.last_flush=-2,la(s),c},We=e=>{const s=Xe(e);var a;return s===c&&((a=e.state).window_size=2*a.w_size,T(a.head),a.max_lazy_match=W[a.level].max_lazy,a.good_match=W[a.level].good_length,a.nice_match=W[a.level].nice_length,a.max_chain_length=W[a.level].max_chain,a.strstart=0,a.block_start=0,a.lookahead=0,a.insert=0,a.match_length=a.prev_length=2,a.match_available=0,a.ins_h=0),s},qe=(e,s,a,r,n,i)=>{if(!e)return x;let _=1;if(s===ca&&(s=6),r<0?(_=0,r=-r):r>15&&(_=2,r-=16),n<1||n>9||a!==ie||r<8||r>15||s<0||s>9||i<0||i>wa||r===8&&_!==1)return D(e,x);r===8&&(r=9);const t=new va;return e.state=t,t.strm=e,t.status=N,t.wrap=_,t.gzhead=null,t.w_bits=r,t.w_size=1<<t.w_bits,t.w_mask=t.w_size-1,t.hash_bits=n+7,t.hash_size=1<<t.hash_bits,t.hash_mask=t.hash_size-1,t.hash_shift=~~((t.hash_bits+3-1)/3),t.window=new Uint8Array(2*t.w_size),t.head=new Uint16Array(t.hash_size),t.prev=new Uint16Array(t.w_size),t.lit_bufsize=1<<n+6,t.pending_buf_size=4*t.lit_bufsize,t.pending_buf=new Uint8Array(t.pending_buf_size),t.sym_buf=t.lit_bufsize,t.sym_end=3*(t.lit_bufsize-1),t.level=s,t.strategy=i,t.method=a,We(e)};var J={deflateInit:(e,s)=>qe(e,s,ie,15,8,ma),deflateInit2:qe,deflateReset:We,deflateResetKeep:Xe,deflateSetHeader:(e,s)=>q(e)||e.state.wrap!==2?x:(e.state.gzhead=s,c),deflate:(e,s)=>{if(q(e)||s>Pe||s<0)return e?D(e,x):x;const a=e.state;if(!e.output||e.avail_in!==0&&!e.input||a.status===G&&s!==b)return D(e,e.avail_out===0?be:x);const r=a.last_flush;if(a.last_flush=s,a.pending!==0){if(g(e),e.avail_out===0)return a.last_flush=-1,c}else if(e.avail_in===0&&Ke(s)<=Ke(r)&&s!==b)return D(e,be);if(a.status===G&&e.avail_in!==0)return D(e,be);if(a.status===N&&a.wrap===0&&(a.status=O),a.status===N){let n=ie+(a.w_bits-8<<4)<<8,i=-1;if(i=a.strategy>=re||a.level<2?0:a.level<6?1:a.level===6?2:3,n|=i<<6,a.strstart!==0&&(n|=32),n+=31-n%31,X(a,n),a.strstart!==0&&(X(a,e.adler>>>16),X(a,65535&e.adler)),e.adler=1,a.status=O,g(e),a.pending!==0)return a.last_flush=-1,c}if(a.status===57){if(e.adler=0,o(a,31),o(a,139),o(a,8),a.gzhead)o(a,(a.gzhead.text?1:0)+(a.gzhead.hcrc?2:0)+(a.gzhead.extra?4:0)+(a.gzhead.name?8:0)+(a.gzhead.comment?16:0)),o(a,255&a.gzhead.time),o(a,a.gzhead.time>>8&255),o(a,a.gzhead.time>>16&255),o(a,a.gzhead.time>>24&255),o(a,a.level===9?2:a.strategy>=re||a.level<2?4:0),o(a,255&a.gzhead.os),a.gzhead.extra&&a.gzhead.extra.length&&(o(a,255&a.gzhead.extra.length),o(a,a.gzhead.extra.length>>8&255)),a.gzhead.hcrc&&(e.adler=U(e.adler,a.pending_buf,a.pending,0)),a.gzindex=0,a.status=69;else if(o(a,0),o(a,0),o(a,0),o(a,0),o(a,0),o(a,a.level===9?2:a.strategy>=re||a.level<2?4:0),o(a,3),a.status=O,g(e),a.pending!==0)return a.last_flush=-1,c}if(a.status===69){if(a.gzhead.extra){let n=a.pending,i=(65535&a.gzhead.extra.length)-a.gzindex;for(;a.pending+i>a.pending_buf_size;){let t=a.pending_buf_size-a.pending;if(a.pending_buf.set(a.gzhead.extra.subarray(a.gzindex,a.gzindex+t),a.pending),a.pending=a.pending_buf_size,a.gzhead.hcrc&&a.pending>n&&(e.adler=U(e.adler,a.pending_buf,a.pending-n,n)),a.gzindex+=t,g(e),a.pending!==0)return a.last_flush=-1,c;n=0,i-=t}let _=new Uint8Array(a.gzhead.extra);a.pending_buf.set(_.subarray(a.gzindex,a.gzindex+i),a.pending),a.pending+=i,a.gzhead.hcrc&&a.pending>n&&(e.adler=U(e.adler,a.pending_buf,a.pending-n,n)),a.gzindex=0}a.status=73}if(a.status===73){if(a.gzhead.name){let n,i=a.pending;do{if(a.pending===a.pending_buf_size){if(a.gzhead.hcrc&&a.pending>i&&(e.adler=U(e.adler,a.pending_buf,a.pending-i,i)),g(e),a.pending!==0)return a.last_flush=-1,c;i=0}n=a.gzindex<a.gzhead.name.length?255&a.gzhead.name.charCodeAt(a.gzindex++):0,o(a,n)}while(n!==0);a.gzhead.hcrc&&a.pending>i&&(e.adler=U(e.adler,a.pending_buf,a.pending-i,i)),a.gzindex=0}a.status=91}if(a.status===91){if(a.gzhead.comment){let n,i=a.pending;do{if(a.pending===a.pending_buf_size){if(a.gzhead.hcrc&&a.pending>i&&(e.adler=U(e.adler,a.pending_buf,a.pending-i,i)),g(e),a.pending!==0)return a.last_flush=-1,c;i=0}n=a.gzindex<a.gzhead.comment.length?255&a.gzhead.comment.charCodeAt(a.gzindex++):0,o(a,n)}while(n!==0);a.gzhead.hcrc&&a.pending>i&&(e.adler=U(e.adler,a.pending_buf,a.pending-i,i))}a.status=103}if(a.status===103){if(a.gzhead.hcrc){if(a.pending+2>a.pending_buf_size&&(g(e),a.pending!==0))return a.last_flush=-1,c;o(a,255&e.adler),o(a,e.adler>>8&255),e.adler=0}if(a.status=O,g(e),a.pending!==0)return a.last_flush=-1,c}if(e.avail_in!==0||a.lookahead!==0||s!==S&&a.status!==G){let n=a.level===0?Ge(a,s):a.strategy===re?((i,_)=>{let t;for(;;){if(i.lookahead===0&&(I(i),i.lookahead===0)){if(_===S)return 1;break}if(i.match_length=0,t=R(i,0,i.window[i.strstart]),i.lookahead--,i.strstart++,t&&(w(i,!1),i.strm.avail_out===0))return 1}return i.insert=0,_===b?(w(i,!0),i.strm.avail_out===0?3:4):i.sym_next&&(w(i,!1),i.strm.avail_out===0)?1:2})(a,s):a.strategy===ga?((i,_)=>{let t,h,l,u;const d=i.window;for(;;){if(i.lookahead<=F){if(I(i),i.lookahead<=F&&_===S)return 1;if(i.lookahead===0)break}if(i.match_length=0,i.lookahead>=3&&i.strstart>0&&(l=i.strstart-1,h=d[l],h===d[++l]&&h===d[++l]&&h===d[++l])){u=i.strstart+F;do;while(h===d[++l]&&h===d[++l]&&h===d[++l]&&h===d[++l]&&h===d[++l]&&h===d[++l]&&h===d[++l]&&h===d[++l]&&l<u);i.match_length=F-(u-l),i.match_length>i.lookahead&&(i.match_length=i.lookahead)}if(i.match_length>=3?(t=R(i,1,i.match_length-3),i.lookahead-=i.match_length,i.strstart+=i.match_length,i.match_length=0):(t=R(i,0,i.window[i.strstart]),i.lookahead--,i.strstart++),t&&(w(i,!1),i.strm.avail_out===0))return 1}return i.insert=0,_===b?(w(i,!0),i.strm.avail_out===0?3:4):i.sym_next&&(w(i,!1),i.strm.avail_out===0)?1:2})(a,s):W[a.level].func(a,s);if(n!==3&&n!==4||(a.status=G),n===1||n===3)return e.avail_out===0&&(a.last_flush=-1),c;if(n===2&&(s===da?oa(a):s!==Pe&&(me(a,0,0,!1),s===ua&&(T(a.head),a.lookahead===0&&(a.strstart=0,a.block_start=0,a.insert=0))),g(e),e.avail_out===0))return a.last_flush=-1,c}return s!==b?c:a.wrap<=0?je:(a.wrap===2?(o(a,255&e.adler),o(a,e.adler>>8&255),o(a,e.adler>>16&255),o(a,e.adler>>24&255),o(a,255&e.total_in),o(a,e.total_in>>8&255),o(a,e.total_in>>16&255),o(a,e.total_in>>24&255)):(X(a,e.adler>>>16),X(a,65535&e.adler)),g(e),a.wrap>0&&(a.wrap=-a.wrap),a.pending!==0?c:je)},deflateEnd:e=>{if(q(e))return x;const s=e.state.status;return e.state=null,s===O?D(e,fa):c},deflateSetDictionary:(e,s)=>{let a=s.length;if(q(e))return x;const r=e.state,n=r.wrap;if(n===2||n===1&&r.status!==N||r.lookahead)return x;if(n===1&&(e.adler=Me(e.adler,s,a,0)),r.wrap=0,a>=r.w_size){n===0&&(T(r.head),r.strstart=0,r.block_start=0,r.insert=0);let h=new Uint8Array(r.w_size);h.set(s.subarray(a-r.w_size,a),0),s=h,a=r.w_size}const i=e.avail_in,_=e.next_in,t=e.input;for(e.avail_in=a,e.next_in=0,e.input=s,I(r);r.lookahead>=3;){let h=r.strstart,l=r.lookahead-2;do r.ins_h=L(r,r.ins_h,r.window[h+3-1]),r.prev[h&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=h,h++;while(--l);r.strstart=h,r.lookahead=2,I(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=2,r.match_available=0,e.next_in=_,e.input=t,e.avail_in=i,r.wrap=n,c},deflateInfo:"pako deflate (from Nodeca project)"};const za=(e,s)=>Object.prototype.hasOwnProperty.call(e,s);var ka=function(e){const s=Array.prototype.slice.call(arguments,1);for(;s.length;){const a=s.shift();if(a){if(typeof a!="object")throw new TypeError(a+"must be non-object");for(const r in a)za(a,r)&&(e[r]=a[r])}}return e},xa=e=>{let s=0;for(let r=0,n=e.length;r<n;r++)s+=e[r].length;const a=new Uint8Array(s);for(let r=0,n=0,i=e.length;r<i;r++){let _=e[r];a.set(_,n),n+=_.length}return a};let Aa=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch{Aa=!1}const ze=new Uint8Array(256);for(let e=0;e<256;e++)ze[e]=e>=252?6:e>=248?5:e>=240?4:e>=224?3:e>=192?2:1;ze[254]=ze[254]=1;var Je=e=>{if(typeof TextEncoder=="function"&&TextEncoder.prototype.encode)return new TextEncoder().encode(e);let s,a,r,n,i,_=e.length,t=0;for(n=0;n<_;n++)a=e.charCodeAt(n),(64512&a)==55296&&n+1<_&&(r=e.charCodeAt(n+1),(64512&r)==56320&&(a=65536+(a-55296<<10)+(r-56320),n++)),t+=a<128?1:a<2048?2:a<65536?3:4;for(s=new Uint8Array(t),i=0,n=0;i<t;n++)a=e.charCodeAt(n),(64512&a)==55296&&n+1<_&&(r=e.charCodeAt(n+1),(64512&r)==56320&&(a=65536+(a-55296<<10)+(r-56320),n++)),a<128?s[i++]=a:a<2048?(s[i++]=192|a>>>6,s[i++]=128|63&a):a<65536?(s[i++]=224|a>>>12,s[i++]=128|a>>>6&63,s[i++]=128|63&a):(s[i++]=240|a>>>18,s[i++]=128|a>>>12&63,s[i++]=128|a>>>6&63,s[i++]=128|63&a);return s},Ea=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0};const Qe=Object.prototype.toString,{Z_NO_FLUSH:Za,Z_SYNC_FLUSH:Ua,Z_FULL_FLUSH:Ra,Z_FINISH:Sa,Z_OK:te,Z_STREAM_END:Ta,Z_DEFAULT_COMPRESSION:La,Z_DEFAULT_STRATEGY:Fa,Z_DEFLATED:Oa}=we;function Q(e){this.options=ka({level:La,method:Oa,chunkSize:16384,windowBits:15,memLevel:8,strategy:Fa},e||{});let s=this.options;s.raw&&s.windowBits>0?s.windowBits=-s.windowBits:s.gzip&&s.windowBits>0&&s.windowBits<16&&(s.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Ea,this.strm.avail_out=0;let a=J.deflateInit2(this.strm,s.level,s.method,s.windowBits,s.memLevel,s.strategy);if(a!==te)throw new Error(ne[a]);if(s.header&&J.deflateSetHeader(this.strm,s.header),s.dictionary){let r;if(r=typeof s.dictionary=="string"?Je(s.dictionary):Qe.call(s.dictionary)==="[object ArrayBuffer]"?new Uint8Array(s.dictionary):s.dictionary,a=J.deflateSetDictionary(this.strm,r),a!==te)throw new Error(ne[a]);this._dict_set=!0}}function ke(e,s){const a=new Q(s);if(a.push(e,!0),a.err)throw a.msg||ne[a.err];return a.result}Q.prototype.push=function(e,s){const a=this.strm,r=this.options.chunkSize;let n,i;if(this.ended)return!1;for(i=s===~~s?s:s===!0?Sa:Za,typeof e=="string"?a.input=Je(e):Qe.call(e)==="[object ArrayBuffer]"?a.input=new Uint8Array(e):a.input=e,a.next_in=0,a.avail_in=a.input.length;;)if(a.avail_out===0&&(a.output=new Uint8Array(r),a.next_out=0,a.avail_out=r),(i===Ua||i===Ra)&&a.avail_out<=6)this.onData(a.output.subarray(0,a.next_out)),a.avail_out=0;else{if(n=J.deflate(a,i),n===Ta)return a.next_out>0&&this.onData(a.output.subarray(0,a.next_out)),n=J.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===te;if(a.avail_out!==0){if(i>0&&a.next_out>0)this.onData(a.output.subarray(0,a.next_out)),a.avail_out=0;else if(a.avail_in===0)break}else this.onData(a.output)}return!0},Q.prototype.onData=function(e){this.chunks.push(e)},Q.prototype.onEnd=function(e){e===te&&(this.result=xa(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var Ve=Q,$e=ke,ea=function(e,s){return(s=s||{}).raw=!0,ke(e,s)},aa=function(e,s){return(s=s||{}).gzip=!0,ke(e,s)},sa=we,Da={Deflate:Ve,deflate:$e,deflateRaw:ea,gzip:aa,constants:sa};v.Deflate=Ve,v.constants=sa,v.default=Da,v.deflate=$e,v.deflateRaw=ea,v.gzip=aa,Object.defineProperty(v,"__esModule",{value:!0})});
