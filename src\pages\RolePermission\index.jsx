import React, { useState, useEffect } from "react";
import {
  Card,
  Button,
  Space,
  Table,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  Radio,
  message,
  Row,
  Col,
  Tooltip,
  Checkbox,
  Dropdown,
  Divider,
  InputNumber,
} from "antd";
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  CaretRightOutlined,
  CaretDownOutlined,
  DownOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import editPermissionImg from "../../img/编辑权限.png";
import "./styles.css";
import { rolePermissionApi } from "../../service/RolePermission";
import { set } from "lodash";

const { Search } = Input;
const { Option } = Select;

// 自定义树节点组件
const CustomTreeNode = ({
  node,
  level = 0,
  checkedKeys,
  halfCheckedKeys,
  onNodeCheck,
}) => {
  const isChecked = checkedKeys.includes(node.key);
  const isHalfChecked = halfCheckedKeys.includes(node.key);
  const hasChildren = node.children && node.children.length > 0;
  const paddingLeft = level * 24;
  const [expanded, setExpanded] = useState(true);

  const toggleExpand = (e) => {
    e.stopPropagation();
    setExpanded(!expanded);
  };

  return (
    <div className="custom-tree-node">
      <div
        className="node-content"
        style={{
          display: "flex",
          alignItems: "center",
          padding: "8px 0",
        }}
      >
        {hasChildren && (
          <span
            onClick={toggleExpand}
            style={{
              marginRight: "8px",
              cursor: "pointer",
              fontSize: "14px",
            }}
          >
            {expanded ? <CaretDownOutlined /> : <CaretRightOutlined />}
          </span>
        )}
        <Checkbox
          checked={isChecked}
          indeterminate={isHalfChecked}
          onChange={(e) => onNodeCheck(node, e.target.checked)}
        >
          <Space>
            <span className="permission-title">{node.title}</span>
            {node.type && (
              <Tag
                color={level === 0 ? "blue" : level === 1 ? "cyan" : "green"}
              >
                {node.type}
              </Tag>
            )}
            {node.description && (
              <span
                style={{
                  color: "rgba(0, 0, 0, 0.45)",
                  fontSize: level === 0 ? "14px" : "12px",
                }}
              >
                {node.description}
              </span>
            )}
          </Space>
        </Checkbox>
      </div>

      {hasChildren && expanded && (
        <div
          className="node-children"
          style={{
            marginLeft: "40px",
            marginTop: "4px",
            paddingLeft: "16px",
            position: "relative",
          }}
        >
          <Row gutter={[8, 8]} style={{ display: "flex", flexWrap: "wrap" }}>
            {node.children.map((child) => (
              <Col
                key={child.key}
                span={child.children && child.children.length > 0 ? 24 : 3}
              >
                <CustomTreeNode
                  node={child}
                  level={level + 1}
                  checkedKeys={checkedKeys}
                  halfCheckedKeys={halfCheckedKeys}
                  onNodeCheck={onNodeCheck}
                />
              </Col>
            ))}
          </Row>
        </div>
      )}
    </div>
  );
};

// 自定义树组件
const CustomTree = ({ treeData, checkedKeys, halfCheckedKeys, onCheck }) => {
  return (
    <div className="custom-tree">
      {treeData.map((node) => (
        <CustomTreeNode
          key={node.key}
          node={node}
          checkedKeys={checkedKeys}
          halfCheckedKeys={halfCheckedKeys}
          onNodeCheck={onCheck}
        />
      ))}
    </div>
  );
};

const RolePermission = () => {
  const [selectedRole, setSelectedRole] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editRoleData, setEditRoleData] = useState(null);
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [checkedKeys, setCheckedKeys] = useState([]);
  const [halfCheckedKeys, setHalfCheckedKeys] = useState([]);
  const [addPermissionVisible, setAddPermissionVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [permissionForm] = Form.useForm();
  // 新增权限数量统计状态
  const [permissionCount, setPermissionCount] = useState({
    assigned: 0,
    total: 0,
  });

  // 添加状态来跟踪当前选择的资源类型
  const [currentResourceType, setCurrentResourceType] = useState("");

  // 添加角色列表和分页状态
  const [roles, setRoles] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 添加过滤条件状态
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [tenantFilter, setTenantFilter] = useState("all");
  const [searchValue, setSearchValue] = useState("");

  // 添加状态来存储权限树数据
  const [permissionTree, setPermissionTree] = useState([]);
  const [permissionLoading, setPermissionLoading] = useState(false);
  const [modules, setModules] = useState([]); // 新增：用于存储模块数据
  const [selectedModule, setSelectedModule] = useState("all"); // 新增：当前选中的模块
  const [selectedAction, setSelectedAction] = useState("selectAll"); // 默认选中全选

  // 添加租户列表状态
  const [tenantList, setTenantList] = useState([]);

  // 新增角色状态记录，用于控制编辑表单中的字段禁用状态
  const [editingRoleStatus, setEditingRoleStatus] = useState("active");

  // 添加编辑角色时的权限树数据和选中的权限状态
  const [editPermissionTree, setEditPermissionTree] = useState([]);
  const [editCheckedKeys, setEditCheckedKeys] = useState([]);
  const [editHalfCheckedKeys, setEditHalfCheckedKeys] = useState([]);
  const [permissionDropdownVisible, setPermissionDropdownVisible] =
    useState(false);

  // 添加useEffect钩子来获取角色数据
  useEffect(() => {
    fetchRoleList(1, pagination.pageSize, {});
  }, []);

  // 获取角色列表
  const fetchRoleList = async (
    pageNum = pagination.current,
    pageSize = pagination.pageSize,
    filters = {}
  ) => {
    try {
      setLoading(true);
      const params = {
        pageNum: pageNum,
        pageSize: pageSize,
        ...filters,
      };

      const response = await rolePermissionApi.getRolePage(params);

      if (response && response.code === 200 && response.data) {
        setLoading(true);
        try {
          const { content, totalElements } = response.data;

          // 将接口返回的数据转换为表格需要的格式
          const formattedData = content.map((item) => ({
            key: item.id,
            name: item.roleName,
            description: item.description,
            priority: item.priority,
            status: item.status,
            isSystemRole: item.isSystemRole,
            type: item.isSystemRole ? "system" : "custom",
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            protected: item.isSystemRole === 1,
            tenantId: item.tenantId, // 添加 tenantId 字段保存 API 返回的租户 ID
          }));

          setRoles(formattedData);
          setPagination({
            current: pageNum,
            pageSize: pageSize,
            total: totalElements,
          });
          setLoading(false);
        } catch (error) {
          console.error("格式化角色列表数据失败", error);
        }
      }
    } catch (error) {
      console.error("获取角色列表错误:", error);
    } finally {
      setLoading(false);
    }
  };

  // 处理分页和页面大小变化
  const handleTableChange = (page, pageSize) => {
    fetchRoleList(page, pageSize, {});
  };

  // 刷新按钮点击事件
  const handleRefresh = () => {
    fetchRoleList(1, pagination.pageSize, {});
    // 清空角色选择和权限数据
    setSelectedRole(null);
    setCheckedKeys([]);
    setHalfCheckedKeys([]);
    setPermissionTree([]);
    setPermissionCount({
      assigned: 0,
      total: 0,
    });
  };

  // Mock data for permissions
  const permissions = [];

  // 获取角色权限数量统计
  const fetchPermissionNum = async (roleId) => {
    try {
      const response = await rolePermissionApi.getPermissionNum(roleId);

      if (response && response.code === 200 && response.data) {
        // 从接口获取已分配权限数量和总权限数量
        const { isHaveNum, totalNum } = response.data;
        setPermissionCount({
          assigned: isHaveNum || 0,
          total: totalNum || 0,
        });
      } else {
        console.error("获取权限数量统计失败", response);
        // 如果接口失败，设置默认值
        setPermissionCount({
          assigned: 0,
          total: 0,
        });
      }
    } catch (error) {
      console.error("获取权限数量统计错误:", error);
      // 发生错误时设置默认值
      setPermissionCount({
        assigned: 0,
        total: 0,
      });
    }
  };

  // 获取角色权限树
  const fetchPermissionMenu = async (roleId) => {
    try {
      setPermissionLoading(true);
      // 调用接口获取权限树
      const response = await rolePermissionApi.getPermissionTree({
        roleId,
        permissionId: "", // 空字符串表示获取全部模块的权限树
      });

      if (response && response.code === 200 && response.data) {
        // 提取模块数据
        const moduleData = extractModules(response.data);
        setModules(moduleData);

        // 提取已选中的权限ID
        const selectedKeys = extractCheckedPermissions(response.data);
        setCheckedKeys(selectedKeys);

        // 处理权限树数据 - 转换后端返回的数据结构为前端Tree组件需要的格式
        const transformedData = transformPermissionData(response.data);
        setPermissionTree(transformedData);
      } else {
        // 如果接口失败，使用默认权限数据
        setPermissionTree(permissions);
        setModules([{ value: "all", label: "全部模块" }]);
        setCheckedKeys([]);
      }
    } catch (error) {
      console.error("获取权限树错误:", error);
      // 如果接口异常，使用默认权限数据
      setPermissionTree(permissions);
      setModules([{ value: "all", label: "全部模块" }]);
      setCheckedKeys([]);
    } finally {
      setPermissionLoading(false);
    }
  };

  // 从权限数据中提取模块信息
  const extractModules = (data) => {
    // 首先添加"全部模块"选项
    const moduleList = [{ value: "all", label: "全部模块" }];

    // 遍历权限数据提取模块信息
    if (Array.isArray(data)) {
      data.forEach((item) => {
        // 假设每个顶级项目就是一个模块
        if (item.id && item.name) {
          moduleList.push({
            value: item.id,
            label: item.name,
          });
        }
      });
    }

    return moduleList;
  };

  // 提取已选中的权限ID
  const extractCheckedPermissions = (data) => {
    const checkedPermissions = new Set();
    const halfCheckedPermissions = new Set();

    // 递归检查节点状态
    const checkNodeStatus = (items) => {
      if (!Array.isArray(items)) return { allChecked: true, anyChecked: false };

      let allChildrenChecked = true;
      let anyChildChecked = false;

      for (const item of items) {
        const id = item.id || item.menuId || item.key;
        const children = item.children || item.subMenus || item.childrenList;

        if (children && children.length > 0) {
          const childStatus = checkNodeStatus(children);
          allChildrenChecked = allChildrenChecked && childStatus.allChecked;
          anyChildChecked = anyChildChecked || childStatus.anyChecked;
        }

        if (item.isHave === 1) {
          anyChildChecked = true;
        } else if (item.isHave === 0) {
          allChildrenChecked = false;
        }
      }

      return { allChecked: allChildrenChecked, anyChecked: anyChildChecked };
    };

    // 递归设置节点选中状态
    const setNodeCheckStatus = (items) => {
      if (!Array.isArray(items)) return;

      items.forEach((item) => {
        const id = item.id || item.menuId || item.key;
        const children = item.children || item.subMenus || item.childrenList;

        if (children && children.length > 0) {
          setNodeCheckStatus(children);
          const status = checkNodeStatus(children);

          if (status.allChecked && item.isHave === 1) {
            checkedPermissions.add(id);
          } else if (status.anyChecked || item.isHave === 1) {
            halfCheckedPermissions.add(id);
          }
        } else if (item.isHave === 1) {
          checkedPermissions.add(id);
        }
      });
    };

    setNodeCheckStatus(data);
    setHalfCheckedKeys(Array.from(halfCheckedPermissions));
    return Array.from(checkedPermissions);
  };

  // 转换权限数据结构
  const transformPermissionData = (data) => {
    // 判断数据是否为数组
    if (!Array.isArray(data)) {
      console.error("权限数据不是数组格式");
      return [];
    }

    // 递归转换数据结构
    const transform = (items) => {
      return items.map((item) => {
        // 构建符合Tree组件要求的节点结构
        const node = {
          title: item.name || item.menuName || item.title || "未命名",
          key: item.id || item.menuId || item.key || `key-${Math.random()}`,
          type: item.type || item.menuType,
          description: item.description || item.menuDesc,
        };

        // 处理子节点
        if (
          item.children &&
          Array.isArray(item.children) &&
          item.children.length > 0
        ) {
          node.children = transform(item.children);
        } else if (
          item.subMenus &&
          Array.isArray(item.subMenus) &&
          item.subMenus.length > 0
        ) {
          node.children = transform(item.subMenus);
        } else if (
          item.childrenList &&
          Array.isArray(item.childrenList) &&
          item.childrenList.length > 0
        ) {
          node.children = transform(item.childrenList);
        }

        return node;
      });
    };

    return transform(data);
  };

  // Table columns
  const columns = [
    {
      title: "角色名称",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (text, record) => (
        <Space direction="vertical" size={0}>
          <Space>
            <span>{text}</span>
            {record.protected && <Tag color="blue">系统保护</Tag>}
          </Space>
        </Space>
      ),
    },
    {
      title: "角色描述",
      dataIndex: "description",
      key: "description",
      align: "center",
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "优先级",
      dataIndex: "priority",
      key: "priority",
      align: "center",
      render: (priority) => {
        let text = "";
        let color = "";

        switch (priority) {
          case 0:
            text = "低";
            color = "blue";
            break;
          case 1:
            text = "中";
            color = "orange";
            break;
          case 2:
            text = "高";
            color = "red";
            break;
          default:
            text = priority;
            color = "blue";
        }

        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      align: "center",
      render: (status) => (
        <Tag color={status === "active" ? "green" : "red"}>
          {status === "active" ? "启用" : "禁用"}
        </Tag>
      ),
    },
    {
      title: "是否为预定义角色",
      dataIndex: "isSystemRole",
      key: "isSystemRole",
      align: "center",
      render: (isSystemRole) => (
        <Tag color={isSystemRole ? "cyan" : "purple"}>
          {isSystemRole ? "是" : "否"}
        </Tag>
      ),
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      align: "center",
    },
    {
      title: "更新时间",
      dataIndex: "updatedAt",
      key: "updatedAt",
      align: "center",
    },
    {
      title: "操作",
      key: "action",
      align: "center",
      render: (_, record) => {
        // 判断角色状态是否为禁用
        const isDisabled = record.status !== "active";

        return (
          <Space>
            <Tooltip title={isDisabled ? "角色已禁用，无法操作" : "查看权限"}>
              <Button
                type="text"
                onClick={() => handleViewPermissions(record)}
                disabled={isDisabled}
              >
                <img
                  src={editPermissionImg}
                  alt="编辑权限"
                  style={{ width: "16px", height: "16px" }}
                />
              </Button>
            </Tooltip>
            {!record.protected && (
              <>
                <Tooltip title={isDisabled ? "编辑角色状态" : "编辑角色"}>
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    onClick={() => handleEditRole(record)}
                    disabled={false} // 编辑按钮始终可用
                  />
                </Tooltip>
                <Tooltip
                  title={isDisabled ? "角色已禁用，无法操作" : "删除角色"}
                >
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleDeleteRole(record)}
                    disabled={isDisabled}
                  />
                </Tooltip>
              </>
            )}
          </Space>
        );
      },
    },
  ];

  const handleCreateRole = (values) => {
    // 准备请求参数
    const params = {
      roleName: values.name,
      description: values.description,
      parentRoleId: values.parentRoleId || "",
      isSystemRole: values.type === "system" ? 1 : 0,
      priority: values.priority,
      tenantId: values.tenant,
      status: values.status,
    };

    // 调用添加角色API
    rolePermissionApi
      .addRole(params)
      .then((response) => {
        if (response && response.code === 200) {
          message.success("角色创建成功！");
          setCreateModalVisible(false);
          form.resetFields();
          // 刷新角色列表
          fetchRoleList(pagination.current, pagination.pageSize, {});
        }
      })
      .catch((error) => {
        console.error("角色创建错误:", error);
      });
  };

  // 获取角色详情
  const fetchRoleDetail = async (id) => {
    try {
      setLoading(true);
      const response = await rolePermissionApi.getRoleById(id);

      if (response && response.code === 200 && response.data) {
        // 获取角色详情成功，设置编辑表单数据
        const roleData = response.data;
        setEditRoleData(roleData);

        // 设置表单初始值
        editForm.setFieldsValue({
          name: roleData.roleName,
          description: roleData.description,
          type: roleData.isSystemRole ? "system" : "custom",
          priority: roleData.priority,
          tenant: roleData.tenantId, // 假设有 tenantId 字段
          status: roleData.status,
          parentRoleId: roleData.parentRoleId, // 添加父级角色ID
        });

        // 显示编辑弹窗
        setEditModalVisible(true);
      }
    } catch (error) {
      console.error("获取角色详情错误:", error);
    } finally {
      setLoading(false);
    }
  };

  // 处理编辑角色按钮点击
  const handleEditRole = (record) => {
    // 保存角色状态，用于控制编辑表单中字段的禁用状态
    setEditingRoleStatus(record.status);
    // 清空之前的权限选择
    setEditCheckedKeys([]);
    setEditHalfCheckedKeys([]);
    // 获取角色详情数据
    fetchRoleDetail(record.key);
    // 获取该角色的权限树
    fetchRolePermissionTree(record.key);
  };

  // 获取角色权限树用于编辑
  const fetchRolePermissionTree = async (roleId) => {
    try {
      setPermissionLoading(true);
      // 调用接口获取权限树
      const response = await rolePermissionApi.getPermissionTree({
        roleId,
        permissionId: "", // 空字符串表示获取全部模块的权限树
      });

      if (response && response.code === 200 && response.data) {
        // 提取已选中的权限ID
        const selectedKeys = extractCheckedPermissions(response.data);
        setEditCheckedKeys(selectedKeys);

        // 处理权限树数据 - 转换后端返回的数据结构为前端Tree组件需要的格式
        const transformedData = transformPermissionData(response.data);
        setEditPermissionTree(transformedData);
      } else {
        setEditPermissionTree([]);
        setEditCheckedKeys([]);
      }
    } catch (error) {
      console.error("获取权限树错误:", error);
      setEditPermissionTree([]);
      setEditCheckedKeys([]);
    } finally {
      setPermissionLoading(false);
    }
  };

  // 处理编辑表单的权限节点选中状态变化
  const handleEditNodeCheck = (node, checked) => {
    // 创建新的选中键列表
    let newCheckedKeys = [...editCheckedKeys];

    // 获取所有子节点的key
    const getAllChildKeys = (item) => {
      let keys = [item.key];
      if (item.children && item.children.length > 0) {
        item.children.forEach((child) => {
          keys = [...keys, ...getAllChildKeys(child)];
        });
      }
      return keys;
    };

    // 获取父节点路径
    const getParentPath = (data, targetKey, path = []) => {
      for (const item of data) {
        if (item.key === targetKey) {
          return path;
        }

        if (item.children && item.children.length > 0) {
          const foundPath = getParentPath(item.children, targetKey, [
            ...path,
            item,
          ]);
          if (foundPath.length) {
            return foundPath;
          }
        }
      }

      return [];
    };

    // 检查节点下是否有任何子节点被选中（递归检查整个子树）
    const hasCheckedDescendant = (item, checkedKeys) => {
      if (!item.children || item.children.length === 0) {
        return checkedKeys.includes(item.key);
      }

      return item.children.some((child) => {
        const childChecked = checkedKeys.includes(child.key);
        const descendantChecked =
          child.children && child.children.length > 0
            ? hasCheckedDescendant(child, checkedKeys)
            : false;
        return childChecked || descendantChecked;
      });
    };

    // 检查节点及其所有后代是否都被选中
    const areAllDescendantsChecked = (item, checkedKeys) => {
      // 检查当前节点是否选中
      if (!checkedKeys.includes(item.key)) {
        return false;
      }

      // 如果没有子节点，则返回true
      if (!item.children || item.children.length === 0) {
        return true;
      }

      // 检查所有子节点是否都被选中
      return item.children.every((child) =>
        areAllDescendantsChecked(child, checkedKeys)
      );
    };

    if (checked) {
      // 选中节点逻辑 - 选中节点及其所有子节点
      const childKeys = getAllChildKeys(node);
      newCheckedKeys = [...new Set([...newCheckedKeys, ...childKeys])];

      // 处理父节点选中状态
      const parentNodes = getParentPath(editPermissionTree, node.key);

      // 更新每个父节点的状态
      parentNodes.forEach((parent) => {
        // 获取所有直接子节点的key
        const childKeys = parent.children.map((child) => child.key);

        // 检查是否所有直接子节点都被选中
        const allDirectChildrenChecked = childKeys.every((key) =>
          newCheckedKeys.includes(key)
        );

        // 检查是否所有子节点及其后代都被选中
        const allDescendantsOfChildrenChecked = parent.children.every(
          (child) => {
            if (!child.children || child.children.length === 0) {
              return newCheckedKeys.includes(child.key);
            }
            return areAllDescendantsChecked(child, newCheckedKeys);
          }
        );

        // 检查是否有任何子节点被选中（包括深层后代）
        const anyChildChecked = parent.children.some((child) =>
          hasCheckedDescendant(child, newCheckedKeys)
        );

        if (allDirectChildrenChecked && allDescendantsOfChildrenChecked) {
          // 如果所有子节点和所有后代都被选中，设置父节点为完全选中状态
          if (!newCheckedKeys.includes(parent.key)) {
            newCheckedKeys.push(parent.key);
          }
          setEditHalfCheckedKeys((prevState) =>
            prevState.filter((key) => key !== parent.key)
          );
        } else if (anyChildChecked) {
          // 如果有任何子节点被选中但不是所有，则设置父节点为半选状态
          newCheckedKeys = newCheckedKeys.filter((key) => key !== parent.key);
          if (!editHalfCheckedKeys.includes(parent.key)) {
            setEditHalfCheckedKeys((prevState) => [...prevState, parent.key]);
          }
        } else {
          // 如果没有子节点被选中，取消父节点的选中和半选状态
          newCheckedKeys = newCheckedKeys.filter((key) => key !== parent.key);
          setEditHalfCheckedKeys((prevState) =>
            prevState.filter((key) => key !== parent.key)
          );
        }
      });
    } else {
      // 取消选中节点逻辑 - 取消选中节点及其所有子节点
      const childKeys = getAllChildKeys(node);
      newCheckedKeys = newCheckedKeys.filter((key) => !childKeys.includes(key));

      // 处理父节点选中状态
      const parentNodes = getParentPath(editPermissionTree, node.key);

      // 更新每个父节点的状态
      parentNodes.forEach((parent) => {
        // 获取所有直接子节点的key
        const childKeys = parent.children.map((child) => child.key);

        // 检查是否所有直接子节点都被选中
        const allDirectChildrenChecked = childKeys.every((key) =>
          newCheckedKeys.includes(key)
        );

        // 检查是否所有子节点及其后代都被选中
        const allDescendantsOfChildrenChecked = parent.children.every(
          (child) => {
            if (!child.children || child.children.length === 0) {
              return newCheckedKeys.includes(child.key);
            }
            return areAllDescendantsChecked(child, newCheckedKeys);
          }
        );

        // 检查是否有任何子节点被选中（包括深层后代）
        const anyChildChecked = parent.children.some((child) =>
          hasCheckedDescendant(child, newCheckedKeys)
        );

        if (allDirectChildrenChecked && allDescendantsOfChildrenChecked) {
          // 如果所有子节点和所有后代都被选中，设置父节点为完全选中状态
          if (!newCheckedKeys.includes(parent.key)) {
            newCheckedKeys.push(parent.key);
          }
          setEditHalfCheckedKeys((prevState) =>
            prevState.filter((key) => key !== parent.key)
          );
        } else if (anyChildChecked) {
          // 如果有任何子节点被选中但不是所有，则设置父节点为半选状态
          newCheckedKeys = newCheckedKeys.filter((key) => key !== parent.key);
          if (!editHalfCheckedKeys.includes(parent.key)) {
            setEditHalfCheckedKeys((prevState) => [...prevState, parent.key]);
          }
        } else {
          // 如果没有子节点被选中，取消父节点的选中和半选状态
          newCheckedKeys = newCheckedKeys.filter((key) => key !== parent.key);
          setEditHalfCheckedKeys((prevState) =>
            prevState.filter((key) => key !== parent.key)
          );
        }
      });
    }

    setEditCheckedKeys(newCheckedKeys);
  };

  // 获取选中权限的标签组件
  const getSelectedPermissionTags = (selectedKeys, tree, onRemove) => {
    if (selectedKeys.length === 0) {
      return null;
    }

    const permissionMap = new Map();

    const buildPermissionMap = (nodes) => {
      nodes.forEach((node) => {
        permissionMap.set(node.key, {
          title: node.title,
          type: node.type,
        });

        if (node.children && node.children.length > 0) {
          buildPermissionMap(node.children);
        }
      });
    };

    buildPermissionMap(tree);

    return (
      <div
        style={{
          maxHeight: "100px",
          overflowY: "auto",
          padding: "5px",
          display: "flex",
          flexWrap: "wrap",
          gap: "5px",
        }}
      >
        {selectedKeys.map((key) => {
          const permission = permissionMap.get(key);
          if (!permission) return null;

          return (
            <Tag
              key={key}
              closable
              style={{
                display: "flex",
                alignItems: "center",
                margin: "2px",
                backgroundColor: "#f5f5f5",
              }}
              onClose={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onRemove(key);
              }}
            >
              {permission.title}
            </Tag>
          );
        })}
      </div>
    );
  };

  // 获取选中权限的显示文本
  const getSelectedPermissionText = () => {
    if (editCheckedKeys.length === 0) {
      return "请选择权限";
    }

    return `已选择 ${editCheckedKeys.length} 项权限`;
  };

  // 处理移除权限标签
  const handleRemovePermissionTag = (key) => {
    const newCheckedKeys = editCheckedKeys.filter((k) => k !== key);
    setEditCheckedKeys(newCheckedKeys);

    // 处理父节点状态
    const getParentPath = (data, targetKey, path = []) => {
      for (const item of data) {
        if (item.key === targetKey) {
          return path;
        }

        if (item.children && item.children.length > 0) {
          const foundPath = getParentPath(item.children, targetKey, [
            ...path,
            item,
          ]);
          if (foundPath.length) {
            return foundPath;
          }
        }
      }

      return [];
    };

    // 获取节点的所有子节点keys
    const getAllChildKeys = (item) => {
      let keys = [item.key];
      if (item.children && item.children.length > 0) {
        item.children.forEach((child) => {
          keys = [...keys, ...getAllChildKeys(child)];
        });
      }
      return keys;
    };

    // 检查节点下是否有任何子节点被选中（递归检查整个子树）
    const hasCheckedDescendant = (item, checkedKeys) => {
      if (!item.children || item.children.length === 0) {
        return checkedKeys.includes(item.key);
      }

      return item.children.some((child) => {
        const childChecked = checkedKeys.includes(child.key);
        const descendantChecked =
          child.children && child.children.length > 0
            ? hasCheckedDescendant(child, checkedKeys)
            : false;
        return childChecked || descendantChecked;
      });
    };

    // 检查所有直接子节点是否都被选中
    const areAllChildrenChecked = (item, checkedKeys) => {
      if (!item.children || item.children.length === 0) {
        return true;
      }

      return item.children.every((child) => checkedKeys.includes(child.key));
    };

    const parentNodes = getParentPath(editPermissionTree, key);

    // 更新每个父节点的状态
    let newHalfCheckedKeys = [...editHalfCheckedKeys];

    parentNodes.forEach((parent) => {
      // 检查是否有任何子节点或后代被选中
      const anyChildChecked = hasCheckedDescendant(parent, newCheckedKeys);

      if (!anyChildChecked) {
        // 如果没有任何子节点被选中，则移除父节点的选中和半选状态
        newHalfCheckedKeys = newHalfCheckedKeys.filter((k) => k !== parent.key);
      } else {
        // 如果有子节点被选中
        const allDirectChildrenChecked = areAllChildrenChecked(
          parent,
          newCheckedKeys
        );

        if (allDirectChildrenChecked) {
          // 如果所有直接子节点都被选中，则设置父节点为选中状态
          if (!newCheckedKeys.includes(parent.key)) {
            setEditCheckedKeys((prevState) => [...prevState, parent.key]);
          }
          newHalfCheckedKeys = newHalfCheckedKeys.filter(
            (k) => k !== parent.key
          );
        } else {
          // 如果只有部分子节点被选中，则设置为半选状态
          if (!newHalfCheckedKeys.includes(parent.key)) {
            newHalfCheckedKeys = [...newHalfCheckedKeys, parent.key];
          }
        }
      }
    });

    setEditHalfCheckedKeys(newHalfCheckedKeys);
  };

  // 自定义权限树下拉内容
  const permissionDropdownContent = (
    <div
      className="permission-dropdown-content"
      style={{ padding: "10px", minWidth: "300px" }}
    >
      <div style={{ marginBottom: "10px" }}>
        <Button
          type="primary"
          size="small"
          onClick={() => {
            const getAllKeys = (nodes) => {
              let keys = [];
              nodes.forEach((node) => {
                keys.push(node.key);
                if (node.children && node.children.length > 0) {
                  keys = [...keys, ...getAllKeys(node.children)];
                }
              });
              return keys;
            };
            const allKeys = getAllKeys(editPermissionTree);
            setEditCheckedKeys(allKeys);
            setEditHalfCheckedKeys([]);
          }}
          style={{ marginRight: "8px" }}
        >
          全选
        </Button>
        <Button
          size="small"
          onClick={() => {
            setEditCheckedKeys([]);
            setEditHalfCheckedKeys([]);
          }}
        >
          清空
        </Button>
      </div>
      <Divider style={{ margin: "8px 0" }} />
      {permissionLoading ? (
        <div style={{ textAlign: "center", padding: "20px" }}>
          <div className="loading-spinner"></div>
          <p>正在加载权限数据...</p>
        </div>
      ) : (
        <CustomTree
          treeData={editPermissionTree}
          checkedKeys={editCheckedKeys}
          halfCheckedKeys={editHalfCheckedKeys}
          onCheck={handleEditNodeCheck}
        />
      )}
    </div>
  );

  // 处理编辑角色提交
  const handleEditRoleSubmit = (values) => {
    // 准备请求参数
    const params = {
      roleName: values.name,
      description: values.description,
      parentRoleId: values.parentRoleId || "", // 从表单获取父级角色ID，如果为空则传入空字符串
      isSystemRole: values.type === "system" ? 1 : 0,
      priority: values.priority,
      tenantId: values.tenant,
      status: values.status,
      id: editRoleData.id, // 使用获取到的角色ID
      permissionIds: [...editCheckedKeys, ...editHalfCheckedKeys], // 添加权限IDs
    };

    // 调用更新角色API
    rolePermissionApi
      .updateRole(params)
      .then((response) => {
        if (response && response.code === 200) {
          message.success("角色编辑成功！");
          setEditModalVisible(false);
          editForm.resetFields();
          // 刷新角色列表
          fetchRoleList(pagination.current, pagination.pageSize, {});

          // 如果当前有选中的角色，且修改的就是该角色，更新权限树
          if (selectedRole && selectedRole.key === editRoleData.id) {
            fetchPermissionMenu(selectedRole.key);
          }
        }
      })
      .catch((error) => {
        console.error("角色编辑错误:", error);
      });
  };

  // 处理删除角色
  const handleDeleteRole = (record) => {
    Modal.confirm({
      title: "删除角色",
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除角色 "${record.name}" 吗？`,
      okText: "是",
      okType: "danger",
      cancelText: "否",
      centered: true,
      onOk() {
        // 调用删除角色API，传入ID数组
        const ids = [record.key];
        return rolePermissionApi
          .deleteRoles(ids)
          .then((response) => {
            if (response && response.code === 200) {
              message.success("角色删除成功！");
              // 刷新角色列表
              fetchRoleList(pagination.current, pagination.pageSize, {});
            }
          })
          .catch((error) => {
            console.error("角色删除错误:", error);
          });
      },
    });
  };

  // 处理批量删除角色
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的角色");
      return;
    }

    Modal.confirm({
      title: "批量删除角色",
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的${selectedRowKeys.length}个角色吗？`,
      okText: "是",
      okType: "danger",
      cancelText: "否",
      centered: true,
      onOk() {
        // 调用删除角色API，传入ID数组
        return rolePermissionApi
          .deleteRoles(selectedRowKeys)
          .then((response) => {
            if (response && response.code === 200) {
              message.success("角色删除成功！");
              // 清空选中项
              setSelectedRowKeys([]);
              // 刷新角色列表
              fetchRoleList(pagination.current, pagination.pageSize, {});
            }
          })
          .catch((error) => {
            console.error("角色删除错误:", error);
          });
      },
    });
  };

  // 重置过滤条件并查询
  const handleReset = () => {
    // 重置所有过滤条件
    setStatusFilter("all");
    setTypeFilter("all");
    setTenantFilter("all");
    setSearchValue("");

    // 调用接口重新查询，不带过滤条件
    fetchRoleList(1, pagination.pageSize, {});
  };

  // 搜索角色
  const handleSearch = () => {
    // 构建过滤参数
    const filters = {};

    // 添加状态过滤
    if (statusFilter !== "all") {
      filters.status = statusFilter;
    }

    // 添加类型过滤
    if (typeFilter !== "all") {
      filters.isSystemRole = typeFilter === "system" ? 1 : 0;
    }

    // 添加租户过滤
    if (tenantFilter !== "all") {
      filters.tenantId = tenantFilter;
    }

    // 添加角色名称过滤
    if (searchValue) {
      filters.roleName = searchValue;
    }

    // 调用接口进行查询，页码重置为1
    fetchRoleList(1, pagination.pageSize, filters);
  };

  // 处理查看权限
  const handleViewPermissions = (record) => {
    try {
      setLoading(true);
      // 调用 getRoleById 接口获取角色详情
      rolePermissionApi
        .getRoleById(record.key)
        .then((response) => {
          if (response && response.code === 200 && response.data) {
            // 获取角色详情成功，设置选中角色
            const roleData = response.data;
            setSelectedRole({
              ...record,
              tenantId: roleData.tenantId, // 确保使用接口返回的 tenantId
            });

            // 重置选中的权限
            setCheckedKeys([]);
            setHalfCheckedKeys([]);
            // 设置默认选择全部模块
            setSelectedModule("all");
            // 调用接口获取权限树
            fetchPermissionMenu(record.key);
            // 调用接口获取权限数量统计
            fetchPermissionNum(record.key);
          }
        })
        .catch((error) => {
          console.error("获取角色详情错误:", error);
        })
        .finally(() => {
          setLoading(false);
        });
    } catch (error) {
      console.error("处理查看权限错误:", error);
      setLoading(false);
    }
  };

  // 根据选中的模块过滤权限树
  const getFilteredPermissionTree = () => {
    if (selectedModule === "all") {
      // 如果选择"全部模块"，返回完整的权限树
      return permissionTree.length > 0 ? permissionTree : permissions;
    } else {
      // 当选择特定模块时，直接返回通过API获取的权限树数据
      return permissionTree.length > 0 ? permissionTree : [];
    }
  };

  // 获取权限下拉选择项（包括所有级别的权限）
  const getFlatPermissionOptions = () => {
    const options = [];

    // 递归函数，用于展平权限树
    const flattenPermissions = (items, parentPath = "") => {
      if (!Array.isArray(items)) return;

      items.forEach((item) => {
        // 添加当前权限项
        options.push({
          value: item.key,
          label: parentPath ? `${parentPath} / ${item.title}` : item.title,
        });

        // 递归处理子节点
        if (item.children && item.children.length > 0) {
          flattenPermissions(
            item.children,
            parentPath ? `${parentPath} / ${item.title}` : item.title
          );
        }
      });
    };

    // 处理权限树
    flattenPermissions(permissionTree);

    return options;
  };

  // 处理保存权限
  const handleSavePermission = () => {
    if (!selectedRole) {
      message.error("请先选择一个角色");
      return;
    }

    // 合并完全选中和半选中的键，并去重
    const allKeysToSend = new Set([...checkedKeys, ...halfCheckedKeys]);

    // 准备请求参数
    const params = {
      roleId: selectedRole.key,
      permissionIds: Array.from(allKeysToSend), // 发送合并后的权限 ID
      tenantId: selectedRole.tenantId,
    };

    // 显示加载状态
    setPermissionLoading(true);

    // 调用更新权限API
    rolePermissionApi
      .updateRolePermission(params)
      .then((response) => {
        if (response && response.code === 200) {
          message.success("权限更新成功！");
        }
      })
      .catch((error) => {
        console.error("权限更新错误:", error);
      })
      .finally(() => {
        setPermissionLoading(false);
      });
  };

  // 处理添加权限
  const handleAddPermission = (values) => {
    // 准备请求参数
    const params = {
      parentId: values.parentId || "",
      name: values.name,
      description: values.description || "",
      isSystemPermission: values.isPredefined ? 1 : 0,
      status: values.status,
      resourceType: values.resourceType,
      operationType: 1,
      tenantId: values.tenant,
      sortOrder: values.sortOrder || 0, // 添加权限排序字段
      metadata: {
        api: values.resourceType === "api" ? values.api : "",
        menu: values.resourceType === "menu" ? values.menu : "",
        button: values.resourceType === "button" ? values.button : "",
      },
    };

    // 调用添加权限API
    rolePermissionApi
      .addPermission(params)
      .then((response) => {
        if (response && response.code === 200) {
          message.success("权限添加成功！");
          setAddPermissionVisible(false);
          permissionForm.resetFields();
          // 刷新权限树
          if (selectedRole) {
            fetchPermissionMenu(selectedRole.key);
          }
        }
      })
      .catch((error) => {
        console.error("权限添加错误:", error);
      });
  };

  // 处理权限添加弹窗打开
  const handleOpenAddPermissionModal = () => {
    // 确保选中了角色
    if (!selectedRole) {
      message.error("请先选择一个角色");
      return;
    }

    // 重置当前选择的资源类型
    setCurrentResourceType("");

    // 直接打开权限添加弹窗，无需重新获取权限树
    setAddPermissionVisible(true);
  };

  // 添加获取租户列表的方法
  const fetchTenantList = async (selectedTenantId) => {
    try {
      const response = await rolePermissionApi.getTenantDropdown({
        tenantId: selectedTenantId,
      });
      if (response && response.code === 200 && response.data) {
        setTenantList(response.data);
      }
    } catch (error) {
      console.error("获取租户列表错误:", error);
    }
  };

  // 在组件加载时获取租户列表
  useEffect(() => {
    fetchTenantList("");
  }, []);

  // 处理节点选中状态变化
  const handleNodeCheck = (node, checked) => {
    // 创建新的选中键列表
    let newCheckedKeys = [...checkedKeys];

    // 获取所有子节点的key
    const getAllChildKeys = (item) => {
      let keys = [item.key];
      if (item.children && item.children.length > 0) {
        item.children.forEach((child) => {
          keys = [...keys, ...getAllChildKeys(child)];
        });
      }
      return keys;
    };

    // 获取父节点路径
    const getParentPath = (data, targetKey, path = []) => {
      for (const item of data) {
        if (item.key === targetKey) {
          return path;
        }

        if (item.children && item.children.length > 0) {
          const foundPath = getParentPath(item.children, targetKey, [
            ...path,
            item,
          ]);
          if (foundPath.length) {
            return foundPath;
          }
        }
      }

      return [];
    };

    // 检查节点下是否有任何子节点被选中（递归检查整个子树）
    const hasCheckedDescendant = (item, checkedKeys) => {
      if (!item.children || item.children.length === 0) {
        return checkedKeys.includes(item.key);
      }

      return item.children.some((child) => {
        const childChecked = checkedKeys.includes(child.key);
        const descendantChecked =
          child.children && child.children.length > 0
            ? hasCheckedDescendant(child, checkedKeys)
            : false;
        return childChecked || descendantChecked;
      });
    };

    // 检查节点及其所有后代是否都被选中
    const areAllDescendantsChecked = (item, checkedKeys) => {
      // 检查当前节点是否选中
      if (!checkedKeys.includes(item.key)) {
        return false;
      }

      // 如果没有子节点，则返回true
      if (!item.children || item.children.length === 0) {
        return true;
      }

      // 检查所有子节点是否都被选中
      return item.children.every((child) =>
        areAllDescendantsChecked(child, checkedKeys)
      );
    };

    if (checked) {
      // 选中节点逻辑 - 选中节点及其所有子节点
      const childKeys = getAllChildKeys(node);
      newCheckedKeys = [...new Set([...newCheckedKeys, ...childKeys])];

      // 处理父节点选中状态
      const parentNodes = getParentPath(getFilteredPermissionTree(), node.key);

      // 更新每个父节点的状态
      parentNodes.forEach((parent) => {
        // 获取所有直接子节点的key
        const childKeys = parent.children.map((child) => child.key);

        // 检查是否所有直接子节点都被选中
        const allDirectChildrenChecked = childKeys.every((key) =>
          newCheckedKeys.includes(key)
        );

        // 检查是否所有子节点及其后代都被选中
        const allDescendantsOfChildrenChecked = parent.children.every(
          (child) => {
            if (!child.children || child.children.length === 0) {
              return newCheckedKeys.includes(child.key);
            }
            return areAllDescendantsChecked(child, newCheckedKeys);
          }
        );

        // 检查是否有任何子节点被选中（包括深层后代）
        const anyChildChecked = parent.children.some((child) =>
          hasCheckedDescendant(child, newCheckedKeys)
        );

        if (allDirectChildrenChecked && allDescendantsOfChildrenChecked) {
          // 如果所有子节点和所有后代都被选中，设置父节点为完全选中状态
          if (!newCheckedKeys.includes(parent.key)) {
            newCheckedKeys.push(parent.key);
          }
          setHalfCheckedKeys((prevState) =>
            prevState.filter((key) => key !== parent.key)
          );
        } else if (anyChildChecked) {
          // 如果有任何子节点被选中但不是所有，则设置父节点为半选状态
          newCheckedKeys = newCheckedKeys.filter((key) => key !== parent.key);
          if (!halfCheckedKeys.includes(parent.key)) {
            setHalfCheckedKeys((prevState) => [...prevState, parent.key]);
          }
        } else {
          // 如果没有子节点被选中，取消父节点的选中和半选状态
          newCheckedKeys = newCheckedKeys.filter((key) => key !== parent.key);
          setHalfCheckedKeys((prevState) =>
            prevState.filter((key) => key !== parent.key)
          );
        }
      });
    } else {
      // 取消选中节点逻辑 - 取消选中节点及其所有子节点
      const childKeys = getAllChildKeys(node);
      newCheckedKeys = newCheckedKeys.filter((key) => !childKeys.includes(key));

      // 处理父节点选中状态
      const parentNodes = getParentPath(getFilteredPermissionTree(), node.key);

      // 更新每个父节点的状态
      parentNodes.forEach((parent) => {
        // 获取所有直接子节点的key
        const childKeys = parent.children.map((child) => child.key);

        // 检查是否所有直接子节点都被选中
        const allDirectChildrenChecked = childKeys.every((key) =>
          newCheckedKeys.includes(key)
        );

        // 检查是否所有子节点及其后代都被选中
        const allDescendantsOfChildrenChecked = parent.children.every(
          (child) => {
            if (!child.children || child.children.length === 0) {
              return newCheckedKeys.includes(child.key);
            }
            return areAllDescendantsChecked(child, newCheckedKeys);
          }
        );

        // 检查是否有任何子节点被选中（包括深层后代）
        const anyChildChecked = parent.children.some((child) =>
          hasCheckedDescendant(child, newCheckedKeys)
        );

        if (allDirectChildrenChecked && allDescendantsOfChildrenChecked) {
          // 如果所有子节点和所有后代都被选中，设置父节点为完全选中状态
          if (!newCheckedKeys.includes(parent.key)) {
            newCheckedKeys.push(parent.key);
          }
          setHalfCheckedKeys((prevState) =>
            prevState.filter((key) => key !== parent.key)
          );
        } else if (anyChildChecked) {
          // 如果有任何子节点被选中但不是所有，则设置父节点为半选状态
          newCheckedKeys = newCheckedKeys.filter((key) => key !== parent.key);
          if (!halfCheckedKeys.includes(parent.key)) {
            setHalfCheckedKeys((prevState) => [...prevState, parent.key]);
          }
        } else {
          // 如果没有子节点被选中，取消父节点的选中和半选状态
          newCheckedKeys = newCheckedKeys.filter((key) => key !== parent.key);
          setHalfCheckedKeys((prevState) =>
            prevState.filter((key) => key !== parent.key)
          );
        }
      });
    }

    setCheckedKeys(newCheckedKeys);
  };

  return (
    <div className="role-permission">
      {/* Filter Bar */}
      <Card className="filter-bar">
        <Space wrap>
          <label>角色状态:</label>
          <Select
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: 120 }}
          >
            <Option value="all">全部状态</Option>
            <Option value="active">活跃</Option>
            <Option value="inactive">禁用</Option>
          </Select>
          <label>角色类型:</label>
          <Select
            value={typeFilter}
            onChange={setTypeFilter}
            style={{ width: 120 }}
          >
            <Option value="all">全部类型</Option>
            <Option value="system">系统角色</Option>
            <Option value="custom">自定义角色</Option>
          </Select>
          <label>租户:</label>
          <Select
            value={tenantFilter}
            onChange={setTenantFilter}
            style={{ width: 120 }}
          >
            <Option value="all">全部租户</Option>
            {tenantList.map((tenant) => (
              <Option key={tenant.id} value={tenant.id}>
                {tenant.name}
              </Option>
            ))}
          </Select>
          <label>搜索:</label>
          <Input
            placeholder="搜索角色名称..."
            style={{ width: 200 }}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            allowClear
          />
          <Button
            type="primary"
            icon={<SearchOutlined />}
            onClick={handleSearch}
          >
            搜索
          </Button>
          <Button icon={<ReloadOutlined />} onClick={handleReset}>
            重置
          </Button>
          <Button
            danger
            icon={<DeleteOutlined />}
            onClick={handleBatchDelete}
            disabled={selectedRowKeys.length === 0}
          >
            批量删除
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            添加角色
          </Button>
        </Space>
      </Card>

      {/* Role List */}
      <Card className="role-list">
        <Table
          columns={columns}
          dataSource={roles}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            getCheckboxProps: (record) => ({
              disabled: record.isSystemRole === 1,
            }),
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ y: roles.length > 5 ? 350 : undefined }}
          loading={loading}
          onChange={(pagination) => {
            handleTableChange(pagination.current, pagination.pageSize);
          }}
        />
      </Card>

      {/* Permission Assignment */}
      {selectedRole && (
        <Card
          title={`所选角色: ${selectedRole.name}`}
          extra={
            <Space>
              <span>
                已分配: {permissionCount.assigned}/{permissionCount.total}
              </span>
              <Button
                icon={<PlusOutlined />}
                onClick={handleOpenAddPermissionModal}
              >
                添加权限
              </Button>
              <Button
                type="primary"
                onClick={handleSavePermission}
                loading={permissionLoading}
              >
                保存
              </Button>
            </Space>
          }
          className="permission-card"
        >
          <div className="permission-header">
            <Space>
              <span>模块浏览:</span>
              <Radio.Group
                value={selectedModule}
                onChange={(e) => {
                  setSelectedModule(e.target.value);
                  if (selectedRole) {
                    setPermissionLoading(true);
                    rolePermissionApi
                      .getPermissionTree({
                        permissionId:
                          e.target.value === "all" ? "" : e.target.value,
                        roleId: selectedRole.key,
                      })
                      .then((response) => {
                        if (
                          response &&
                          response.code === 200 &&
                          response.data
                        ) {
                          const selectedKeys = extractCheckedPermissions(
                            response.data
                          );
                          setCheckedKeys(selectedKeys);
                          const transformedData = transformPermissionData(
                            response.data
                          );
                          setPermissionTree(transformedData);
                        }
                      })
                      .catch((error) => {
                        console.error("获取权限树错误:", error);
                      })
                      .finally(() => {
                        setPermissionLoading(false);
                      });
                  }
                }}
              >
                {modules.map((module) => (
                  <Radio.Button key={module.value} value={module.value}>
                    {module.label}
                  </Radio.Button>
                ))}
              </Radio.Group>
            </Space>
          </div>

          <div
            className="permission-actions"
            style={{ marginTop: "10px", marginBottom: "10px" }}
          >
            <Space>
              <Button
                type={selectedAction === "selectAll" ? "primary" : "default"}
                htmlType="button"
                onClick={() => {
                  const getAllKeys = (nodes) => {
                    let keys = [];
                    nodes.forEach((node) => {
                      keys.push(node.key);
                      if (node.children && node.children.length > 0) {
                        keys = [...keys, ...getAllKeys(node.children)];
                      }
                    });
                    return keys;
                  };
                  const allKeys = getAllKeys(getFilteredPermissionTree());
                  setCheckedKeys(allKeys);
                  setHalfCheckedKeys([]);
                  setSelectedAction("selectAll"); // 更新选中状态
                }}
              >
                全选
              </Button>
              <Button
                type={selectedAction === "selectNone" ? "primary" : "default"}
                htmlType="button"
                onClick={() => {
                  setCheckedKeys([]);
                  setHalfCheckedKeys([]);
                  setSelectedAction("selectNone"); // 更新选中状态
                }}
              >
                全不选
              </Button>
            </Space>
          </div>

          <div
            className="permission-checkbox-list"
            style={{ height: "290px", overflowY: "auto", overflowX: "hidden" }}
          >
            {permissionLoading ? (
              <div className="permission-loading">
                <div className="loading-spinner"></div>
                <p>正在加载权限数据...</p>
              </div>
            ) : (
              <CustomTree
                treeData={getFilteredPermissionTree()}
                checkedKeys={checkedKeys}
                halfCheckedKeys={halfCheckedKeys}
                onCheck={handleNodeCheck}
              />
            )}
          </div>
        </Card>
      )}

      {/* Create Role Modal */}
      <Modal
        title="添加角色"
        open={createModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        width={720}
        centered={true}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateRole}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="角色名称"
                rules={[{ required: true }]}
              >
                <Input placeholder="请输入角色名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true }]}
              >
                <Select>
                  <Option value={2}>高</Option>
                  <Option value={1}>中</Option>
                  <Option value={0}>低</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="角色类型"
                rules={[{ required: true }]}
              >
                <Radio.Group>
                  <Radio value="system">系统角色</Radio>
                  <Radio value="custom">自定义角色</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="status" label="状态" initialValue="active">
                <Radio.Group>
                  <Radio value="active">启用</Radio>
                  <Radio value="disabled">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="tenant"
                label="所属租户"
                rules={[{ required: true }]}
              >
                <Select>
                  {tenantList.map((tenant) => (
                    <Option key={tenant.id} value={tenant.id}>
                      {tenant.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="parentRoleId" label="父级角色">
                <Select placeholder="请选择父角色">
                  <Option value="">无</Option>
                  {roles.map((role) => (
                    <Option key={role.key} value={role.key}>
                      {role.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="角色描述">
            <Input.TextArea rows={2} placeholder="请输入角色描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit Role Modal */}
      <Modal
        title="编辑角色"
        open={editModalVisible}
        onOk={() => editForm.submit()}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
        }}
        width={720}
        centered={true}
      >
        <Form form={editForm} layout="vertical" onFinish={handleEditRoleSubmit}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="角色名称"
                rules={[{ required: true }]}
              >
                <Input
                  placeholder="请输入角色名称"
                  disabled={editingRoleStatus !== "active"}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="parentRoleId" label="父级角色">
                <Select
                  placeholder="请选择父级角色"
                  disabled={editingRoleStatus !== "active"}
                >
                  <Option value="">无</Option>
                  {roles.map((role) => (
                    <Option key={role.key} value={role.key}>
                      {role.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="tenant"
                label="所属租户"
                rules={[{ required: true }]}
              >
                <Select disabled={editingRoleStatus !== "active"}>
                  {tenantList.map((tenant) => (
                    <Option key={tenant.id} value={tenant.id}>
                      {tenant.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true }]}
              >
                <Select disabled={editingRoleStatus !== "active"}>
                  <Option value={2}>高</Option>
                  <Option value={1}>中</Option>
                  <Option value={0}>低</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="角色类型"
                rules={[{ required: true }]}
              >
                <Radio.Group disabled={editingRoleStatus !== "active"}>
                  <Radio value="system">系统角色</Radio>
                  <Radio value="custom">自定义角色</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="status" label="状态" initialValue="active">
                <Radio.Group>
                  <Radio value="active">启用</Radio>
                  <Radio value="disabled">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>
          {/* <Form.Item
            name="permission"
            label="权限"
          >
            <Dropdown
              menu={{ content: permissionDropdownContent }}
              open={permissionDropdownVisible}
              onOpenChange={setPermissionDropdownVisible}
              trigger={['click']}
              dropdownRender={() => permissionDropdownContent}
              overlayStyle={{
                maxWidth: '672px', // 720px(modal width) - 24px(padding) * 2
                maxHeight: '400px',
                marginTop: '-120px'
              }}
            >
              <div
                style={{
                  border: '1px solid #d9d9d9',
                  borderRadius: '2px',
                  backgroundColor: '#fff',
                  width: '100%',
                  cursor: 'pointer'
                }}
                onClick={() => setPermissionDropdownVisible(true)}
              >
                {editCheckedKeys.length > 0 ? (
                  getSelectedPermissionTags(editCheckedKeys, editPermissionTree, handleRemovePermissionTag)
                ) : (
                  <div style={{ padding: '4px 11px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ color: '#bfbfbf' }}>请选择权限</span>
                    <DownOutlined />
                  </div>
                )}
              </div>
            </Dropdown>
          </Form.Item> */}

          <Form.Item name="description" label="角色描述">
            <Input.TextArea
              rows={3}
              placeholder="请输入角色描述"
              disabled={editingRoleStatus !== "active"}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Add Permission Modal */}
      <Modal
        title="添加权限"
        open={addPermissionVisible}
        onOk={() => permissionForm.submit()}
        onCancel={() => {
          setAddPermissionVisible(false);
          permissionForm.resetFields();
        }}
        width={720}
        centered={true}
      >
        <Form
          form={permissionForm}
          layout="vertical"
          onFinish={handleAddPermission}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="权限名称"
                rules={[{ required: true }]}
              >
                <Input placeholder="请输入权限名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="parentId" label="上级权限">
                <Select placeholder="请选择上级权限">
                  <Option value="">无</Option>
                  {getFlatPermissionOptions().map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="isPredefined"
                label="是否为预定权限"
                initialValue={false}
              >
                <Radio.Group>
                  <Radio value={true}>是</Radio>
                  <Radio value={false}>否</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="status" label="状态" initialValue="active">
                <Radio.Group>
                  <Radio value="active">启用</Radio>
                  <Radio value="disabled">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="tenant"
                label="所属租户"
                rules={[{ required: true }]}
              >
                <Select placeholder="请选择所属租户">
                  {tenantList.map((tenant) => (
                    <Option key={tenant.id} value={tenant.id}>
                      {tenant.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="resourceType"
                label="资源类型"
                rules={[{ required: true }]}
              >
                <Select
                  placeholder="请选择资源类型"
                  onChange={(value) => setCurrentResourceType(value)}
                >
                  <Option value="api">api</Option>
                  <Option value="menu">菜单</Option>
                  <Option value="button">按钮</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="api" label="API路径">
                <Input
                  placeholder="请输入api路径"
                  disabled={currentResourceType !== "api"}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="menu" label="菜单路径">
                <Input
                  placeholder="请输入菜单路径"
                  disabled={currentResourceType !== "menu"}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="button" label="按钮路径">
                <Input
                  placeholder="请输入按钮路径"
                  disabled={currentResourceType !== "button"}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="sortOrder"
                label="权限排序"
                initialValue={0}
                tooltip="数字越小排序越靠前"
              >
                <InputNumber
                  placeholder="请输入排序值"
                  style={{ width: "100%" }}
                  min={0}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="description" label="权限描述">
                <Input.TextArea rows={1} placeholder="请输入权限描述" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default RolePermission;
