
.mouse-activity {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.filter-bar {
  background: #fff;
  border-radius: 4px;
}

.statistics-cards .ant-card {
  border-radius: 8px;
}

.trend-info {
  margin-top: 8px;
  font-size: 14px;
}

.mouse-active-content{
  width: 100%;
  display: flex;
  height: calc(100% - 56px);
  /* overflow-x: auto; */
}

.mouse-active-side{
  width: 300px;
  margin-right: 24px;
  background-color: #fff;
  border-radius: 5px;
  padding: 5px;
}

.mouse-active-content-chart{
  width: 100%;
  /* overflow-y: auto; */
}

.mouse-active-content-chart .ant-tabs-nav{
  width: 100%;
  background-color: #fff;
  padding: 5px;
  border-radius: 8px;
}

.mouse-active-content-chart .ant-tabs-content{
  height: 100%;
}

.mouse-active-content-chart .ant-tabs-content .ant-tabs-tabpane{
  height: 100%;
}

.mouse-active-content-chart .ant-tabs-content .ant-tabs-tabpane .ant-card{
  height: 100%;
}


.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-label {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.sub-title {
  margin-top: 4px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.chart-summary {
  text-align: center;
  margin-top: 16px;
  height: 22px;
  color: rgba(0, 0, 0, 0.65);
}

.insight-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.insight-list li {
  margin-bottom: 12px;
  padding-left: 20px;
  position: relative;
}

.insight-list li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #1890ff;
}

.data-footer {
  margin-top: auto;
}

.data-footer .separator {
  margin: 0 8px;
  color: rgba(0, 0, 0, 0.25);
}

/* Responsive styles */
@media (max-width: 768px) {
  .mouse-activity {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
  }

  .statistics-cards .ant-col {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
