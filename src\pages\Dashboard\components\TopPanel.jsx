import { Row, Col, Card, Statistic, List, Tag } from 'antd'
import { HeartTwoTone, AlertOutlined, WarningOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs';

const DeviceLogo = () => (
  <img src='/device_logo.png' alt='Device Logo' style={{ paddingTop: 10,marginRight: 10 }} />
)

// 获取告警级别对应的颜色
const getLevelColor = (level) => {
  if (!level && level !== 0) return 'default'

  // Handle both string and numeric level values
  const colorMap = {
    '低': 'blue',
    '中': 'orange',
    '高': 'gold',
    '紧急': 'red',
    // Numeric mappings (from AlertInfo component)
    '0': 'blue',
    '1': 'orange',
    '2': 'gold',
    '3': 'red'
  }

  return colorMap[String(level)] || 'default'
}

// 获取告警级别文本
const getLevelText = (level) => {
  if (!level && level !== 0) return '未知'

  // Handle both string and numeric level values
  const textMap = {
    '0': '低',
    '1': '中',
    '2': '高',
    '3': '紧急'
  }

  // If it's a number, convert to text representation
  return textMap[String(level)] || level || '未知'
}

const TopPanel = ({ onlineRate, totalDevices, healthScore, alarms }) => {
  const navigate = useNavigate()

  // Handle alarms data - if it's a number, convert to display format
  // If it's an array, use it directly; if it's a number, create a simple display
  const safeAlarms = Array.isArray(alarms) ? alarms : []
  const alarmCount = typeof alarms === 'number' ? alarms : (Array.isArray(alarms) ? alarms.reduce((sum, item) => sum + (item.count || 0), 0) : 0)
  return (
    <Row gutter={[16, 16]}>
      <Col span={8}>
        <Card className='dashboard-card dashboard-card-1'  onClick={() =>{
            navigate('/device/repellent')
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Statistic
              title='设备总数'
              value={totalDevices}
              prefix={<DeviceLogo />}
            />
            <div style={{ textAlign: 'right', marginTop: '8px' }}>
              <div style={{ fontSize: '14px', color: '#8c8c8c', marginBottom: '4px' }}>
                在线率
              </div>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                {onlineRate ? onlineRate?.toFixed(2) : 0}%
              </div>
            </div>
          </div>
        </Card>
      </Col>
      {/* <Col span={6}>
        <Card className="dashboard-card">
          <Statistic
            title="客户数量"
            value={256}
            prefix={<TeamOutlined />}
            suffix={
              <div style={{ fontSize: '14px', marginTop: '8px' }}>
                <span style={{ color: '#52c41a' }}>↑ 12% 同比增长</span>
              </div>
            }
          />
        </Card>
      </Col> */}
      <Col span={8}>
        <Card className='dashboard-card dashboard-card-2' onClick={() =>{
            navigate('/device/repellent')
        }}>
          <Statistic
            title='设备健康度'
            value={healthScore ? healthScore : 0}
            prefix={<HeartTwoTone twoToneColor='#eb2f96' />}
            suffix="/ 100"
          />
        </Card>
      </Col>
      <Col span={8}>
        <Card
          className="dashboard-card dashboard-card-4"
          onClick={() =>{
            navigate('/alerts/info',
              {
                state: {
                  startTime: dayjs().startOf('month').format("YYYY-MM-DD HH:mm:ss"),
                  endTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
                }
              }
            )
        }}>
          <Statistic
            title='本月告警总数'
            value={alarmCount || 0}
            prefix={<AlertOutlined style={{ color: '#ff4d4f' }} />}
          />
        </Card>
      </Col>
    </Row>
  )
}

export default TopPanel
