import { getLoginEntry } from '../utils/loginLogger';
// API基础URL - 可以根据实际部署环境手动配置
import { API_PREFIX } from "./constant";
import { message } from 'antd';

const handleApiError = (response) => {
  return response.json().then(data => {
    if (response.status === 401 ) {
      message.error(data?.message);
      setTimeout(() => {
        window.location.href = '/login'
      }, 500);
    } else if (response.status === 400 || response.status === 404 || response.status === 500) {
      message.error(data?.message);
    } else if (response.status === 403) {
      // Handle forbidden
    }
    return Promise.reject(data);
  });
};

export const layoutApi = {
  logout: async () => {
    // 从localStorage获取最新的登录数据
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/auth/logout`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },

  // 获取文件预签名URL
  getFilePresignedUrl: async (fileId) => {
    // 从localStorage获取最新的登录数据
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/file/presigned-url/${fileId}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      }
    }).then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },

  // 获取用户菜单
  getUserMenu: async () => {
    // 从localStorage获取最新的登录数据
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/role-permission/user/menu/tree`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      }
    }).then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  }
};

