.tenant-list {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-switch {
  margin-left: 16px;
}

.filter-bar {
  margin-bottom: 24px;
}

.filter-bar .ant-form {
  margin-bottom: -24px;
}

.statistics-cards {
  margin-bottom: 24px;
}

.statistics-cards .ant-card {
  border-radius: 8px;
}

.trend-info {
  margin-top: 8px;
  font-size: 14px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-label {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.expanded-content {
  padding: 16px;
  background: #fafafa;
}

.expanded-content .ant-card {
  background: #fff;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .statistics-cards .ant-col {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 768px) {
  .tenant-list {
    padding: 16px;
  }

  .header-title,
  .header-actions {
    flex-direction: column;
    gap: 16px;
  }

  .header-actions .ant-space {
    width: 100%;
  }

  .header-actions .ant-input-search {
    width: 100%;
  }

  .statistics-cards .ant-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .filter-bar .ant-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .filter-bar .ant-form-item {
    margin-right: 0;
    width: 100%;
  }
}