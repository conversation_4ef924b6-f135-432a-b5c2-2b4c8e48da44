import { useState, useEffect, useRef } from "react";
import {
  Layout,
  Tabs,
  Button,
  Input,
  Space,
  Table,
  Tag,
  Tooltip,
  Tree,
  Modal,
  Form,
  Select,
  DatePicker,
  Row,
  Col,
  TreeSelect,
  message,
  Drawer,
  AutoComplete,
} from "antd";
import dayjs from "dayjs";
import { debounce, set } from "lodash";
import {
  GlobalOutlined,
  TeamOutlined,
  EditOutlined,
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  ArrowRightOutlined
} from "@ant-design/icons";
import {
  getDeviceGroupsByLocation,
  getCustomerGroupsTree,
} from "../../service/deviceGroups";
import {
  createCamera,
  updateCamera,
  getCameraList,
  deleteCamerabyIds,
  simulateRatEvent,
  getCameraLikeCameraNo,
  getCameraDetail,
} from "../../service/deviceCamera";
import "./styles.css";
import useSelect from "../../hooks/useSelect";
import {
  getNationalStandardInformationType,
  getCameraType,
} from "../../service/dictionary";
import { useNavigate, useLocation } from "react-router-dom";
import AdvancedSearch from "../../components/advancedSearch";
import { use } from "react";

const { Sider, Content } = Layout;

const DeviceCamera = () => {
  const [activeTab, setActiveTab] = useState("location"); // 当前选中的tab
  const [selectedKeys, setSelectedKeys] = useState([]); // 选中的节点
  const [expandedKeys, setExpandedKeys] = useState([]); // 展开的节点
  const [treeData, setTreeData] = useState(null); // 树数据
  const [searchParams, setSearchParams] = useState({
    page: 0,
    size: 10,
    isNotGroup: false,
    keyword: "",
    groupType: "", // 0是行政区域，1是设备分组
    groupId: "", // 地理分组
    orgId: "", // 客户分组
  });
  const [total, setTotal] = useState(0); // 总条数
  const [isModalVisible, setIsModalVisible] = useState(false); // 是否显示新增/编辑弹窗
  const [editingRecord, setEditingRecord] = useState(null); // 编辑的记录
  const [form] = Form.useForm(); // 表单实例
  const [searchForm] = Form.useForm(); // 搜索表单实例
  const [locationTreeData, setLocationTreeData] = useState([]); // 位置树数据
  const [customerListData, setCustomerListData] = useState([]); // 客户树数据
  const [cameraData, setCameraData] = useState([]); // 摄像头列表数据
  const [deleteModalVisible, setDeleteModalVisible] = useState(false); // 删除确认弹窗
  const [deleteCamera, setDeleteCamera] = useState(null); // 要删除的摄像头
  const [selectedRowKeys, setSelectedRowKeys] = useState([]); // 选中的行keys
  const [batchDeleteModalVisible, setBatchDeleteModalVisible] = useState(false); // 批量删除确认弹窗
  const [viewModalVisible, setViewModalVisible] = useState(false); // 查看弹窗
  const [viewingCamera, setViewingCamera] = useState(null); // 查看的摄像头数据
  const [gbData, setGbData] = useState(null); // 国标信息
  const [cameraTypeData, setCameraTypebData] = useState(null); // 摄像头类型
  const [autocompleteOptions, setAutocompleteOptions] = useState([]); // 存储自动完成选项
  const [
    node,
    searchValue,
    autoExpandParent,
    filteredTreeData,
    setAutoExpandParent,
  ] = useSelect(treeData, setExpandedKeys);
  const lockVersionRef = useRef();
  const navigate = useNavigate();
  const location = useLocation();
  const [advancedSearchVisible, setAdvancedSearchVisible] = useState(false); // 高级搜索弹窗显示隐藏
  const [deviceStatusModalVisible, setDeviceStatusModalVisible] =
    useState(false); // 设备状态弹窗显示隐藏
  const [currentCameraDevices, setCurrentCameraDevices] = useState([]); // 当前摄像头的设备列表
  const [loading, setLoading] = useState(false); // 加载中

  // 获取位置树
  const getLocationTree = async () => {
    setLoading(true);
    try {
      const res = await getDeviceGroupsByLocation(1);
      const key = [];
      const transformNode = (item, isFlag = false) => {
        key.push(item.id || item.locationId);
        return {
          title: item.groupName || item.description,
          key: item.id || item.locationId,
          value: item.id || item.locationId,
          isLocal: item.isLocal,
          ...(isFlag && { selectable: !!item.isLocal }),
          children:
            item.children && item.children.length > 0
              ? item.children.map((child) => transformNode(child, isFlag))
              : item.deviceLocationTreeVos &&
                item.deviceLocationTreeVos.length > 0
              ? item.deviceLocationTreeVos.map((child) =>
                  transformNode(child, isFlag)
                )
              : undefined,
        };
      };

      const transformedData = res.map((item) => transformNode(item));
      setTreeData([
        ...transformedData,
        { title: "未分组", key: "isNotGroup", isLocal: 0 },
      ]);
      setLocationTreeData(res.map((item) => transformNode(item, true))); // 弹窗选择数据
      setExpandedKeys(key);
      if (!location.state) {
        setSelectedKeys([key[0]]);
        const tempObj = {
          groupId: key[0],
          groupType: 0,
          orgId: "",
          page: 0,
        };
        updateSeachParam(tempObj);
        fetchCameraList(tempObj);
      }
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch location tree:", error);
    }
  };

  // 获取客户分组树
  const fetchCustomerGroupsTree = async () => {
    setLoading(true);
    try {
      const res = await getCustomerGroupsTree();
      const transformNode = (item) => ({
        title: item.ownershipName,
        key: item.ownershipId,
        value: item.ownershipId,
        children:
          item.children && item.children.length > 0
            ? item.children.map((child) => transformNode(child))
            : undefined,
      });
      const transformedData = res.map((item) => transformNode(item));
      setTreeData([
        ...transformedData,
        { title: "未分组", key: "isNotGroup", isLocal: 0 },
      ]);
      if (!location.state) {
        setSelectedKeys([transformedData[0]?.key || "isNotGroup"]); // 设置当前选中的
        const tempObj = {
          page: 0,
          orgId: transformedData[0]?.key || "isNotGroup",
          groupType: "",
          groupId: "",
        };
        updateSeachParam(tempObj);
        fetchCameraList(tempObj);
      }
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch customer groups tree:", error);
    }
  };

  // 获取客户数据
  const fetchCustomerData = async () => {
    if (customerListData.length === 0) {
      try {
        const res = await getCustomerGroupsTree();
        const transformNode = (item) => ({
          title: item.ownershipName,
          key: item.ownershipId,
          value: item.ownershipId,
          children:
            item.children && item.children.length > 0
              ? item.children.map((child) => transformNode(child))
              : undefined,
        });
        const transformedData = res.map((item) => transformNode(item));
        setCustomerListData(transformedData);
      } catch (error) {
        console.error("Failed to fetch customer groups tree:", error);
      }
    }
  };

  // 获取摄像头列表
  const fetchCameraList = async (obj = {}) => {
    setLoading(true);
    try {
      const res = await getCameraList({
        ...searchParams,
        ...obj,
      });
      setCameraData(res.content);
      setTotal(res.totalElements);
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch camera list:", error);
    }
  };

  //更新搜索条件
  const updateSeachParam = (obj) => {
    setSearchParams((pre) => ({
      ...pre,
      ...obj,
    }));
  };

  //跳转驱鼠器
  const handleGetDevice = (record) =>{
    // 获取选择摄像头的编号，跳转到DeviceRepellent页面
    navigate('/device/repellent', {
      replace: true,
      state: {
        cameraNo: record.cameraNo
      }
    });
  }

  useEffect(() => {
    (async () => {
      if (!treeData) {
        await getLocationTree();
      }

      if (location.state) {
        const { isNotGroup, keyword, groupType, groupId, orgId } =
          location.state;
        if (orgId || groupType === "") {
          setActiveTab("customer");
          await fetchCustomerGroupsTree();
        }
        const tempObj = {
          isNotGroup: !isNotGroup,
          keyword,
          groupType,
          groupId,
          orgId,
          page: 0,
        };
        setSelectedKeys([groupId || orgId || "isNotGroup"]);

        searchForm.setFieldsValue({
          keyword,
        });
        updateSeachParam(tempObj);
        fetchCameraList(tempObj);
        navigate(".", { replace: true, state: null });
      }
    })();
  }, [location.state]);

  useEffect(() => {
    // 清理函数，在组件卸载时取消请求
    return () => {
      if (window.cancelToken) {
        window.cancelToken(); // 取消请求
      }
    };
  }, []);

  // Table columns configuration
  const columns = [
    {
      title: "摄像头编号",
      dataIndex: "cameraNo",
      key: "cameraNo",
      ellipsis: true,
      render: (text, record) => (
        <a onClick={() => handleViewCamera(record)}>{text}</a>
      ),
    },
    {
      title: "摄像头名称",
      dataIndex: "cameraName",
      key: "cameraName",
      ellipsis: true,
    },
    {
      title: "绑定驱鼠器",
      dataIndex: "onlineDeviceStatus",
      key: "onlineDeviceStatus",
      ellipsis: true,
      render: (text, record) => {
        const statusMap = {
          0: "全部离线",
          1: "部分在线",
          2: "全部在线",
          3: "无绑定设备",
        };
        return (
          <a
            onClick={() => handleDeviceStatusClick(record)}
            style={{ color: "#1890ff", cursor: "pointer" }}
          >
            {statusMap[text] || "-"}
          </a>
        );
      },
    },
    {
      title: "摄像头类型",
      dataIndex: "cameraType",
      key: "cameraType",
      ellipsis: true,
      render: (text) => {
        // cameraTypeData 可能为 null，需判空
        if (!cameraTypeData) return text;
        const found = cameraTypeData.find((item) => item.itemCode === text);
        return found ? found.itemName : "-";
      },
    },
    // {
    //   title: '摄像头状态',
    //   dataIndex: 'cameraStatus',
    //   key: 'cameraStatus',
    //   ellipsis: true,
    //   render: status => (
    //     <Tag
    //       color={
    //         status === 'online'
    //           ? 'success'
    //           : status === 'fault'
    //           ? 'error'
    //           : 'default'
    //       }
    //     >
    //       {status === 'online' ? '在线' : status === 'fault' ? '故障' : '离线'}
    //     </Tag>
    //   )
    // },
    {
      title: "摄像头型号",
      dataIndex: "cameraModel",
      key: "cameraModel",
      ellipsis: true,
    },
    {
      title: "摄像头品牌",
      dataIndex: "cameraBrand",
      key: "cameraBrand",
      ellipsis: true,
    },
    // {
    //   title: '分辨率',
    //   dataIndex: 'cameraResolution',
    //   key: 'cameraResolution'
    // },
    // {
    //   title: '视野角度',
    //   dataIndex: 'cameraFov',
    //   key: 'cameraFov'
    // },
    // {
    //   title: '摄像头安装日期',
    //   dataIndex: 'cameraInstallationDate',
    //   key: 'cameraInstallationDate'
    // },
    // {
    //   title: '摄像头位置描述',
    //   dataIndex: 'cameraLocation',
    //   key: 'cameraLocation'
    // },
    // {
    //   title: '国标信息',
    //   dataIndex: 'gbInfo',
    //   key: 'gbInfo',
    // },
    // {
    //   title: '备注',
    //   dataIndex: 'notes',
    //   key: 'notes',
    // },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          {/* <Tooltip title='查看驱鼠器'>
            <Button
              type='text'
              icon={<img src={'/qsq.png'} className='icon_img' />}
              onClick={() => {
                // 传递搜索参数
                navigate('/device/repellent', {
                  replace: true,
                  state: {
                    isNotGroup: searchParams.isNotGroup,
                    keyword: record.cameraNo,
                    groupType: searchParams.groupType,
                    groupId: searchParams.groupId,
                    orgId: searchParams.orgId
                  }
                })
              }}
            />
          </Tooltip> */}
          <Tooltip title="模拟鼠患触发">
            <Button
              type="text"
              icon={
                <img src={"/rodent_infestation.png"} className="icon_img" />
              }
              onClick={() => {
                simulateRatEvent(record.cameraId)
                  .then(() => {
                    message.success("模拟鼠患触发成功");
                  })
                  .catch((error) => {
                    console.error("Failed to simulate rat event:", error);
                    message.error("模拟鼠患触发失败");
                  });
              }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditCamera(record)}
            />
          </Tooltip>
          <Tooltip title="查看">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewCamera(record)}
            />
          </Tooltip>
          <Tooltip title="跳转驱鼠器列表">
            <Button
              type="text"
              icon={<ArrowRightOutlined />}
              onClick={() => handleGetDevice(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteCamera(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 切换tab
  const onGroupTypeChange = (key) => {
    setTreeData(null);
    setActiveTab(key);
    if (key === "location") {
      getLocationTree();
    } else {
      fetchCustomerGroupsTree();
    }
  };

  // 树节点选中
  const onTreeSelect = (selectedKeys, node) => {
    if (window.cancelToken) {
      window.cancelToken();
    }
    if (selectedKeys.length > 0 && !node.node.disabled) {
      setSelectedKeys(selectedKeys);
      const tempObj = {
        [activeTab === "location" ? "groupId" : "orgId"]:
          selectedKeys[0] !== "isNotGroup" ? selectedKeys[0] : "",
        groupType: activeTab === "location" ? node.node.isLocal : "",
        isNotGroup: node.node.key === "isNotGroup",
        page: 0,
        ...searchForm.getFieldsValue(),
      };
      updateSeachParam(tempObj);
      fetchCameraList(tempObj);
    }
  };

  // 新增摄像头
  const handleAddCamera = async () => {
    setEditingRecord(null);
    form.resetFields();
    form.setFieldValue("cameraName", "智慧摄像头");
    await fetchCustomerData();
    setIsModalVisible(true);
  };

  // 新增/编辑弹窗确定
  const handleModalOk = async (values) => {
    try {
      // 恢复原始值
      if (editingRecord) {
        // 如果是编辑模式，检查摄像头类型和国标信息是否为"-"
        if (values.cameraType === "-") {
          values.cameraType = editingRecord.cameraType;
        }
        if (values.gbInfo === "-") {
          values.gbInfo = editingRecord.gbInfo;
        }
      }

      if (values.cameraInstallationDate) {
        values.cameraInstallationDate = dayjs(
          values.cameraInstallationDate
        ).format("YYYY-MM-DD");
      }
      if (editingRecord) {
        await updateCamera({
          ...values,
          cameraId: editingRecord.cameraId,
          lockVersion: lockVersionRef.current,
        });
        message.success("编辑成功");
      } else {
        await createCamera(values);
        message.success("新增成功");
      }
      await fetchCameraList();
      setIsModalVisible(false);
      form.resetFields();
      setEditingRecord(null);
    } catch (error) {
      console.error("Validation failed:", error);
      // message.error(error.message);
    }
  };

  // 新增/编辑弹窗取消
  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setEditingRecord(null);
  };

  // 编辑摄像头
  const handleEditCamera = async (record) => {
    setEditingRecord(record);
    lockVersionRef.current = record.lockVersion;
    await fetchCustomerData();
    setIsModalVisible(true);

    // 检查摄像头类型和国标信息是否在选项中
    let cameraTypeValue = record.cameraType;
    let gbInfoValue = record.gbInfo;

    // 判断摄像头类型是否在选项中
    if (cameraTypeData && cameraTypeValue) {
      const cameraTypeExists = cameraTypeData.some(
        (item) => item.itemCode === cameraTypeValue
      );
      if (!cameraTypeExists) {
        // 如果不存在，设置临时值"-"用于显示
        cameraTypeValue = "-";
      }
    }

    // 判断国标信息是否在选项中
    if (gbData && gbInfoValue) {
      const gbInfoExists = gbData.some((item) => item.itemCode === gbInfoValue);
      if (!gbInfoExists) {
        // 如果不存在，设置临时值"-"用于显示
        gbInfoValue = "-";
      }
    }

    form.setFieldsValue({
      ...record,
      cameraType: cameraTypeValue,
      gbInfo: gbInfoValue,
      cameraInstallationDate: record.cameraInstallationDate
        ? dayjs(record.cameraInstallationDate)
        : undefined,
    });
  };

  // 处理分页变化
  const handleTableChange = (pagination) => {
    const tempObj = {
      page: pagination.current - 1, // 后端分页从0开始
      size: pagination.pageSize,
    };
    updateSeachParam(tempObj);
    fetchCameraList(tempObj);
  };

  // 处理搜索
  const handleSearch = async (values) => {
    const tempObj = {
      page: 0, // 搜索时重置到第一页
      keyword: values.keyword || "",
    };
    updateSeachParam(tempObj);
    await fetchCameraList(tempObj);
  };

  // 处理重置
  const handleReset = () => {
    searchForm.resetFields();
    const tempObj = {
      page: 0,
      size: 10,
      keyword: "",
    };
    updateSeachParam(tempObj);
    fetchCameraList(tempObj);
  };

  // 删除摄像头
  const handleDeleteCamera = async (record) => {
    setDeleteCamera(record);
    setDeleteModalVisible(true);
  };

  // 确认删除
  const confirmDelete = async () => {
    try {
      await deleteCamerabyIds([deleteCamera.cameraId]);
      await fetchCameraList();
      setDeleteModalVisible(false);
      setDeleteCamera(null);
    } catch (error) {
      console.error("Failed to delete camera:", error);
    }
  };

  // 批量删除摄像头
  const handleBatchDelete = () => {
    setBatchDeleteModalVisible(true);
  };

  // 确认批量删除
  const confirmBatchDelete = async () => {
    try {
      await deleteCamerabyIds(selectedRowKeys);
      await fetchCameraList();
      setBatchDeleteModalVisible(false);
      setSelectedRowKeys([]);
    } catch (error) {
      console.error("Failed to batch delete cameras:", error);
    }
  };

  // 查看摄像头
  const handleViewCamera = async (record) => {
    try {
      const res = await getCameraDetail(record.cameraId);
      setViewingCamera(res);
      setViewModalVisible(true);
    } catch (error) {
      console.log(error);
    }
  };

  // 处理点击摄像头编号
  const handleCameraNoClick = (record) => {
    Modal.info({
      title: "摄像头详情",
      width: 600,
      content: (
        <div style={{ padding: "20px" }}>
          <Row gutter={16}>
            <Col span={12}>
              <p>
                <strong>设备名称:</strong> {record.deviceName || "-"}
              </p>
            </Col>
            <Col span={12}>
              <p>
                <strong>设备状态:</strong>
                {record.deviceStatus === 0
                  ? "离线"
                  : record.deviceStatus === 1
                  ? "在线"
                  : "停用"}
              </p>
            </Col>
          </Row>
        </div>
      ),
      okText: "关闭",
      centered: true,
      maskClosable: true,
    });
  };

  // 处理点击摄像头状态
  const handleDeviceStatusClick = (record) => {
    // 获取摄像头下的设备列表
    const devices = record.children || [];
    setCurrentCameraDevices(devices);
    setDeviceStatusModalVisible(true);
  };

  // 搜索摄像头编号
  const handleDeviceNoSearch = debounce(async (value) => {
    if (value) {
      try {
        const response = await getCameraLikeCameraNo(value);
        console.log("response", response);
        if (response) {
          // 根据接口返回的实际数据结构进行处理
          // 接口返回的是字符串数组 ["TEST100", "TEST201", ...]
          setAutocompleteOptions(
            response.map((deviceNo) => ({
              value: deviceNo,
            }))
          );
        }
      } catch (error) {
        console.error("Failed to fetch device numbers:", error);
      }
    } else {
      setAutocompleteOptions([]);
    }
  }, 500);

  useEffect(() => {
    getDictionay();
  }, []);

  const getDictionay = async () => {
    try {
      const [result1, result2] = await Promise.all([
        getNationalStandardInformationType(),
        getCameraType(),
      ]);
      setGbData(result1);
      setCameraTypebData(result2);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <Layout className="device-camera-container">
      <Sider width={300} className="group-sider" loading={loading}>
        <div className="group-header">
          <Tabs
            activeKey={activeTab}
            onChange={onGroupTypeChange}
            items={[
              {
                key: "location",
                label: (
                  <span>
                    <GlobalOutlined />
                    地理分组
                  </span>
                ),
              },
              {
                key: "customer",
                label: (
                  <span>
                    <TeamOutlined />
                    客户分组
                  </span>
                ),
              },
            ]}
          />
        </div>
        {node()}
        {treeData && (
          <div className="group-tree-scroll">
            <Tree
              showIcon
              // className={
              //   activeTab === 'location'
              //     ? 'device-camera-tree'
              //     : 'device-camera-tree customer-tree'
              // }
              onSelect={onTreeSelect}
              onExpand={(keys) => {
                setExpandedKeys(keys);
                setAutoExpandParent(false); // 用户手动展开后关闭自动展开
              }}
              selectedKeys={selectedKeys}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              treeData={searchValue ? filteredTreeData : treeData}
              blockNode
            />
          </div>
        )}
      </Sider>
      <Content className="device-content">
        <div className="search-filter">
          <Space size="middle" className="search-left">
            <Form form={searchForm} layout="inline" onFinish={handleSearch}>
              <Form.Item name="keyword" label="摄像头编号">
                <AutoComplete
                  placeholder="请输入摄像头编号"
                  style={{ width: 200 }}
                  options={autocompleteOptions}
                  onSearch={handleDeviceNoSearch}
                  onSelect={(value) => {
                    searchForm.setFieldsValue({ keyword: value });
                  }}
                  filterOption={false}
                  onChange={(value) => {
                    if (!value) {
                      setAutocompleteOptions([]);
                    }
                  }}
                />
              </Form.Item>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    搜索
                  </Button>
                  <Button onClick={handleReset}>重置</Button>
                </Space>
              </Form.Item>
            </Form>
          </Space>
          <Space size="middle" className="search-right">
            {/* <Button
              type='primary'
              icon={<SearchOutlined />}
              onClick={() => setAdvancedSearchVisible(true)}
            >
              高级搜索
            </Button> */}
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddCamera}
            >
              新增
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleBatchDelete}
              disabled={selectedRowKeys.length === 0}
            >
              批量删除
            </Button>
          </Space>
        </div>
        <Table
          columns={columns}
          loading={loading}
          dataSource={cameraData}
          className="camera-table"
          rowKey="cameraId"
          pagination={{
            total: total,
            current: searchParams.page + 1,
            pageSize: searchParams.size,
            showTotal: (total) => `共 ${total} 条`,
          }}
          onChange={handleTableChange}
          rowSelection={{
            type: "checkbox",
            selectedRowKeys: selectedRowKeys,
            onChange: (newSelectedRowKeys) => {
              setSelectedRowKeys(newSelectedRowKeys);
            },
          }}
          scroll={{ x: "max-content" }}
          tableLayout="auto"
          expandable={{
            childrenColumnName: "DO_NOT_SHOW_CHILDREN", // 设置为不存在的字段名
          }}
        />

        <Modal
          title={
            editingRecord
              ? `编辑摄像头-${editingRecord?.cameraName}`
              : "新增摄像头"
          }
          open={isModalVisible}
          okText={editingRecord ? `编辑` : "新增"}
          onOk={() => form.submit()}
          onCancel={handleModalCancel}
          width={1000}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleModalOk}
            initialValues={{
              cameraStatus: "online",
            }}
          >
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="cameraNo"
                  label="摄像头编号"
                  rules={[{ required: true, message: "请输入摄像头编号" }]}
                >
                  <Input placeholder="请输入摄像头编号" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="cameraName"
                  label="摄像头名称"
                  rules={[{ required: true, message: "请输入摄像头名称" }]}
                >
                  <Input placeholder="请输入摄像头名称" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="cameraModel"
                  label="摄像头型号"
                  rules={[{ required: true, message: "请输入摄像头型号" }]}
                >
                  <Input placeholder="请输入摄像头型号" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="cameraType" label="摄像头类型">
                  <Select allowClear placeholder="请选择摄像头类型">
                    {cameraTypeData
                      ? cameraTypeData.map((item) => (
                          <Select.Option key={item.itemCode}>
                            {item.itemName}
                          </Select.Option>
                        ))
                      : ""}
                    {form.getFieldValue("cameraType") === "-" && (
                      <Select.Option key="-">-</Select.Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item name="belongToGroup" label="区域">
                  <TreeSelect
                    showSearch
                    treeData={locationTreeData}
                    placeholder="请选择区域"
                    treeNodeFilterProp="title"
                    treeDefaultExpandAll
                    allowClear
                    treeNodeLabelProp="title"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="belongToOrg" label="客户">
                  <TreeSelect
                    placeholder="请选择客户"
                    showSearch
                    treeNodeFilterProp="title"
                    treeDefaultExpandAll
                    allowClear
                    treeNodeLabelProp="title"
                    treeData={customerListData}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item name="cameraBrand" label="摄像头品牌">
                  <Input placeholder="请输入摄像头品牌" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="cameraResolution" label="摄像头分辨率">
                  <Input placeholder="请输入摄像头分辨率" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item name="cameraFov" label="摄像头视野角度">
                  <Input placeholder="请输入摄像头视野角度" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="cameraLocation" label="摄像头位置描述">
                  <Input placeholder="请输入摄像头位置描述" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item name="gbInfo" label="国标信息">
                  <Select allowClear placeholder="请选择国标信息">
                    {gbData
                      ? gbData.map((item) => (
                          <Select.Option key={item.itemCode}>
                            {item.itemName}
                          </Select.Option>
                        ))
                      : ""}
                    {form.getFieldValue("gbInfo") === "-" && (
                      <Select.Option key="-">-</Select.Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="cameraInstallationDate" label="摄像头安装日期">
                  <DatePicker
                    style={{ width: "100%" }}
                    disabled={!!editingRecord}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item name="notes" label="备注信息">
                  <Input.TextArea placeholder="请输入备注信息" />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>

        <Modal
          title="确认删除"
          open={deleteModalVisible}
          onOk={confirmDelete}
          onCancel={() => {
            setDeleteModalVisible(false);
            setDeleteCamera(null);
          }}
        >
          <p>
            确定要删除摄像头 "{deleteCamera?.cameraName}" 吗？此操作不可恢复。
          </p>
        </Modal>

        <Modal
          title="确认批量删除"
          open={batchDeleteModalVisible}
          onOk={confirmBatchDelete}
          onCancel={() => {
            setBatchDeleteModalVisible(false);
            setSelectedRowKeys([]);
          }}
        >
          <p>
            确定要删除选中的 {selectedRowKeys.length}{" "}
            个摄像头吗？此操作不可恢复。
          </p>
        </Modal>

        <Drawer
          title={
            <div className="drawer-title">
              <span>摄像头详情</span>
              <span className="drawer-subtitle">
                {viewingCamera?.cameraName}
              </span>
            </div>
          }
          open={viewModalVisible}
          onClose={() => {
            setViewModalVisible(false);
            setViewingCamera(null);
          }}
          width={450}
        >
          {viewingCamera && (
            <div className="camera-detail-container">
              <div className="camera-detail-section">
                <h3>基本信息</h3>
                <div className="detail-item">
                  <span className="detail-label">摄像头编号：</span>
                  <span className="detail-value">{viewingCamera.cameraNo}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">摄像头名称：</span>
                  <span className="detail-value">
                    {viewingCamera.cameraName}
                  </span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">摄像头类型：</span>
                  <span className="detail-value">
                    {(() => {
                      if (!cameraTypeData) return viewingCamera.cameraType;
                      const found = cameraTypeData.find(
                        (item) => item.itemCode === viewingCamera.cameraType
                      );
                      return found ? found.itemName : "-";
                    })()}
                  </span>
                </div>
                {console.log("111111", viewingCamera)}
                <div className="detail-item">
                  <span className="detail-label">地址信息：</span>
                  <span className="detail-value">
                    {viewingCamera.location || "-"}
                  </span>
                </div>
              </div>

              <div className="camera-detail-section">
                <h3>设备规格</h3>
                <div className="detail-item">
                  <span className="detail-label">摄像头品牌：</span>
                  <span className="detail-value">
                    {viewingCamera.cameraBrand || "-"}
                  </span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">摄像头型号：</span>
                  <span className="detail-value">
                    {viewingCamera.cameraModel || "-"}
                  </span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">分辨率：</span>
                  <span className="detail-value">
                    {viewingCamera.cameraResolution || "-"}
                  </span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">视野角度：</span>
                  <span className="detail-value">
                    {viewingCamera.cameraFov || "-"}
                  </span>
                </div>
              </div>

              <div className="camera-detail-section">
                <h3>安装信息</h3>
                <div className="detail-item">
                  <span className="detail-label">安装日期：</span>
                  <span className="detail-value">
                    {viewingCamera.cameraInstallationDate || "-"}
                  </span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">位置描述：</span>
                  <span className="detail-value">
                    {viewingCamera.cameraLocation || "-"}
                  </span>
                </div>
              </div>

              <div className="camera-detail-section">
                <h3>其他信息</h3>
                <div className="detail-item">
                  <span className="detail-label">国标信息：</span>
                  <span className="detail-value">
                    {(() => {
                      if (!gbData) return viewingCamera.gbInfo;
                      const found = gbData.find(
                        (item) => item.itemCode === viewingCamera.gbInfo
                      );
                      return found ? found.itemName : "-";
                    })()}
                  </span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">备注：</span>
                  <span className="detail-value">
                    {viewingCamera.notes || "-"}
                  </span>
                </div>
              </div>
            </div>
          )}
        </Drawer>

        {advancedSearchVisible && (
          <AdvancedSearch
            advancedSearchVisible={advancedSearchVisible}
            onCancel={() => {
              setAdvancedSearchVisible(false);
            }}
            data={[
              {
                label: "摄像头编号",
                value: "cameraNo",
              },
              {
                label: "摄像头名称",
                value: "cameraName",
              },
              {
                label: "摄像头类型",
                value: "cameraType",
                options: cameraTypeData?.map((ele) => ({
                  label: ele.itemName,
                  value: ele.itemCode,
                })),
                children: (setInputValue) => (
                  <Select
                    placeholder="请选择摄像头类型"
                    style={{ width: "300px" }}
                    onChange={(value) => {
                      setInputValue(value);
                    }}
                    options={cameraTypeData?.map((ele) => ({
                      label: ele.itemName,
                      value: ele.itemCode,
                    }))}
                  />
                ),
              },
              {
                label: "摄像头型号",
                value: "cameraModel",
              },
              {
                label: "摄像头品牌",
                value: "cameraBrand",
              },
              {
                label: "摄像头分辨率",
                value: "cameraResolution",
              },
              {
                label: "摄像头视野角度",
                value: "cameraFov",
              },
              {
                label: "摄像头位置描述",
                value: "cameraLocation",
              },
              {
                label: "国标信息",
                value: "gbInfo",
                options: gbData?.map((ele) => ({
                  label: ele.itemName,
                  value: ele.itemCode,
                })),
                children: (setInputValue) => (
                  <Select
                    placeholder="请选择国标信息"
                    style={{ width: "300px" }}
                    onChange={(value) => {
                      setInputValue(value);
                    }}
                    options={gbData?.map((ele) => ({
                      label: ele.itemName,
                      value: ele.itemCode,
                    }))}
                  />
                ),
              },
              {
                label: "摄像头安装日期",
                value: "cameraInstallationDate",
                children: (setInputValue) => (
                  <DatePicker
                    style={{ width: "300px" }}
                    format={"YYYY-MM-DD"}
                    onChange={(value) => {
                      setInputValue(value ? value.format("YYYY-MM-DD") : "");
                    }}
                  />
                ),
              },
              {
                label: "备注信息",
                value: "notes",
              },
            ]}
            getSearchData={(data) => {
              const tempObj = {
                page: 0,
                ...data,
              };
              updateSeachParam(tempObj);
              fetchCameraList(tempObj);
              setAdvancedSearchVisible(false);
            }}
          />
        )}

        {/* 设备状态弹窗 */}
        <Modal
          title="设备状态详情"
          open={deviceStatusModalVisible}
          centered
          onCancel={() => {
            setDeviceStatusModalVisible(false);
            setCurrentCameraDevices([]);
          }}
          footer={[
            <Button
              key="close"
              onClick={() => {
                setDeviceStatusModalVisible(false);
                setCurrentCameraDevices([]);
              }}
            >
              关闭
            </Button>,
          ]}
          width={600}
        >
          <Table
            dataSource={currentCameraDevices}
            rowKey="deviceId"
            pagination={false}
            columns={[
              {
                title: "设备名称",
                dataIndex: "deviceName",
                key: "deviceName",
                render: (text) => text || "-",
              },
              {
                title: "设备编号",
                dataIndex: "deviceNo",
                key: "deviceNo",
                render: (text) => text || "-",
              },
              {
                title: "设备状态",
                dataIndex: "deviceStatus",
                key: "deviceStatus",
                render: (status) => {
                  const statusMap = {
                    0: { text: "离线", color: "#ff4d4f" },
                    1: { text: "在线", color: "#52c41a" },
                    2: { text: "停用", color: "#faad14" },
                  };
                  const statusInfo = statusMap[status] || {
                    text: "-",
                    color: "#d9d9d9",
                  };
                  return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
                },
              },
            ]}
          />
        </Modal>
      </Content>
    </Layout>
  );
};

export default DeviceCamera;
