.repellent-effect {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.repellent-effect-group-sider {
  background: #fff;
  border-right: 1px solid #f0f0f0;
  margin-right: 24px;
  padding: 5px;
  border-radius: 8px;
}

.repellent-effect-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.header-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.overview-filter-bar {
  background: #fff;
  border-radius: 4px;
  margin-bottom: 24px;
}

.repellent-effect-content {
  /* background: #fff !important; */
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  gap: 16px;
  border-radius: 5px;
  overflow: hidden;
}

.repellent-effect-content .ant-tabs-nav{
  background-color: #fff;
  padding: 5px;
  border-radius: 8px;
}

.repellent-effect-content .ant-tabs{
  height: 100%;
}

.repellent-effect-content .ant-tabs-content-holder .ant-tabs-content{
  height: 100%;
}

.repellent-effect-content .ant-tabs-content-holder .ant-tabs-content .ant-tabs-tabpane{
  height: 100%;
}
.statistics-cards .ant-card {
  border-radius: 8px;
}

.repellent-effect-cart{
  height: calc(100% - 105px);
}

/* Responsive styles */
@media (max-width: 768px) {
  /* .repellent-effect {
    padding: 16px;
  } */

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
  }

  .statistics-cards .ant-col {
    flex: 0 0 100%;
    max-width: 100%;
  }
}