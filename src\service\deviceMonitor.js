import fetch from './fetch';

import {API_PREFIX} from "./constant"
import { method } from 'lodash';

// 获取设备实时状态
export const getDeviceStatus = (data) => {
  return fetch({
    url: `${API_PREFIX["view-service"]}/monitor/device/status`,
    method: 'post',
    data
  })
}

// 导出设备状态数据
export const exportDeviceStatus = (data) => {
  return fetch({
    url: `${API_PREFIX["view-service"]}/monitor/export`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

//获取设备温度
export const gettemperature = (data) =>{
  return fetch({
    url:`${API_PREFIX["view-service"]}/monitor/device/tem`,
    method:'post',
    data
  })
}

//获取设备湿度
export const getHum = (data) =>{
  return fetch({
    url:`${API_PREFIX["view-service"]}/monitor/device/hum`,
    method:'post',
    data
  })
}

//获取设备告警
export const getAlarm = (data) => {
  return fetch({
    url:`${API_PREFIX["view-service"]}/monitor/device/alarm`,
    method:'post',
    data
  })
}
