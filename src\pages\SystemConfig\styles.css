.system-config {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.page-header {
  margin-bottom: 24px;
}

.header-left {
  margin-bottom: 16px;
}

.header-left h2 {
  margin: 8px 0 0;
  font-size: 24px;
  font-weight: 500;
}

.env-config {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.env-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.env-header > span:last-child {
  margin-left: auto;
  color: rgba(0, 0, 0, 0.45);
}

.action-bar {
  margin-top: 24px;
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

/* Responsive styles */
@media (max-width: 768px) {
  .system-config {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .env-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .env-header > * {
    width: 100%;
  }

  .action-bar {
    padding: 16px 8px;
  }

  .action-bar .ant-space {
    width: 100%;
    flex-direction: column;
  }

  .action-bar .ant-btn {
    width: 100%;
  }
}