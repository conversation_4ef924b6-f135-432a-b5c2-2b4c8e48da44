import { useState } from 'react'
import { Input } from 'antd';
import { debounce } from 'lodash'
// const { Search } = Input;

export default function useSelect(treeData,setExpandedKeys) {
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [filteredTreeData, setFilteredTreeData] = useState([]);


      // 高亮匹配的文本（核心逻辑）
      const highlightMatch = (title,keyword) => {
        if (!keyword) return title;
        const index = title.indexOf(keyword);
        if (index === -1) return title;
        return (
          <span>
            {title.substr(0, index)}
            <span style={{ color: '#f50', fontWeight: 'bold' }}>
              {title.substr(index, keyword.length)}
            </span>
            {title.substr(index + keyword.length)}
          </span>
        );
      };
  
      // 递归过滤树节点（核心逻辑）
      const filterTreeData = (nodes, parentKeys = [],keyword) => {
        // 检查nodes是否存在且为数组
        if (!nodes || !Array.isArray(nodes)) {
          return [];
        }

        return nodes
          .map((node) => {
            const { children, ...rest } = node;
            const isMatch = rest.title && rest.title.includes(keyword);
            // 1. 当前节点匹配：保留并展开父级路径
            if (isMatch) {
              return {
                ...rest,
                title: highlightMatch(rest.title,keyword),
                children: children ? filterTreeData(children, [...parentKeys, rest.key],keyword) : null,
              };
            }
            // 2. 子节点存在匹配：递归过滤子节点
            if (children) {
              const filteredChildren = filterTreeData(children, [...parentKeys, rest.key],keyword);
              if (filteredChildren.length > 0) {
                return {
                  ...rest,
                  title: highlightMatch(rest.title,keyword),
                  children: filteredChildren,
                };
              }
            }
            // 3. 完全不匹配：过滤掉当前节点
            return null;
          })
          .filter(Boolean);
      };
  
      // 输入搜索时的处理
      const handleSearch = debounce((value) => {
        setSearchValue(value);

        // 检查treeData是否存在且为数组
        if (!Array.isArray(treeData) || treeData.length === 0) {
          setFilteredTreeData([]);
          return;
        }

        const matchedKeys = [];
        const data = filterTreeData(treeData,[],value);
        // 收集所有父级节点key用于展开
        const traverseCollectParentKeys = (nodes) => {
          nodes.forEach((node) => {
            if (node.children) {
              traverseCollectParentKeys(node.children);
              if (node.children.some(child => child.children || node.children.length > 0)) {
                matchedKeys.push(node.key);
              }
            }
          });
        };
        traverseCollectParentKeys(data);
        setFilteredTreeData(data);
        setExpandedKeys(matchedKeys);     // 自动展开匹配路径
        setAutoExpandParent(true);        // 允许自动展开父级
      },300);
  const node = () => <div style={{ marginBottom: 8,padding: "0px 20px"}}><Input placeholder="请输入关键词搜索"  onChange={(e) => handleSearch(e.target.value)}  /></div>
  return [node,searchValue,autoExpandParent,filteredTreeData,setAutoExpandParent]
}
