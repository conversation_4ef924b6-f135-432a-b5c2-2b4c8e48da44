import { Modal, message, Form, Select, Input, Radio } from 'antd'
import { getFirmwarePackageList } from './../../service/upgradePackages'
import { createTaskByDevice } from './../../service/maintenanceUpgrade'
import { oneDeviceUpgrade } from './../../service/deviceRepellent'
import PropTypes from 'prop-types'
import { useEffect, useState } from 'react'
const { Option } = Select
import { debounce } from 'lodash'

const CreateTask = ({ visible, onCancel, deviceId, currentDevice }) => {
  const [taskForm] = Form.useForm()
  const [selectloading, setSelectLoading] = useState(false)
  const [submitLoading, setSubmitLoading] = useState(false)
  const [firmwareData, setFirmwareData] = useState([])
  const [searchFirmwareData, setSearchFirmwareData] = useState({
    packageName: '',
    packageType: 'FIRMWARE',
    status: 'APPROVED',
    size: 50,
    page: 0
  }) // 分页查询摄像头
  const [totalPages, setTotalPages] = useState(0)

  const fetchFirmwareList = async (obj = {}) => {
    try {
      const res = await getFirmwarePackageList({
        ...searchFirmwareData,
        ...obj
      })
      setTotalPages(res.totalPages)
      return res.content
    } catch (error) {
      console.error(error)
    }
  }

  const handleSubmit = async () => {
    if (submitLoading) return
    setSubmitLoading(true)
    try {
      const values = await taskForm.validateFields()
      delete values.version
      delete values.description
      currentDevice
        ? await oneDeviceUpgrade({
            ...values,
            deviceId: currentDevice.deviceId
          })
        : await createTaskByDevice({ ...values, deviceIds: deviceId })
      currentDevice
        ? message.success('创建成功，已开始执行')
        : message.success('任务创建成功')
      onCancel()
    } catch (error) {
      console.log(error.message)
      // message.error(error.message)
    } finally {
      setSubmitLoading(false)
    }
  }

  // 下拉搜索
  const selectSearch = async value => {
    setSelectLoading(true)
    try {
      const data = await fetchFirmwareList({ packageName: value, page: 0 })
      setFirmwareData(data)
    } catch (error) {
      console.error('Failed to search cameras:', error)
    } finally {
      setSelectLoading(false)
    }
  }

  // 滚动加载
  const loadMore = async e => {
    if (selectloading) return
    const bottom =
      e.target.scrollHeight === e.target.scrollTop + e.target.clientHeight
    if (bottom && totalPages - 1 > searchFirmwareData.page) {
      setSearchFirmwareData({
        ...searchFirmwareData,
        page: searchFirmwareData.page + 1
      })
      setSelectLoading(true)
      const newOptions = await fetchFirmwareList({
        page: searchFirmwareData.page + 1
      })
      setFirmwareData(prevOptions => [...prevOptions, ...newOptions])
      setSelectLoading(false)
    }
  }

  useEffect(() => {
    ;(async () => {
      const res = await fetchFirmwareList()
      setFirmwareData(res)
      taskForm.setFieldsValue({
        taskName: currentDevice
          ? '驱鼠器升级' + +new Date()
          : '驱鼠器升级任务' + +new Date(),
        firmwareId: res[0]?.packageId,
        priority: 1,
        version: res[0]?.version,
        description: res[0]?.description
      })
    })()
  }, [])

  return (
    <Modal
      title={currentDevice ? '驱鼠器升级' : '创建升级任务'}
      open={visible}
      onOk={() => taskForm.submit()}
      okText={currentDevice ? '执行' : '创建'}
      onCancel={() => {
        onCancel()
        taskForm.resetFields()
      }}
      width={500}
      confirmLoading={submitLoading}
    >
      <Form form={taskForm} layout='vertical' onFinish={handleSubmit}>
        <Form.Item
          name='taskName'
          label='任务名称'
          rules={[{ required: true }]}
        >
          <Input placeholder='请输入任务名称' />
        </Form.Item>

        <Form.Item name='firmwareId' label='固件' rules={[{ required: true }]}>
          <Select
            showSearch
            allowClear
            placeholder='请数固件名称搜索'
            onPopupScroll={loadMore}
            loading={selectloading}
            onSearch={debounce(selectSearch, 300)}
            style={{ width: '100%' }}
            optionFilterProp='children'
            filterOption={false}
            onSelect={(_, item) => {
              taskForm.setFieldsValue({
                version: item.version,
                description: item.description
              })
            }}
            onDropdownVisibleChange={async open => {
              if (open) {
                const res = await fetchFirmwareList()
                setFirmwareData(res)
              }
            }}
          >
            {firmwareData?.map(option => (
              <Option
                key={option.packageId}
                value={option.packageId}
                version={option.version}
                description={option.description}
              >
                {option.packageName}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item name='priority' label='优先级' rules={[{ required: true }]}>
          <Radio.Group>
            <Radio.Button value={0}>低</Radio.Button>
            <Radio.Button value={1}>中</Radio.Button>
            <Radio.Button value={2}>高</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item name='version' label='固件版本'>
          <Input disabled />
        </Form.Item>
        <Form.Item name='description' label='升级包描述'>
          <Input.TextArea rows={4} disabled />
        </Form.Item>
      </Form>
    </Modal>
  )
}

CreateTask.propTypes = {
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  deviceId: PropTypes.array,
  currentDevice: PropTypes.object
}

export default CreateTask
