import { makeAutoObservable } from 'mobx';

class RootStore {
  constructor() {
    makeAutoObservable(this);
  }

  // User state
  userInfo = {
    name: 'Admin',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin'
  };

  // Dashboard statistics
  statistics = {
    totalDevices: 0,
    activeDevices: 0,
    totalOrders: 0,
    monthlyRevenue: 0
  };

  // Regional sales data
  regionalSales = [
    { name: '北京', value: 25 },
    { name: '上海', value: 20 },
    { name: '广州', value: 18 },
    { name: '深圳', value: 15 },
    { name: '湖北', value: 12 },
    { name: '河南', value: 10 }
  ];

  // Tasks list
  tasks = [
    {
      id: 1,
      date: '2023-05-03',
      assignee: '张浩然',
      content: '解决动态路由加载问题'
    },
    {
      id: 2,
      date: '2023-05-02',
      assignee: '陈晓东',
      content: '增加销货页面和HTTP异常页面'
    }
  ];

  // Project updates
  updates = [
    {
      id: 1,
      user: 'chenhu',
      content: '解决了登录bug',
      time: '2023-07-12 20:46'
    },
    {
      id: 2,
      user: '张妙然',
      content: '按钮颜色与设计不相符',
      time: '2023-07-03 20:46'
    }
  ];

  setStatistics(stats) {
    this.statistics = stats;
  }

  addTask(task) {
    this.tasks.push(task);
  }

  addUpdate(update) {
    this.updates.unshift(update);
  }
}

export const store = new RootStore();