import { useState, useEffect, useRef } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { observer } from 'mobx-react-lite';
import Layout from './components/Layout';
import Login from './pages/Login';
import { authStore } from './stores/authStore';
import './App.css';
import Cookies from 'js-cookie';

const PrivateRoute = observer(({ children }) => {
  const [isReady, setIsReady] = useState(false);
  const logPrinted = useRef(false);
  
  useEffect(() => {
    // 确保在渲染保护路由前先检查认证状态
    setTimeout(() => {
      // 检查认证状态前先从存储中获取token
      const hasToken = localStorage.getItem('token') ;
      
      // 如果有token，立即设置认证状态为true
      if (hasToken) {
        authStore.setIsAuthenticated(true);
        
        // 恢复用户数据
        const userData = localStorage.getItem('userData') || sessionStorage.getItem('userData');
        if (userData && !authStore.user) {
          try {
            authStore.setUser(JSON.parse(userData));
          } catch (e) {
            console.error('恢复用户数据失败:', e);
          }
        }
      }
      
      if (!logPrinted.current) {
        logPrinted.current = true;
      }
      
      setIsReady(true);
    }, 300); // 增加等待时间，确保有足够时间处理认证状态
  }, []);
  
  if (!isReady) {
    return null;
  }
  
  // 如果认证有效，检查是否需要重定向
  if (authStore.isAuthenticated) {
    const lastPage = localStorage.getItem('lastPage');
    const currentPath = window.location.pathname;
    
    // 仅在以下条件满足时重定向：
    // 1. 有lastPage
    // 2. lastPage不是登录页
    // 3. 当前路径不是lastPage
    // 4. 当前路径不是根路径'/'
    // 5. 有token存在
    if (lastPage && lastPage !== '/login' && currentPath !== lastPage && currentPath !== '/') {
      const hasToken = localStorage.getItem('token') ;
      if (hasToken) {
        localStorage.removeItem('lastPage');
        return <Navigate to={lastPage} replace />;
      }
    }
    return children;
  }
  
  // 检查是否有token存在
  const hasToken = localStorage.getItem('token') ;
  if (hasToken) {
    authStore.setIsAuthenticated(true);
    return children;
  }
  
  return <Navigate to="/login" />;
});

const App = observer(() => {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route
            path="/*"
            element={
              <PrivateRoute>
                <Layout />
              </PrivateRoute>
            }
          />
        </Routes>
      </Router>
    </ConfigProvider>
  );
});

export default App;