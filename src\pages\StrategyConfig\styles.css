.strategy-config-container {
  height: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden !important;
}
.add-strategy-content {
  padding: 16px;
  height: 100% !important;
  background: #fff;
  border-radius: 4px;
  overflow: auto !important;
}

.strategy-tree-scroll{
  height: calc(100% - 130px);
  overflow-y: auto;
}

.strategy-sider {
  background: #fff;
  padding: 8px;
  border-right: 1px solid #f0f0f0;
}

.strategy-header {
  padding: 16px 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.strategy-content {
  padding: 16px;
  background: #fff;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.strategy-table {
  margin-top: 8px;
}


.startegy-tree .ant-tree-switcher{
  display: none;
}

.startegy-tree .ant-tree-node-content-wrapper{
  padding-left: 15px;
}

/* Tree node customization */
.ant-tree-node-content-wrapper {
  width: 100%;
}

.ant-tree-node-selected {
  background-color: #e6f7ff !important;
}



@media (max-width: 768px) {
  .group-sider {
    display: none;
  }
  
  .search-filter {
    flex-direction: column;
    gap: 16px;
  }
  
  .search-left,
  .search-right {
    width: 100%;
  }
}