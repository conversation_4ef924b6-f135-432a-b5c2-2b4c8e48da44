import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Table,
  Breadcrumb,
  Input,
  Select,
  DatePicker,
  Statistic,
  Badge,
  Modal,
  Form,
  Radio,
  Tabs,
  Timeline,
  List,
  Avatar,
  message,
  Tooltip,
  Drawer,
} from "antd";
import {
  SearchOutlined,
  UserOutlined,
  EyeOutlined,
  DeleteOutlined,
  RedoOutlined
} from "@ant-design/icons";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import "./styles.css";
import { alertInfoApi } from "../../service/alertInfo";
import { useLocation,useNavigate } from "react-router-dom"


dayjs.extend(relativeTime);
const { RangePicker } = DatePicker;
const { Option } = Select;

const AlertInfo = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedAlert, setSelectedAlert] = useState(null);
  const [editingAlarmId, setEditingAlarmId] = useState(null);
  const [editingAlarmDeviceId, setEditingAlarmDeviceId] = useState("");
  const [editingCreatedTime, setEditingCreatedTime] = useState("");
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [alertList, setAlertList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingEdit, setLoadingEdit] = useState(false);
  const [userOptions, setUserOptions] = useState([]);
  const [total, setTotal] = useState(0);
  const [searchParams, setSearchParams] = useState({
    alarmType: "",
    alarmLevel: "",
    alarmStatus: "",
    startTime: "",
    endTime: "",
    page: 0,
    size: 10,
  });

  const location = useLocation()
  const navigate = useNavigate()

  
  const [alarmList, setAlarmList] = useState([]);

  // Mock data for statistics
  const statistics = [
    // {
    //   title: '待发送',
    //   value: 12,
    //   icon: <ClockCircleOutlined style={{ fontSize: 24, color: '#1890ff' }} />
    // },
    // {
    //   title: '已发送',
    //   value: 86,
    //   icon: <CheckCircleOutlined style={{ fontSize: 24, color: '#52c41a' }} />
    // },
    // {
    //   title: '失败',
    //   value: 5,
    //   icon: <CloseCircleOutlined style={{ fontSize: 24, color: '#ff4d4f' }} />
    // }
  ];


  // 获取告警信息
  const fetchAlertList = async (
    page = searchParams.page,
    customParams = null
  ) => {
    try {
      setLoading(true);
      const params = customParams || {
        ...searchParams,
        page,
        size: searchParams.size,
      };
      const response = await alertInfoApi.findAlarms(params);
      if (response) {
        // 处理状态数据
        if (response.content) {
          const formattedData = response.content.map((item, index) => ({
            key: item.id || index.toString(),
            title: item.title,
            content: item.content,
            alarmStatus: item.alarmStatus,
            alarmType: item.alarmType,
            alarmLevel: item.alarmLevel,
            deviceNo: item.deviceNo,
            receivers: item.receivers,
            channel: item.channel,
            createdTime: item.createdTime,
            // 设置操作按钮状态
            status: getStatusFromAlarmStatus(item.alarmStatus),
          }));
          setAlertList(formattedData);
        }
        // 设置总数
        setTotal(response.totalElements || 0);
      }
    } catch (error) {
      console.error("获取告警信息失败:", error);
    } finally {
      setLoading(false);
    }
  };

  // 将接口返回的状态值映射到操作按钮状态
  const getStatusFromAlarmStatus = (alarmStatus) => {
    const statusMap = {
      0: "pending", // 新告警
      1: "sent", // 已确认
      2: "sent", // 已解决
      3: "sent", // 已关闭
    };
    return statusMap[alarmStatus] || "pending";
  };

  // 获取用户下拉列表
  const fetchUserDropdown = async () => {
    try {
      const response = await alertInfoApi.getUserDropdown();
      if (response) {
        // 确保userOptions是一个数组
        if (Array.isArray(response)) {
          setUserOptions(response);
        } else if (response.data && Array.isArray(response.data)) {
          // 处理可能的响应结构，如{data: [...]}
          setUserOptions(response.data);
        } else {
          // 如果不是数组，则设置为空数组
          console.warn("获取的用户数据不是数组格式", response);
          setUserOptions([]);
        }
      } else {
        setUserOptions([]);
      }
    } catch (error) {
      console.error("获取用户列表失败:", error);
      setUserOptions([]);
    }
  };

  // 获取告警详情
  const fetchAlarmDetail = async (id) => {
    try {
      setLoadingEdit(true);
      // 同时获取用户下拉列表
      await fetchUserDropdown();

      const response = await alertInfoApi.getAlarmById(id);
      if (response) {
        // 当用于详情查看时，设置selectedAlert并打开详情弹窗
        if (!editModalVisible) {
          setSelectedAlert(response);
          setDetailModalVisible(true);
        } else {
          // 保存编辑中的告警ID和deviceId，用于后续更新操作
          setEditingAlarmId(response.id);
          setEditingAlarmDeviceId(response.deviceId || "");
          setEditingCreatedTime(response.createdTime || "");

          // 使用setTimeout确保表单已经挂载
          setTimeout(() => {
            editForm.setFieldsValue({
              title: response.title,
              content: response.content,
              alarmStatus: response.alarmStatus?.toString(),
              alarmType: response.alarmType?.toString(),
              alarmLevel: response.alarmLevel?.toString(),
              channel: response.channel?.toString(),
              receivers: response.receivers,
              remark: response.remark,
            });
          }, 0);
        }
      }
    } catch (error) {
      console.error("获取告警详情失败:", error);
    } finally {
      setLoadingEdit(false);
    }
  };

  // 保存编辑的告警信息
  const handleEditAlert = async (values) => {
    try {
      setLoadingEdit(true);

      // 准备提交的数据
      const updateData = {
        id: editingAlarmId,
        deviceId: editingAlarmDeviceId,
        title: values.title,
        content: values.content,
        receivers: values.receivers, // 这里应该是选择的接收员工的id数组
        channel: parseInt(values.channel),
        alarmType: parseInt(values.alarmType),
        createdTime: editingCreatedTime, // 使用从接口获取的createdTime
        alarmLevel: parseInt(values.alarmLevel),
        alarmStatus: parseInt(values.alarmStatus),
        alarmNote: values.remark || "",
      };

      // 调用更新接口
      await alertInfoApi.updateAlarm(editingAlarmId, updateData);

      message.success("更新告警信息成功");

      // 关闭弹窗并重置表单
      setEditModalVisible(false);
      editForm.resetFields();

      // 刷新列表
      fetchAlertList();
    } catch (error) {
      console.error("更新告警信息失败:", error);
    } finally {
      setLoadingEdit(false);
    }
  };

  // 处理删除告警
  const handleDeleteAlarm = async (id) => {
    try {
      await alertInfoApi.deleteAlarm(id);
      message.success("删除告警成功");
      fetchAlertList();
    } catch (error) {
      console.error("删除告警失败:", error);
    }
  };

  // 组件加载时获取数据
  useEffect(() => {

    if(location.state){
      setSearchParams((prev) => ({ ...prev, ...location.state }))
      fetchAlertList(0, {
        ...searchParams,
        ...location.state
      } );
      navigate('.', { replace: true, state: null })
    }else{
      fetchAlertList()
    }
   
    
    fetchUserDropdown();
  }, []);

  // 渲染告警状态
  const renderAlarmStatus = (status) => {
    const statusConfig = {
      0: { text: "未处理", color: "#1890ff" },
      1: { text: "已确认", color: "#52c41a" },
      2: { text: "已解决", color: "#52c41a" },
      3: { text: "已关闭", color: "#d9d9d9" },
    };
    const config = statusConfig[status] || { text: "未知状态", color: "#d9d9d9" };
    return <Badge color={config.color} text={config.text} />;
  };

  // 渲染告警类型
  const renderAlarmType = (type) => {
    const typeMap = {
      0: "驱鼠告警",
      1: "设备告警",
      2: "平台告警",
      3: "升级告警"
    };
    return typeMap[type] || "未知类型";
  };

  // 渲染告警等级
  const renderAlarmLevel = (level) => {
    const levelConfig = {
      0: { text: "低", color: "#52c41a" },
      1: { text: "中", color: "#faad14" },
      2: { text: "高", color: "#f5222d" },
      3: { text: "紧急", color: "#f5222d" },
    };
    const config = levelConfig[level] || { text: "未知等级", color: "#d9d9d9" };
    return <Badge color={config.color} text={config.text} />;
  };

  // 渲染通知渠道
  const renderChannel = (channel) => {
    const channelMap = {
      0: "邮箱",
      1: "短信",
      2: "微信",
      3: "钉钉",
      4: "系统内部",
    };
    return channelMap[channel] || "未知渠道";
  };

  // Table columns
  const columns = [
    {
      title: "告警标题",
      dataIndex: "title",
      align: "center",
 
      // width: "10%",
      key: "title",
    },
    {
      title: "告警设备",
      dataIndex: "deviceNo",
      align: "center",
      key: "deviceNo",
      ellipsis: true
    },
    {
      title: "告警内容",
      dataIndex: "content",
      align: "center",
      width: "400px",
      // width: "10%",
      key: "content",
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          {text ? (text.length > 10 ? `${text.substring(0, 10)}...` : text) : "-"}
        </Tooltip>
      ),
    },
    {
      title: "告警等级",
      dataIndex: "alarmLevel",
      align: "center",
      width: "100px",
      // width: "10%",
      key: "alarmLevel",
      render: (text) => renderAlarmLevel(text),
    },
    {
      title: "告警类型",
      dataIndex: "alarmType",
      align: "center",
      width: "100px",
      key: "alarmType",
      render: (text) => renderAlarmType(text),
    },

    {
      title: "告警状态",
      dataIndex: "alarmStatus",
      align: "center",
      width: "100px",
      key: "alarmStatus",
      render: (text) => renderAlarmStatus(text),
    },
    // {
    //   title: "推送状态",
    //   dataIndex: "alarmStatus",
    //   align: "center",
    //   width: "10%",
    //   key: "alarmStatus"
    // },


    {
      title: "接收人员",
      dataIndex: "receivers",
      align: "center",
      // width: "10%",
      key: "receivers",
      render: (receivers) => {
        if (!Array.isArray(receivers) || !Array.isArray(userOptions)) {
          return "-";
        }
        return receivers
          .map((id) => {
            const user = userOptions.find((u) => u.id === id);
            return user ? user.name : "";
          })
          .filter((name) => name)
          .join("，");
      },
    },
    {
      title: "通知渠道",
      dataIndex: "channel",
      align: "center",
      width: "10%",
      key: "channel",
      render: (text) => renderChannel(text),
    },
    {
      title: "创建时间",
      dataIndex: "createdTime",
      align: "center",
      width: "10%",
      key: "createdTime",
      render: (text) => {
        return dayjs(text).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    {
      title: "操作",
      key: "action",
      align: "center",
      render: (_, record) => {
        const actions = {
          pending: [
            { icon: <EyeOutlined />, label: '查看' },
            { icon: <DeleteOutlined />, label: "删除", danger: true },
          ],
          sent: [
            { icon: <EyeOutlined />, label: "查看" },
            { icon: <RedoOutlined />, label: "重发" },
          ],
          // failed: [
          //   { icon: <EyeOutlined />, label: "详情" },
          //   { icon: <RedoOutlined />, label: "重试", type: "primary" },
          // ],
        };

        return (
          <Space>
            {actions[record.status].map((action, index) => (
              <Tooltip key={index} title={action.label}>
                <Button
                  type={action.type || "text"}
                  icon={action.icon}
                  danger={action.danger}
                  onClick={() => {
                    if (action.label === "查看") {
                      // 通过API获取详细数据
                      fetchAlarmDetail(record.key);
                    } else if (action.label === "删除") {
                      Modal.confirm({
                        title: "确认删除",
                        content: "确定要删除这条告警信息吗？",
                        okText: "确定",
                        centered: "true",
                        cancelText: "取消",
                        onOk: () => handleDeleteAlarm(record.key),
                      });
                    }
                  }}
                />
              </Tooltip>
            ))}
          </Space>
        );
      },
    },
  ];

  const handleCreateAlert = (values) => {
    setCreateModalVisible(false);
    form.resetFields();
  };

  // 处理创建告警表单的重置
  useEffect(() => {
    if (createModalVisible) {
      form.resetFields();
    }
  }, [createModalVisible, form]);

  // 确保编辑表单在弹窗打开时已经准备好
  useEffect(() => {
    if (!editModalVisible) {
      editForm.resetFields();
    }
  }, [editModalVisible, editForm]);

  return (
    <div className="alert-info">

      {/* Filter Bar */}
      <Card className="filter-bar">
        <Space wrap>
          <label>告警状态:</label>
          <Select
            value={searchParams.alarmStatus}
            style={{ width: 120 }}
            placeholder="请选择告警类型"
            onChange={(value) =>
              setSearchParams((prev) => ({ ...prev, alarmStatus: value }))
            }
          >
            <Option value="">全部状态</Option>
            <Option value="0">未处理</Option>
            <Option value="1">已确认</Option>
            <Option value="2">已解决</Option>
            <Option value="3">已关闭</Option>
          </Select>
          <label>告警类型:</label>
          <Select
            value={searchParams.alarmType}
            style={{ width: 120 }}
            onChange={(value) =>
              setSearchParams((prev) => ({ ...prev, alarmType: value }))
            }
          >
            <Option value="">全部类型</Option>
            <Option value="0">驱鼠告警</Option>
            <Option value="1">设备告警</Option>
            <Option value="2">平台告警</Option>
            <Option value="3">升级告警</Option>
          </Select>
          <label>告警级别:</label>
          <Select
            value={searchParams.alarmLevel}
            style={{ width: 120 }}
            placeholder="请选择告警基本"
            onChange={(value) =>
              setSearchParams((prev) => ({ ...prev, alarmLevel: value }))
            }
          >
            <Option value="">全部级别</Option>
            <Option value="0">低</Option>
            <Option value="1">中</Option>
            <Option value="2">高</Option>
            <Option value="3">紧急</Option>
          </Select>
          {/* <Select defaultValue="" style={{ width: 120 }}>
            <Option value="">全部渠道</Option>
            <Option value="0">邮箱</Option>
            <Option value="1">短信</Option>
            <Option value="2">微信</Option>
            <Option value="3">钉钉</Option>
            <Option value="4">系统内部</Option>
          </Select> */}
          <label>告警时间:</label>
          <RangePicker
            value={
              searchParams.startTime
                ? [dayjs(searchParams.startTime), dayjs(searchParams.endTime)]
                : null
            }
            style={{ width: 350 }}
            showTime={{ format: "HH:mm:ss" }}
            format="YYYY-MM-DD HH:mm:ss"
            onChange={(dates) => {
              if (dates) {
                setSearchParams((prev) => ({
                  ...prev,
                  startTime: dates[0]?.format("YYYY-MM-DD HH:mm:ss"),
                  endTime: dates[1]?.format("YYYY-MM-DD HH:mm:ss"),
                }));
              } else {
                setSearchParams((prev) => ({
                  ...prev,
                  startTime: "",
                  endTime: "",
                }));
              }
            }}
          />
          <Space>
            {/* <Select 
              defaultValue="" 
              style={{ width: 120 }}
              placeholder="请选择接收者"
            >
              <Option value="">全部接收者</Option>
              {Array.isArray(userOptions) && userOptions.map(user => (
                <Option key={user.id} value={user.name}>{user.name}</Option>
              ))}
            </Select> */}
            <Button
              onClick={() => fetchAlertList(0)}
              type="primary"
              icon={<SearchOutlined />}
            >
              搜索
            </Button>
            <Button
              onClick={() => {
                const { size } = searchParams;
                const newParams = {
                  alarmType: "",
                  alarmLevel: "",
                  alarmStatus: "",
                  startTime: "",
                  endTime: "",
                  page: 0,
                  size,
                };
                setSearchParams(newParams);
                fetchAlertList(0, newParams);
              }}
            >
              重置
            </Button>
          </Space>
        </Space>
      </Card>

      {/* Statistics */}
      <Row gutter={[16, 16]} className="statistics-cards">
        {statistics.map((stat, index) => (
          <Col span={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Alert List */}
      <Card className="alert-list">
        <Table
          columns={columns}
          dataSource={alertList}
          loading={loading}
          scroll={{ y: alertList.length > 10 ? "62vh" : undefined }}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          pagination={{
            total: total,
            pageSize: searchParams.size,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              const newParams = {
                ...searchParams,
                page: page - 1,
                size: pageSize,
              };
              setSearchParams(newParams);
              fetchAlertList(page - 1, newParams);
            },
          }}
        />
      </Card>

      {/* Create Alert Modal */}
      <Modal
        title="创建告警"
        open={createModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        width={720}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateAlert}>
          <Form.Item name="title" label="告警标题" rules={[{ required: true }]}>
            <Input placeholder="请输入告警标题" />
          </Form.Item>

          <Form.Item
            name="content"
            label="告警内容"
            rules={[{ required: true }]}
          >
            <Input.TextArea rows={4} placeholder="请输入告警内容" />
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true }]}
          >
            <Radio.Group>
              <Radio.Button value="high">高</Radio.Button>
              <Radio.Button value="medium">中</Radio.Button>
              <Radio.Button value="low">低</Radio.Button>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            name="category"
            label="告警分类"
            rules={[{ required: true }]}
          >
            <Select placeholder="请选择告警分类">
              <Option value="system">系统通知</Option>
              <Option value="device">设备告警</Option>
              <Option value="business">业务通知</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="receivers"
            label="接收者"
            rules={[{ required: true }]}
          >
            <Select mode="multiple" placeholder="请选择接收者">
              <Option value="ops">运维组</Option>
              <Option value="admin">管理员</Option>
              <Option value="user1">用户1</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="channels"
            label="发送渠道"
            rules={[{ required: true }]}
          >
            <Select mode="multiple" placeholder="请选择发送渠道">
              <Option value="app">应用内</Option>
              <Option value="email">邮件</Option>
              <Option value="sms">短信</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="sendTime"
            label="发送时间"
            rules={[{ required: true }]}
          >
            <Radio.Group>
              <Radio value="now">立即发送</Radio>
              <Radio value="schedule">定时发送</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>

      {/* Alert Detail Modal */}
      <Drawer
        title="告警详情"
        open={detailModalVisible}
        onClose={() => setDetailModalVisible(false)}
        width={400}
      >
        {selectedAlert ? (
          <div style={{ display: 'flex', gap: '24px' }}>
            <div style={{ flex: 1 }}>
              <p>
                <strong>告警标题：</strong>
                {selectedAlert.title}
              </p>
              <p>
                <strong>设备ID：</strong>
                {selectedAlert.deviceId}
              </p>
              <p>
                <strong>告警状态：</strong>
                {renderAlarmStatus(selectedAlert.alarmStatus)}
              </p>
              <p>
                <strong>告警类型：</strong>
                {renderAlarmType(selectedAlert.alarmType)}
              </p>
              <p>
                <strong>告警等级：</strong>
                {renderAlarmLevel(selectedAlert.alarmLevel)}
              </p>
              <p>
                <strong>创建时间：</strong>
                {dayjs(selectedAlert.createdTime).format("YYYY-MM-DD HH:mm:ss")}
              </p>
              <p>
                <strong>告警内容：</strong>
                {selectedAlert.content}
              </p>
            </div>
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            加载中...
          </div>
        )}
      </Drawer>

      {/* Edit Alert Modal */}
      <Modal
        title="编辑告警信息:"
        open={editModalVisible}
        onOk={() => editForm.submit()}
        centered={true}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
        }}
        width={720}
        confirmLoading={loadingEdit}
      >
        <Form form={editForm} layout="vertical" onFinish={handleEditAlert}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="title"
                label="告警标题"
                rules={[{ required: true, message: "请输入告警标题" }]}
              >
                <Input placeholder="请输入告警标题" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="alarmStatus"
                label="告警状态"
                rules={[{ required: true, message: "请选择告警状态" }]}
              >
                <Select placeholder="请选择告警状态">
                  <Option value="0">新告警</Option>
                  <Option value="1">已确认</Option>
                  <Option value="2">已解决</Option>
                  <Option value="3">已关闭</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="alarmType"
                label="告警类型"
                rules={[{ required: true, message: "请选择告警类型" }]}
              >
                <Select placeholder="请选择告警类型">
                  <Option value="0">设备离线</Option>
                  <Option value="1">高温告警</Option>
                  <Option value="2">网络故障</Option>
                  <Option value="3">断电告警</Option>
                  <Option value="4">自定义告警</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="alarmLevel"
                label="告警等级"
                rules={[{ required: true, message: "请选择告警等级" }]}
              >
                <Select placeholder="请选择告警等级">
                  <Option value="0">低</Option>
                  <Option value="1">中</Option>
                  <Option value="2">高</Option>
                  <Option value="3">紧急</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="channel"
                label="通知渠道"
                rules={[{ required: true, message: "请选择通知渠道" }]}
              >
                <Select placeholder="请选择通知渠道">
                  <Option value="0">邮箱</Option>
                  <Option value="1">短信</Option>
                  <Option value="2">微信</Option>
                  <Option value="3">钉钉</Option>
                  <Option value="4">系统内部</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="receivers"
                label="接收人员"
                rules={[{ required: true, message: "请选择接收人员" }]}
              >
                <Select mode="multiple" placeholder="请选择接收人员">
                  {Array.isArray(userOptions) && userOptions.length > 0 ? (
                    userOptions.map((user) => (
                      <Option
                        key={user.id || Math.random()}
                        value={user.id || ""}
                      >
                        {user.name || "未知用户"}
                      </Option>
                    ))
                  ) : (
                    <Option value="" disabled>
                      暂无可选用户
                    </Option>
                  )}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="content"
            label="告警内容"
            rules={[{ required: true, message: "请输入告警内容" }]}
          >
            <Input.TextArea rows={4} placeholder="请输入告警内容" />
          </Form.Item>

          <Form.Item name="remark" label="告警备注">
            <Input.TextArea rows={2} placeholder="请输入告警备注" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AlertInfo;
