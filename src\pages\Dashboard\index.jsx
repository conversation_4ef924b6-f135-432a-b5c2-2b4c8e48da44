import { useState, useEffect } from 'react'
import {
  Button,
  Space,
} from 'antd'
import {
  DownloadOutlined,
} from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import dayjs from 'dayjs'
import GISMap from './components/GISMap'
import TopPanel from './components/TopPanel'
import LeftPanel from './components/LeftPanel'
import RightPanel from './components/RightPanel'
import BottomPanel from './components/BottomPanel'
import { getDashboardData,exportDashboardData } from './../../service/dashboard'
import './styles.css'

// const { RangePicker } = DatePicker;

const Dashboard = observer(() => {
  const [dashboardData, setDashboardData] = useState({})

  const fetchData = async () => {
    try {
      const res = await getDashboardData()
      setDashboardData(res)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    fetchData()
     // 清理函数，在组件卸载时取消请求
     return () => {
      if (window.cancelToken) {
        window.cancelToken() // 取消请求
      }
    }
  }, [])

  const handleExport = async () => {
    try {
      const result = await exportDashboardData();
      const blob = new Blob([result], { type: 'application/xlsx' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'download.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export failed:', error);
    }
  }

  return (
    <div className='dashboard-container'>
      {/* Header Controls */}
      <div className='dashboard-header'>
        <div className='header-left' style={{marginBottom: 0}}>
          <img src='/qsq.png' alt='Logo' className='dashboard-logo' />
          <div className='header-info'>
            <h1>生物驱离系统</h1>
            <span>数据更新时间：{dayjs().format('YYYY-MM-DD HH:mm:ss')}</span>
          </div>
        </div>
        <div className='header-controls'>
          <Space size='middle'>
            {/* <Select
              defaultValue="day"
              style={{ width: 120 }}
              onChange={setTimeRange}
              options={[
                { value: 'hour', label: '按小时' },
                { value: 'day', label: '按天' },
                { value: 'week', label: '按周' },
                { value: 'month', label: '按月' },
                { value: 'quarter', label: '按季' },
                { value: 'year', label: '按年' }
              ]}
            />
            <RangePicker value={dateRange} onChange={setDateRange} />
            <Select
              value={selectedArea}
              style={{ width: 120 }}
              onChange={setSelectedArea}
              options={areas}
            /> */}
            {/* <Button icon={<DownloadOutlined />} onClick={handleExport}>
              导出
            </Button> */}
          </Space>
        </div>
      </div>

      {/* Main Content */}
      <div className='dashboard-content'>
        {/* Top Panel */}
        <TopPanel
          {...{
            onlineRate: dashboardData?.onlineRate,
            totalDevices: dashboardData?.totalDevices,
            healthScore: dashboardData?.healthScore,
            alarms: dashboardData?.alarms            
          }}
        />

        {/* Main Layout */}
        <div className='main-layout'>
          {/* Left Panel */}
          <div className='left-panel'>
            <LeftPanel {...{
              deviceCountGroupByStatus: dashboardData?.deviceCountGroupByStatus,
            }}/>
          </div>

          {/* Center Map */}
          <div className='center-panel'>
            {/* <GISMap /> */}
            <BottomPanel {...{currentMonthRatEvents: dashboardData?.currentMonthRatEvents}}/>
          </div>

          {/* Right Panel */}
          <div className='right-panel'>
            <RightPanel {...{ratEventsGroupByCamera: dashboardData?.ratEventsGroupByCamera}}/>
          </div>
        </div>

        {/* Bottom Panel */}
        {/* <div className='bottom-panel'>
          <BottomPanel {...{currentMonthRatEvents: dashboardData?.currentMonthRatEvents}}/>
        </div> */}
      </div>
    </div>
  )
})

export default Dashboard
