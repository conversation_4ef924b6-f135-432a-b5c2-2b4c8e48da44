import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Breadcrumb,
  Input,
  Select,
  DatePicker,
  Tag,
  Badge,
  Progress,
  Avatar,
  Alert,
  Radio,
  Carousel,
  Tooltip,
  Modal,
  Form,
  Tabs
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  SettingOutlined,
  TableOutlined,
  AppstoreOutlined,
  CalendarOutlined,
  CloseOutlined,
  RightOutlined,
  LeftOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ShareAltOutlined,
  PushpinOutlined,
  BellOutlined,
  MailOutlined,
  MessageOutlined,
  ClockCircleOutlined,
  UserOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import './styles.css';

const { Search } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;

const AlertsNotice = () => {
  const [viewMode, setViewMode] = useState('card');
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [form] = Form.useForm();

  // Mock data for important notices
  const importantNotices = [
    {
      id: 1,
      title: '系统将于本周六22:00-次日06:00进行版本升级维护，期间服务不可用',
      type: 'system',
      importance: 'urgent'
    },
    {
      id: 2,
      title: '新版本功能预告：智能调度算法升级',
      type: 'feature',
      importance: 'important'
    }
  ];

  // Mock data for notices
  const notices = [
    {
      id: 1,
      title: '系统升级通知',
      content: '系统将进行版本升级维护，期间服务不可用...',
      type: 'system',
      importance: 'urgent',
      isPinned: true,
      publishTime: '2023-10-15',
      publisher: {
        name: '系统管理员',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin'
      },
      readRate: 78,
      channels: ['app', 'email', 'sms']
    },
    {
      id: 2,
      title: '新功能发布',
      content: '新增远程升级功能，支持批量设备升级...',
      type: 'feature',
      importance: 'normal',
      isPinned: false,
      publishTime: '2023-10-12',
      publisher: {
        name: '产品经理',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=pm'
      },
      readRate: 45,
      channels: ['app']
    }
  ];

  // Render notice card
  const renderNoticeCard = (notice) => (
    <Card
      className={`notice-card ${notice.importance} ${notice.isPinned ? 'pinned' : ''}`}
      actions={[
        <Tooltip title="查看详情">
          <EyeOutlined key="view" />
        </Tooltip>,
        <Tooltip title="分享">
          <ShareAltOutlined key="share" />
        </Tooltip>,
        <Tooltip title={notice.isPinned ? '取消置顶' : '置顶'}>
          <PushpinOutlined key="pin" />
        </Tooltip>
      ]}
    >
      <div className="notice-card-header">
        {notice.isPinned && <Tag color="red">置顶</Tag>}
        <Tag color={
          notice.importance === 'urgent' ? 'red' :
          notice.importance === 'important' ? 'orange' : 'blue'
        }>
          {notice.importance === 'urgent' ? '紧急' :
           notice.importance === 'important' ? '重要' : '普通'}
        </Tag>
        <Tag color={
          notice.type === 'system' ? 'cyan' :
          notice.type === 'feature' ? 'green' :
          notice.type === 'security' ? 'red' : 'blue'
        }>
          {notice.type === 'system' ? '系统通知' :
           notice.type === 'feature' ? '新功能' :
           notice.type === 'security' ? '安全公告' : '其他'}
        </Tag>
      </div>
      <div className="notice-card-title">{notice.title}</div>
      <div className="notice-card-content">{notice.content}</div>
      <div className="notice-card-footer">
        <Space>
          <Avatar src={notice.publisher.avatar} size="small" />
          <span>{notice.publisher.name}</span>
        </Space>
        <Space>
          <span>{notice.publishTime}</span>
          <Progress
            percent={notice.readRate}
            size="small"
            format={(percent) => `阅读: ${percent}%`}
          />
        </Space>
      </div>
      <div className="notice-card-channels">
        <Space>
          {notice.channels.includes('app') && (
            <Tooltip title="应用内通知">
              <BellOutlined />
            </Tooltip>
          )}
          {notice.channels.includes('email') && (
            <Tooltip title="邮件通知">
              <MailOutlined />
            </Tooltip>
          )}
          {notice.channels.includes('sms') && (
            <Tooltip title="短信通知">
              <MessageOutlined />
            </Tooltip>
          )}
        </Space>
      </div>
    </Card>
  );

  const handleCreateNotice = (values) => {
    console.log('Form values:', values);
    setCreateModalVisible(false);
    form.resetFields();
  };

  return (
    <div className="alerts-notice">
      {/* Header */}
      <div className="page-header">
        <div className="header-left">
          <Breadcrumb
            items={[
              { title: '首页' },
              { title: '告警通知' },
              { title: '通知公告' }
            ]}
          />
          <h2>通知公告</h2>
        </div>
        <div className="header-right">
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              发布公告
            </Button>
            <Button icon={<FileTextOutlined />}>模板管理</Button>
            <Button icon={<ReloadOutlined />}>刷新</Button>
          </Space>
        </div>
      </div>

      {/* Important Notice Banner */}
      <Card className="notice-banner">
        <Carousel
          autoplay
          dots={false}
          arrows
          prevArrow={<LeftOutlined />}
          nextArrow={<RightOutlined />}
        >
          {importantNotices.map(notice => (
            <div key={notice.id}>
              <Alert
                message={notice.title}
                type="error"
                banner
                action={
                  <Button size="small" icon={<CloseOutlined />} />
                }
              />
            </div>
          ))}
        </Carousel>
      </Card>

      {/* Filter Bar */}
      <Card className="filter-bar">
        <Space wrap>
          <Select defaultValue="all" style={{ width: 120 }}>
            <Option value="all">全部分类</Option>
            <Option value="system">系统通知</Option>
            <Option value="business">业务通知</Option>
            <Option value="marketing">营销通知</Option>
            <Option value="training">培训通知</Option>
            <Option value="security">安全公告</Option>
          </Select>
          <Select defaultValue="all" style={{ width: 120 }}>
            <Option value="all">全部状态</Option>
            <Option value="draft">草稿</Option>
            <Option value="published">已发布</Option>
            <Option value="expired">已过期</Option>
            <Option value="recalled">已撤回</Option>
          </Select>
          <Select defaultValue="all" style={{ width: 120 }}>
            <Option value="all">全部发布者</Option>
            <Option value="admin">系统管理员</Option>
            <Option value="pm">产品经理</Option>
          </Select>
          <RangePicker style={{ width: 240 }} />
          <Select defaultValue="all" style={{ width: 120 }}>
            <Option value="all">全部重要程度</Option>
            <Option value="urgent">紧急</Option>
            <Option value="important">重要</Option>
            <Option value="normal">普通</Option>
          </Select>
          <Select defaultValue="all" style={{ width: 120 }}>
            <Option value="all">全部渠道</Option>
            <Option value="app">应用内</Option>
            <Option value="email">邮件</Option>
            <Option value="sms">短信</Option>
          </Select>
          <Search
            placeholder="搜索公告..."
            style={{ width: 200 }}
            allowClear
          />
        </Space>
      </Card>

      {/* View Mode Switch */}
      <Card className="view-switch">
        <Space className="view-mode">
          <Radio.Group value={viewMode} onChange={(e) => setViewMode(e.target.value)}>
            <Radio.Button value="card">
              <AppstoreOutlined /> 卡片视图
            </Radio.Button>
            <Radio.Button value="list">
              <TableOutlined /> 列表视图
            </Radio.Button>
            <Radio.Button value="calendar">
              <CalendarOutlined /> 日历视图
            </Radio.Button>
          </Radio.Group>
        </Space>
        <Space>
          <span>排序：</span>
          <Select defaultValue="publishTime" style={{ width: 120 }}>
            <Option value="publishTime">发布时间</Option>
            <Option value="readRate">阅读率</Option>
            <Option value="importance">重要程度</Option>
            <Option value="title">标题</Option>
          </Select>
        </Space>
      </Card>

      {/* Notice Cards */}
      <div className="notice-grid">
        <Row gutter={[16, 16]}>
          {notices.map(notice => (
            <Col xs={24} sm={12} md={8} lg={6} key={notice.id}>
              {renderNoticeCard(notice)}
            </Col>
          ))}
        </Row>
      </div>

      {/* Create Notice Modal */}
      <Modal
        title="发布公告"
        open={createModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateNotice}
        >
          <Tabs
            items={[
              {
                key: 'basic',
                label: '基本信息',
                children: (
                  <>
                    <Form.Item
                      name="title"
                      label="公告标题"
                      rules={[{ required: true }]}
                    >
                      <Input placeholder="请输入公告标题" />
                    </Form.Item>

                    <Form.Item
                      name="type"
                      label="公告类型"
                      rules={[{ required: true }]}
                    >
                      <Select>
                        <Option value="system">系统通知</Option>
                        <Option value="business">业务通知</Option>
                        <Option value="marketing">营销通知</Option>
                        <Option value="training">培训通知</Option>
                        <Option value="security">安全公告</Option>
                      </Select>
                    </Form.Item>

                    <Form.Item
                      name="importance"
                      label="重要程度"
                      rules={[{ required: true }]}
                    >
                      <Radio.Group>
                        <Radio.Button value="urgent">紧急</Radio.Button>
                        <Radio.Button value="important">重要</Radio.Button>
                        <Radio.Button value="normal">普通</Radio.Button>
                      </Radio.Group>
                    </Form.Item>

                    <Form.Item
                      name="content"
                      label="公告内容"
                      rules={[{ required: true }]}
                    >
                      <Input.TextArea rows={6} placeholder="请输入公告内容" />
                    </Form.Item>
                  </>
                )
              },
              {
                key: 'publish',
                label: '发布设置',
                children: (
                  <>
                    <Form.Item
                      name="isPinned"
                      valuePropName="checked"
                      label="置顶设置"
                    >
                      <Radio.Group>
                        <Radio value={true}>置顶</Radio>
                        <Radio value={false}>不置顶</Radio>
                      </Radio.Group>
                    </Form.Item>

                    <Form.Item
                      name="validPeriod"
                      label="有效期"
                      rules={[{ required: true }]}
                    >
                      <RangePicker showTime style={{ width: '100%' }} />
                    </Form.Item>

                    <Form.Item
                      name="channels"
                      label="发布渠道"
                      rules={[{ required: true }]}
                    >
                      <Select mode="multiple">
                        <Option value="app">应用内</Option>
                        <Option value="email">邮件</Option>
                        <Option value="sms">短信</Option>
                      </Select>
                    </Form.Item>

                    <Form.Item
                      name="receivers"
                      label="接收范围"
                      rules={[{ required: true }]}
                    >
                      <Select mode="multiple">
                        <Option value="all">全部用户</Option>
                        <Option value="admin">管理员</Option>
                        <Option value="operator">运营人员</Option>
                      </Select>
                    </Form.Item>
                  </>
                )
              }
            ]}
          />
        </Form>
      </Modal>
    </div>
  );
};

export default AlertsNotice;