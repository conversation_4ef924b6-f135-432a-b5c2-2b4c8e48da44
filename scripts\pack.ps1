# Check if the dist directory exists and remove it
if (Test-Path -Path "dist" -PathType Container) {
    Remove-Item -Path "dist" -Recurse -Force
}

# Set NODE_ENV environment variable
$env:NODE_ENV = "production"
Write-Host "NODE_ENV set to $env:NODE_ENV"

# build the project
npm run build
# Check if the npm build command was successful
if (-not $?) {
    Write-Error "npm run build failed."
    exit 1
}

# Check if the build output exists
if (-not (Test-Path -Path "dist/index.html" -PathType Leaf)) {
    Write-Error "Build failed: dist/index.html not found after build."
    exit 1
}

Write-Host "Build successful: dist/index.html found."

# Check if the docker/context/vcloud-web/www directory exists and remove it
if (Test-Path -Path "docker/context/vcloud-web/www" -PathType Container) {
    Write-Host "Removing existing docker/context/vcloud-web/www directory..."
    Remove-Item -Path "docker/context/vcloud-web/www" -Recurse -Force
}

# Create the docker/context/vcloud-web/www directory
New-Item -Path "docker/context/vcloud-web/www" -ItemType Directory -Force | Out-Null

# Copy the contents of the dist directory to docker/context/vcloud-web/www
Write-Host "Copying build output from dist to docker/context/vcloud-web/www..."
Copy-Item -Path "dist\*" -Destination "docker/context/vcloud-web/www" -Recurse -Force

# Verify the copy operation
if (-not (Test-Path -Path "docker/context/vcloud-web/www/index.html" -PathType Leaf)) {
    Write-Error "Copy failed: docker/context/vcloud-web/www/index.html not found after copy."
    exit 1
}

Write-Host "Files copied successfully to docker/context/vcloud-web/www."

