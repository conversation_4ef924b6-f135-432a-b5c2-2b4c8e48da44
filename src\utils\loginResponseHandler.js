/**
 * 清空所有存储的token和用户数据
 * 在进入登录页面时调用，确保清除之前的登录状态
 */
export const clearStoredTokens = () => {
  try {
    // 清除localStorage中的token和用户数据
    localStorage.removeItem('token');
    localStorage.removeItem('userData');
    localStorage.removeItem('userMenu'); // 清除用户菜单权限缓存
    localStorage.removeItem('menuToken'); // 清除菜单对应的token
    localStorage.removeItem('logEntry'); // 清除登录日志
    localStorage.removeItem('lastPage'); // 清除上次访问页面记录

    // 清除sessionStorage中的token和用户数据
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('userData');
    sessionStorage.removeItem('userMenu'); // 清除用户菜单权限缓存
    sessionStorage.removeItem('layoutLoaded'); // 清除布局加载状态

    console.log('已清空所有存储的token、用户数据和权限缓存');
  } catch (error) {
    console.error('清空token时出错:', error);
  }
};

/**
 * 处理登录接口的响应数据
 * @param {Object} response - 来自登录API的响应对象
 * @returns {Object} 处理后的结果，包含success、message、data或error字段
 */
export const handleLoginResponse = (response) => {
  try {
    // 检查响应是否有效
    if (!response) {
      return {
        success: false,
        error: '登录失败：服务器未返回任何响应'
      };
    }

    // 检查响应状态码
    if (response.code === 200) {
      // 存储令牌到localStorage和Cookie
      if (response.data && response.data.token) {
        const token = response.data.token;
        
        // 优先存储到localStorage
        localStorage.setItem('token', token);
        
        // 同时设置到sessionStorage作为备份
        sessionStorage.setItem('token', token);
        
        // 不主动设置Cookie，由authStore.checkAuth来处理
      }

      // 存储用户数据到所有存储位置
      if (response.data && response.data.user) {
        const userData = JSON.stringify(response.data.user);
        localStorage.setItem('userData', userData);
        sessionStorage.setItem('userData', userData);
      }

      return {
        success: true,
        message: response.message || '登录成功',
        data: response.data || {}
      };
    } else {
      // 处理错误情况
      return {
        success: false,
        error: response.message || '登录失败：请检查用户名和密码'
      };
    }
  } catch (error) {
    console.error('处理登录响应时出错:', error);
    return {
      success: false,
      error: '登录处理过程中发生错误'
    };
  }
}; 