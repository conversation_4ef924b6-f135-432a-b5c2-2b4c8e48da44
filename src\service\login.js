import { API_PREFIX } from "./constant";
import { message } from 'antd';

const handleApiError = (response) => {
  return response.json().then(data => {
    if (response.status === 401) {
      message.error(data?.message);
      setTimeout(() => {
        window.location.href = '/login'
      }, 500);
    } else if (response.status === 400 || response.status === 404 || response.status === 500) {
      message.error(data?.message);
    } else if (response.status === 403) {
      // Handle forbidden
    }
    return Promise.reject(data);
  });
};

// 检查响应数据是否包含错误码
const checkResponseData = (data) => {
  if (data && data.code && data.code !== 200) {
    message.error(data.message || '操作失败');
    return Promise.reject(data);
  }
  return data;
};

// 登录相关接口
export const loginApi = {
  login: (account, password, captchaKey, captcha) => {
    return fetch(`${API_PREFIX["user-service"]}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        account,
        password,
        captchaKey,
        captcha
      })
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }else{
        return handleApiError(response);
      }
    })
  },

  register: (account, password) => {
    return fetch(`${API_PREFIX["user-service"]}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        account,
        password
      })
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  getCaptcha: () => {
    return fetch(`${API_PREFIX["user-service"]}/auth/captcha`, {
      method: 'GET'
    }).then(res => {
      const contentType = res.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return res.json().then(checkResponseData);
      } else {
        return res.text().then(text => {
          return {
            code: 200,
            data: {
              captchaText: '',
              captchaImage: text
            }
          };
        });
      }
    })
      .then(data => {
        // 处理返回的验证码数据
        if (data && data.code === 200 && data.data) {
          // 直接返回服务器的响应，但将字段名称映射到我们的预期格式
          return {
            code: 200,
            data: {
              captchaText: data.data.captchaKey || '',
              captchaImage: data.data.captcha || ''
            }
          };
        } else {
          return handleApiError(response);
        }
      })
  }
};

