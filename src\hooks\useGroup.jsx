import { useState, useEffect} from 'react'
import {
  Tabs,
  Tree,
} from 'antd'
import {
  GlobalOutlined,
  TeamOutlined
} from '@ant-design/icons'
import useSelect from "./useSelect"
import {
  getDeviceGroupsByLocation,
  getCustomerGroupsTree
} from './../service/deviceGroups'
import { use } from 'react'

export default function useGroup() {

  const [activeTab, setActiveTab] = useState('location') // 当前选中的tab
  const [selectedKeys, setSelectedKeys] = useState([]) // 选中的节点
  const [expandedKeys, setExpandedKeys] = useState([]) // 展开的节点
  const [treeData, setTreeData] = useState(null) // 树数据
  const [
    node,
    searchValue,
    autoExpandParent,
    filteredTreeData,
    setAutoExpandParent
  ] = useSelect(treeData, setExpandedKeys)
  const [loading,setLoading] = useState(false)

  const [searchParams, setSearchParams] = useState(null)

    //更新搜索条件
    const updateSeachParam = obj => {
      const tempObj = {
        isNotGroup: "",
        groupType: 1, // 0是行政区域，1是设备分组
        groupId: '', // 地理分组
        orgId: '' // 客户分组
      }
      setSearchParams(() => ({
        ...tempObj,
        ...obj
      }))
    }
  
    // 切换tab
    const onGroupTypeChange = key => {
      if (window.cancelToken) {
        window.cancelToken() // 取消请求
      }
      setTreeData(null)
      setActiveTab(key)
      if (key === 'location') {
        getLocationTree()
      } else {
        fetchCustomerGroupsTree()
      }
    }
  
    // 获取位置树
    const getLocationTree = async () => {
      setLoading(true)
      try {
        const res = await getDeviceGroupsByLocation(1)
        const key = []
        let defaultKey = null
        const transformNode = (item) => {
          key.push(item.id || item.locationId)
          if(item.isLocal && defaultKey === null){
            defaultKey = item.locationId
          }
          return {
            title: item.groupName || item.description,
            key: item.id || item.locationId,
            value: item.id || item.locationId,
            isLocal: item.isLocal,
            // selectable: !!item.isLocal,
            children:
              item.children && item.children.length > 0
                ? item.children.map(child => transformNode(child))
                : item.deviceLocationTreeVos &&
                  item.deviceLocationTreeVos.length > 0
                ? item.deviceLocationTreeVos.map(child =>
                    transformNode(child)
                  )
                : undefined
          }
        }
  
        const transformedData = res.map(item => transformNode(item))
        setTreeData([
          ...transformedData,
          { title: '未分组', key: 1, isLocal: 0 },
          { title: "未绑定摄像头", key: -1, isLocal: 0}
        ])
        setExpandedKeys(key)
        setSelectedKeys([defaultKey])
        const tempObj = {
          groupId: defaultKey,
          orgId: '',
        }
        updateSeachParam(tempObj)
        // fetchCameraList(tempObj)
        setLoading(false)
      } catch (error) {
        console.error('Failed to fetch location tree:', error)
      }
    }
  
    // 获取客户分组树
    const fetchCustomerGroupsTree = async () => {
      setLoading(true)
      try {
        const res = await getCustomerGroupsTree()
        const transformNode = item => ({
          title: item.ownershipName,
          key: item.ownershipId,
          value: item.ownershipId,
          children:
            item.children && item.children.length > 0
              ? item.children.map(child => transformNode(child))
              : undefined
        })
        const transformedData = res.map(item => transformNode(item))
        setTreeData([
          ...transformedData,
          { title: '未分组', key: 1, isLocal: 0 },
          { title: "未绑定摄像头", key: -1, isLocal: 0}
        ])
        setSelectedKeys([transformedData[0]?.key || 1]) // 设置当前选中的
        const tempObj = {
          orgId: transformedData[0]?.key || 1,
          groupType: '',
          groupId: ''
        }
        updateSeachParam(tempObj)
        setLoading(false)
      } catch (error) {
        console.error('Failed to fetch customer groups tree:', error)
      }
    }
  
    
    // 树节点选中
    const onTreeSelect = (selectedKeys, node) => {
      if(window.cancelToken){
        window.cancelToken()
      }
      if (selectedKeys.length > 0) {
        setSelectedKeys(selectedKeys)
        const tempObj = {
          [activeTab === 'location' ? 'groupId' : 'orgId']:
            selectedKeys[0] !== 1 ? selectedKeys[0] : '',
          groupType: activeTab === 'location' ? node.node.isLocal : '',
          isNotGroup: node.node.key === 1 ? 0 : -1,
        }
        updateSeachParam(tempObj)
      }
    }

    useEffect(() =>{
      getLocationTree()
    },[])

  const gropNode =  () => <>
        <Tabs
              activeKey={activeTab}
              onChange={onGroupTypeChange}
              items={[
                {
                  key: 'location',
                  label: (
                    <span>
                      <GlobalOutlined />
                      地理分组
                    </span>
                  )
                },
                {
                  key: 'customer',
                  label: (
                    <span>
                      <TeamOutlined />
                      客户分组
                    </span>
                  )
                }
              ]}
            />
          {node()}
          {treeData && (
            <div className='group-tree-scroll'>
                <Tree
              showIcon
              loading={loading}
              onSelect={onTreeSelect}
              onExpand={keys => {
                setExpandedKeys(keys)
                setAutoExpandParent(false) // 用户手动展开后关闭自动展开
              }}
              selectedKeys={selectedKeys}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              treeData={searchValue ? filteredTreeData : treeData}
              blockNode
            />
            </div>
          
          )}
  </>
  return [gropNode,searchParams]
}
