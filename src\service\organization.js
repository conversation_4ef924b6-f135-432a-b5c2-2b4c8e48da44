// API基础URL - 可以根据实际部署环境手动配置
import { API_PREFIX } from "./constant" // 如果API和前端在同一域名下，可以使用相对路径
import { getLoginEntry } from '../utils/loginLogger';
import { message } from 'antd';

const handleApiError = (response) => {
  return response.json().then(data => {
    if (response.status === 401) {
      message.error(data?.message);
      setTimeout(() => {
        // window.location.href = '/login'
      }, 500);
    } else if (response.status === 400 || response.status === 404 || response.status === 500) {
      message.error(data?.message);
    } else if (response.status === 403) {
      // Handle forbidden
    }
    return Promise.reject(data);
  });
};

// 检查响应数据是否包含错误码
const checkResponseData = (data) => {
  if (data && data.code && data.code !== 200) {
    message.error(data.message || '操作失败');
    return Promise.reject(data);
  }
  return data;
};

// 组织管理相关接口
export const organizationApi = {
  // 获取组织列表
  getOrgPage: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/org/page`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 获取租户下拉列表
  getTenantDropdown: () => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/tenant/dropdown-query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 获取用户下拉列表
  getUserDropdown: () => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/user/dropdown`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 获取组织树
  getOrgTree: () => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/org/tree`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 获取组织详情
  getOrgDetail: (id) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/org/${id}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 新增组织
  addOrg: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/org/add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 更新组织
  updateOrg: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/org/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 禁用组织
  disableOrg: (ids) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/org/disabled`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify({ ids })
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 启用组织
  enableOrg: (ids) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/org/enable`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify({ ids })
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 删除组织
  deleteOrg: (ids) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/org/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify({ ids })
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  }
};

