import { useState, useEffect, useReducer } from 'react'
import {
  Form,
  Input,
  Select,
  Checkbox,
  InputNumber,
  TimePicker,
  Button,
  Card,
  Row,
  Slider,
  Col,
  message,
  Switch
} from 'antd'
import PropTypes from 'prop-types'
import {
  addCustomModelConfig,
  updateCustomModelConfig
} from '../../service/strategyConfig'
// import { getAllRatAudio } from "./../../service/maintenanceAudio"
import dayjs from 'dayjs'

const { Option } = Select

const AddUpdataStrategy = ({
  handleBackToList,
  configType,
  isEditMode,
  strategyData
}) => {
  const [form] = Form.useForm()
  const [audioList, setAudioList] = useState([])
  const [, forceUpdate] = useReducer(x => x + 1, 0)
  const [checked, setChecked] = useState(false)

  // useEffect(() => {
  //   // Fetch audio files when component mounts
  //   const fetchAudioFiles = async () => {
  //     try {
  //       const response = await getAllRatAudio()
  //       console.log(response,777)
  //       if (response && response.data) {
  //         setAudioList(response.data)
  //       }
  //     } catch (error) {
  //       console.error('Failed to fetch audio files:', error)
  //     }
  //   }

  //   fetchAudioFiles()
  // }, [])

  useEffect(() => {
    if (isEditMode && strategyData) {
      // Convert time strings to dayjs objects for the form
      const timeFields = [
        'workStartTime',
        'workEndTime',
        'ultrasoundOpenTime',
        'ultrasoundCloseTime',
        'ledOpenTime',
        'ledCloseTime'
      ]

      const formData = { ...strategyData }
      timeFields.forEach(field => {
        if (formData[field]) {
          formData[field] = dayjs(formData[field], 'HH:mm')
        }
      })

      // Set form values
      form.setFieldsValue(formData)
    }
  }, [isEditMode, strategyData, form])

  const onFinish = async values => {
    ;[1, 2, 3, 4, 5, 6].forEach(element => {
      values[`powerMode${element}`] = values.powerMode
      values[`prePower${element}`] = values.prePower
    })

    delete values.powerMode
    delete values.prePower

    const timeFields = [
      'workStartTime',
      'workEndTime',
      'ultrasoundOpenTime',
      'ultrasoundCloseTime',
      'ledOpenTime',
      'ledCloseTime'
    ]

    timeFields.forEach(field => {
      if (values[field]) {
        values[field] = values[field].format('HH:mm')
      }
    })

    try {
      if (isEditMode) {
        await updateCustomModelConfig({
          ...values,
          id: strategyData.id,
          lockVersion: strategyData.strategyData
        })
        message.success('编辑成功')
      } else {
        await addCustomModelConfig(values)
        message.success('新增成功')
      }

      handleBackToList(true)
    } catch (error) {
      console.error('Failed to save strategy:', error)
      // message.error(error.message)
    }
  }

  const onFinishFailed = errorInfo => {
    console.error('Form validation failed:', errorInfo)
  }

  const renderChannelFields = channelNumber => {
    return (
      <Card
        title={`超声波通道${channelNumber}设置`}
        key={channelNumber}
        extra={
          channelNumber === 1 ? (
            <Checkbox
              checked={checked}
              onChange={e => {
                setChecked(e.target.checked)
                if (e.target.checked) {
                  const channel1Values = {
                    throttle: form.getFieldValue('throttle1'),
                    frequency: form.getFieldValue('frequency1'),
                    firstFrequency: form.getFieldValue('firstFrequency1'),
                    firstTime: form.getFieldValue('firstTime1'),
                    secondFrequency: form.getFieldValue('secondFrequency1'),
                    secondTime: form.getFieldValue('secondTime1'),
                    thirdFrequency: form.getFieldValue('thirdFrequency1'),
                    thirdTime: form.getFieldValue('thirdTime1'),
                    powerMode: form.getFieldValue('powerMode'),
                    prePower: form.getFieldValue('prePower')
                  }

                  // Set values for channels 2-6
                  for (let i = 2; i <= 6; i++) {
                    form.setFieldsValue({
                      [`throttle${i}`]: channel1Values.throttle,
                      [`frequency${i}`]: channel1Values.frequency,
                      [`firstFrequency${i}`]: channel1Values.firstFrequency,
                      [`firstTime${i}`]: channel1Values.firstTime,
                      [`secondFrequency${i}`]: channel1Values.secondFrequency,
                      [`secondTime${i}`]: channel1Values.secondTime,
                      [`thirdFrequency${i}`]: channel1Values.thirdFrequency,
                      [`thirdTime${i}`]: channel1Values.thirdTime,
                      [`powerMode${i}`]: channel1Values.powerMode,
                      [`prePower${i}`]: channel1Values.prePower
                    })
                  }
                }
              }}
            >
              {'所有超声波通道设置一致'}
            </Checkbox>
          ) : (
            ''
          )
        }
      >
        <Form.Item
          name={`throttle${channelNumber}`}
          label='是否开启'
          valuePropName='checked'
        >
          <Switch disabled={checked && channelNumber !== 1} />
        </Form.Item>

        <Form.Item
          name={`frequency${channelNumber}`}
          label='频率模式'
          rules={[{ required: true, message: '请选择频率模式' }]}
        >
          <Select
            onChange={value => {
              if (value === 1) {
                form.setFieldsValue({
                  [`firstFrequency${channelNumber}`]: undefined,
                  [`firstTime${channelNumber}`]: 30,
                  [`secondFrequency${channelNumber}`]: undefined,
                  [`secondTime${channelNumber}`]: 30,
                  [`thirdFrequency${channelNumber}`]: undefined,
                  [`thirdTime${channelNumber}`]: 30
                })
              } else if (value === 2) {
                form.setFieldsValue({
                  [`secondFrequency${channelNumber}`]: undefined,
                  [`secondTime${channelNumber}`]: 30,
                  [`thirdFrequency${channelNumber}`]: undefined,
                  [`thirdTime${channelNumber}`]: 30
                })
              }
            }}
            style={{ width: '100%' }}
            disabled={checked && channelNumber !== 1}
          >
            <Option value={1}>自动变频模式</Option>
            <Option value={2}>固定频率模式</Option>
            <Option value={3}>固定跳频模式</Option>
          </Select>
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return (
              prev[`frequency${channelNumber}`] !==
              next[`frequency${channelNumber}`]
            )
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`firstFrequency${channelNumber}`}
              label={`频率1`}
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`frequency${channelNumber}`) !== 1,
                  message: '请输入频率1'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                disabled={
                  getFieldValue(`frequency${channelNumber}`) === 1 ||
                  (checked && channelNumber !== 1)
                }
                suffix='KHz'
                min={20}
                max={65}
                step={0.1}
                placeholder='请输入20~65'
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return (
              prev[`frequency${channelNumber}`] !==
              next[`frequency${channelNumber}`]
            )
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`firstTime${channelNumber}`}
              label='持续时长'
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`frequency${channelNumber}`) !== 1,
                  message: '请输入持续时长'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                suffix='s'
                min={30}
                max={300}
                placeholder='请输入30s~300s'
                disabled={
                  getFieldValue(`frequency${channelNumber}`) === 1 ||
                  (checked && channelNumber !== 1)
                }
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return (
              prev[`frequency${channelNumber}`] !==
              next[`frequency${channelNumber}`]
            )
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`secondFrequency${channelNumber}`}
              label='频率2'
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`frequency${channelNumber}`) === 3,
                  message: '请输入频率2'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                suffix='KHz'
                min={20}
                max={65}
                step={0.1}
                placeholder='请输入20~65'
                disabled={
                  getFieldValue(`frequency${channelNumber}`) !== 3 ||
                  (checked && channelNumber !== 1)
                }
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return (
              prev[`frequency${channelNumber}`] !==
              next[`frequency${channelNumber}`]
            )
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`secondTime${channelNumber}`}
              label='持续时长'
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`frequency${channelNumber}`) === 3,
                  message: '请输入持续时长'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                suffix='s'
                min={30}
                max={300}
                placeholder='请输入30s~300s'
                disabled={
                  getFieldValue(`frequency${channelNumber}`) !== 3 ||
                  (checked && channelNumber !== 1)
                }
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return (
              prev[`frequency${channelNumber}`] !==
              next[`frequency${channelNumber}`]
            )
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`thirdFrequency${channelNumber}`}
              label='频率3'
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`frequency${channelNumber}`) === 3,
                  message: '请输入频率3'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                suffix='KHz'
                min={20}
                max={65}
                step={0.1}
                placeholder='请输入20~65'
                disabled={
                  getFieldValue(`frequency${channelNumber}`) !== 3 ||
                  (checked && channelNumber !== 1)
                }
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return (
              prev[`frequency${channelNumber}`] !==
              next[`frequency${channelNumber}`]
            )
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`thirdTime${channelNumber}`}
              label='持续时长'
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`frequency${channelNumber}`) === 3,
                  message: '请输入持续时长'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                suffix='s'
                min={30}
                max={300}
                placeholder='请输入30s~300s'
                disabled={
                  getFieldValue(`frequency${channelNumber}`) !== 3 ||
                  (checked && channelNumber !== 1)
                }
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item
          name={`powerMode`}
          label='强度模式'
          rules={[{ required: true, message: '请选择强度模式' }]}
        >
          <Select
            onChange={value => {
              if (value === 1) {
                form.setFieldsValue({
                  [`prePower`]: undefined
                })
              }
            }}
            style={{ width: '100%' }}
            disabled={checked && channelNumber !== 1}
          >
            <Option value={1}>自动强度模式</Option>
            <Option value={2}>固定强度模式</Option>
          </Select>
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          wrapperCol={{ span: 24 }}
          shouldUpdate={(prev, next) => {
            return prev[`powerMode`] !== next[`powerMode`]
          }}
        >
          {({ getFieldValue }) => (
            <Form.Item
              name={`prePower`}
              label='预设强度'
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue(`powerMode`) === 2,
                  message: '请输入预设强度'
                })
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={10}
                max={100}
                suffix='%'
                placeholder='请输入10~100'
                disabled={
                  getFieldValue(`powerMode`) !== 2 ||
                  (checked && channelNumber !== 1)
                }
              />
            </Form.Item>
          )}
        </Form.Item>
      </Card>
    )
  }

  const renderBasicInfo = () => (
    <Card title='基本信息' style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item
            name='name'
            label='策略名称'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            rules={[{ required: true, message: '请输入策略名称' }]}
          >
            <Input style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            name='typeCode'
            label='策略类型'
            rules={[{ required: true, message: '请选择策略类型' }]}
          >
            <Select
              placeholder='请选择策略类型'
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              options={configType.map(item => ({
                value: item.key,
                label: item.title
              }))}
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>

        <Col span={6}>
          <Form.Item name='workStartTime' label='工作开始时间'>
            <TimePicker format='HH:mm' style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item name='workEndTime' label='工作结束时间'>
            <TimePicker format='HH:mm' style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16} style={{ height: 56 }}>
        <Col span={6}>
          <Form.Item
            name='ultrasoundWorkMode'
            label='超声波工作模式'
            required
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <Select
              onChange={() => {
                form.setFieldsValue({
                  ultrasoundDuration: 30,
                  ultrasoundCloseTime: undefined,
                  ultrasoundOpenTime: undefined
                })
              }}
              style={{ width: '100%' }}
            >
              <Option value='always'>持续模式</Option>
              <Option value='trigger'>触发模式</Option>
              <Option value='switch'>开关模式</Option>
              <Option value='unmanned'>无人工作模式</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            shouldUpdate={(prev, next) => {
              return prev.ultrasoundWorkMode !== next.ultrasoundWorkMode
            }}
          >
            {({ getFieldValue }) => (
              <Form.Item
                name='ultrasoundDuration'
                label='持续时间'
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
                rules={[
                  ({ getFieldValue }) => ({
                    required: getFieldValue('ultrasoundWorkMode') === 'trigger',
                    message: '触发模式下请输入持续时间'
                  })
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  suffix='s'
                  min={30}
                  max={300}
                  placeholder='请输入30s~300s'
                  disabled={getFieldValue('ultrasoundWorkMode') !== 'trigger'}
                />
              </Form.Item>
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            shouldUpdate={(prev, next) => {
              return prev.ultrasoundWorkMode !== next.ultrasoundWorkMode
            }}
          >
            {({ getFieldValue }) => (
              <Form.Item
                name='ultrasoundOpenTime'
                label='开始时间'
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
              >
                <TimePicker
                  format='HH:mm'
                  style={{ width: '100%' }}
                  disabled={getFieldValue('ultrasoundWorkMode') !== 'switch'}
                />
              </Form.Item>
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            shouldUpdate={(prev, next) => {
              return prev.ultrasoundWorkMode !== next.ultrasoundWorkMode
            }}
          >
            {({ getFieldValue }) => (
              <Form.Item
                name='ultrasoundCloseTime'
                label='结束时间'
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
              >
                <TimePicker
                  format='HH:mm'
                  style={{ width: '100%' }}
                  disabled={getFieldValue('ultrasoundWorkMode') !== 'switch'}
                />
              </Form.Item>
            )}
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16} style={{ height: 56 }}>
        <Col span={6}>
          <Form.Item name='ledWorkMode' label='灯光工作模式' required>
            <Select
              onChange={() => {
                form.setFieldsValue({
                  ledDuration: 30,
                  ledCloseTime: undefined,
                  ledOpenTime: undefined
                })
              }}
              style={{ width: '100%' }}
            >
              <Option value='always'>持续模式</Option>
              <Option value='trigger'>触发模式</Option>
              <Option value='switch'>开关模式</Option>
              <Option value='unmanned'>无人工作模式</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            shouldUpdate={(prev, next) => {
              return prev.ledWorkMode !== next.ledWorkMode
            }}
          >
            {({ getFieldValue }) => (
              <Form.Item
                name='ledDuration'
                label='持续时间'
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
                rules={[
                  ({ getFieldValue }) => ({
                    required: getFieldValue('ledWorkMode') === 'trigger',
                    message: '触发模式下请输入持续时间'
                  })
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  suffix='s'
                  min={30}
                  max={300}
                  placeholder='请输入30s~300s'
                  disabled={getFieldValue('ledWorkMode') !== 'trigger'}
                />
              </Form.Item>
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            shouldUpdate={(prev, next) => {
              return prev.ledWorkMode !== next.ledWorkMode
            }}
          >
            {({ getFieldValue }) => (
              <Form.Item
                name='ledOpenTime'
                label='开始时间'
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
              >
                <TimePicker
                  format='HH:mm'
                  style={{ width: '100%' }}
                  disabled={getFieldValue('ledWorkMode') !== 'switch'}
                />
              </Form.Item>
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            shouldUpdate={(prev, next) => {
              return prev.ledWorkMode !== next.ledWorkMode
            }}
          >
            {({ getFieldValue }) => (
              <Form.Item
                name='ledCloseTime'
                label='结束时间'
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
              >
                <TimePicker
                  format='HH:mm'
                  style={{ width: '100%' }}
                  disabled={getFieldValue('ledWorkMode') !== 'switch'}
                />
              </Form.Item>
            )}
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item name='soundWorkMode' label='喇叭工作模式' required>
            <Select disabled style={{ width: '100%' }}>
              <Option value='always'>持续模式</Option>
              <Option value='trigger'>触发模式</Option>
              <Option value='switch'>开关模式</Option>
              <Option value='unmanned'>无人工作模式</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
    </Card>
  )

  const renderLEDSettings = () => (
    <Card title='灯光设置' style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name='ledColor'
            label='灯光颜色'
            rules={[{ required: true, message: '请选择灯光颜色' }]}
          >
            <Select style={{ width: '100%' }}>
              <Option value={'1'}>白色</Option>
              <Option value={'2'}>蓝色</Option>
              <Option value={'3'}>蓝白混合</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='ledPower'
            label='灯光强度'
            rules={[{ required: true, message: '请选择灯光强度' }]}
          >
            <div
              style={{
                width: '100%',
                display: 'flex',
                alignItems: 'center',
                gap: '16px'
              }}
            >
              <Slider
                min={0}
                max={100}
                style={{ flex: 1 }}
                defaultValue={50}
                tooltip={{ open: true }}
                onChange={value => {
                  form.setFieldValue('ledPower', value)
                  forceUpdate()
                }}
                value={form.getFieldValue('ledPower')}
              />
              <InputNumber
                min={0}
                max={100}
                style={{ width: '50px' }}
                controls={false}
                defaultValue={50}
                onChange={value => {
                  form.setFieldValue('ledPower', value)
                  forceUpdate()
                }}
                value={form.getFieldValue('ledPower')}
              />
            </div>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='ledTime'
            label='持续点亮时长'
            rules={[{ required: true, message: '请输入持续点亮时长' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              suffix='s'
              min={0.1}
              max={1}
              step={0.01}
              placeholder='请输入0.1s~1s'
            />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='ledFrequency'
            label='间隔时长'
            rules={[{ required: true, message: '请输入1s~10s' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              suffix='s'
              max={10}
              min={1}
              step={0.1}
              placeholder='请输入1s~10s'
            />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  )

  const renderSpeakerSettings = () => (
    <Card title='可闻声设置' style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name='playFrequency'
            label='播放强度'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            rules={[{ required: true, message: '请选择播放强度' }]}
          >
            <div
              style={{
                width: '100%',
                display: 'flex',
                alignItems: 'center',
                gap: '16px'
              }}
            >
              <Slider
                min={0}
                max={100}
                style={{ flex: 1 }}
                defaultValue={50}
                value={form.getFieldValue('playFrequency')}
                onChange={value => {
                  form.setFieldsValue({ playFrequency: value })
                  forceUpdate()
                }}
                tooltip={{ open: true }}
              />
              <InputNumber
                min={0}
                max={100}
                style={{ width: '50px' }}
                controls={false}
                defaultValue={50}
                value={form.getFieldValue('playFrequency')}
                onChange={value => {
                  form.setFieldsValue({ playFrequency: value })
                  forceUpdate()
                }}
              />
            </div>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='soundSleepTime'
            label='可闻声间隔时间'
            rules={[{ required: true, message: '请输入可闻声间隔时间' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              suffix='s'
              min={30}
              max={600}
              placeholder='请输入30~600'
            />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name='soundRepetitions'
            label='重复播放次数'
            rules={[{ required: true, message: '请输入重复播放次数' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              placeholder='请输入>=1'
            />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            name='fileName'
            label='播放文件'
            rules={[{ required: true, message: '请选择播放文件' }]}
          >
            <Input style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  )

  return (
    <Form
      form={form}
      onFinishFailed={onFinishFailed}
      layout='horizontal'
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      onFinish={onFinish}
      onValuesChange={value => {
        if (checked) {
          const key = Object.keys(value)[0]
          const val = value[key]
          const tempArray1 = [
            'throttle1',
            'frequency1',
            'firstFrequency1',
            'firstTime1',
            'secondFrequency1',
            'secondTime1',
            'thirdFrequency1',
            'thirdTime1'
          ]
          if (tempArray1.includes(key)) {
            for (let i = 2; i <= 6; i++) {
              form.setFieldsValue({
                [`${key.slice(0, -1)}${i}`]: val
              })
            }
          }
        }
      }}
      initialValues={{
        ultrasoundDuration: 30,
        ultrasoundWorkMode: 'always',
        ledWorkMode: 'always',
        soundWorkMode: 'trigger',
        ledDuration: 30,
        ledPower: 50,
        ledFrequency: 1,
        ledTime: 1,
        playFrequency: 50,
        soundSleepTime: 30,
        powerMode: 1,
        thirdTime1: 30,
        firstTime1: 30,
        secondTime1: 30,
        thirdTime2: 30,
        firstTime2: 30,
        secondTime2: 30,
        thirdTime3: 30,
        firstTime3: 30,
        secondTime3: 30,
        thirdTime4: 30,
        firstTime4: 30,
        secondTime4: 30,
        thirdTime5: 30,
        firstTime5: 30,
        secondTime5: 30,
        thirdTime6: 30,
        firstTime6: 30,
        secondTime6: 30,
        frequency1: 1,
        frequency2: 1,
        frequency3: 1,
        frequency4: 1,
        frequency5: 1,
        frequency6: 1
      }}
    >
      {renderBasicInfo()}
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(4, 1fr)',
          gap: '16px',
          marginBottom: '16px'
        }}
      >
        <div>{renderChannelFields(1)}</div>
        <div>{renderChannelFields(2)}</div>
        <div>{renderChannelFields(3)}</div>
        <div>{renderChannelFields(4)}</div>
        <div>{renderChannelFields(5)}</div>
        <div>{renderChannelFields(6)}</div>
        <div>{renderLEDSettings()}</div>
        <div>{renderSpeakerSettings()}</div>
      </div>

      <Form.Item
        style={{ marginTop: 16, textAlign: 'right' }}
        wrapperCol={{ span: 24 }}
      >
        <Button onClick={handleBackToList} style={{ marginRight: 10 }}>
          取消
        </Button>
        <Button type='primary' htmlType='submit'>
          {isEditMode ? '编辑' : '新增'}
        </Button>
      </Form.Item>
    </Form>
  )
}

export default AddUpdataStrategy

AddUpdataStrategy.propTypes = {
  handleBackToList: PropTypes.func.isRequired,
  configType: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string,
      key: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
    })
  ).isRequired,
  isEditMode: PropTypes.bool.isRequired,
  strategyData: PropTypes.object
}
