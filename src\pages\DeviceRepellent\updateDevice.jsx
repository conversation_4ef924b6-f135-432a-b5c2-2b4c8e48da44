import { Modal, Form, Input } from 'antd'
import PropTypes from 'prop-types'
import { useEffect } from 'react'
import dayjs from 'dayjs'

const UpdateDevice = ({ visible, onOk, onCancel, currentDevice }) => {
  const [form] = Form.useForm()
  
  useEffect(() => {
    if (currentDevice) {
      const formValues = {
        ...currentDevice,
        registrationDate: currentDevice.registrationDate
          ? dayjs(currentDevice.registrationDate)
          : null,
        lastOnlineTime: currentDevice.lastOnlineTime
          ? dayjs(currentDevice.lastOnlineTime)
          : null
      }
      form.setFieldsValue(formValues)
    }
  }, [currentDevice, form])

  return (
    <Modal
      title={`修改设备-${currentDevice?.deviceName}`}
      open={visible}
      onOk={() => form.submit()}
      okText={"修改"}
      onCancel={onCancel}
      width={500}
    >
      <Form form={form} layout='vertical' onFinish={onOk}>
        <Form.Item
          name='deviceNo'
          label='驱鼠器编号'
        >
          <Input disabled />
        </Form.Item>
        <Form.Item
          name='deviceName'
          label='驱鼠器名称'
          rules={[{ required: true, message: '驱鼠器名称必填' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item name='lockVersion' label='乐观锁' hidden>
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  )
}

UpdateDevice.propTypes = {
  visible: PropTypes.bool.isRequired,
  onOk: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  currentDevice: PropTypes.object
}

export default UpdateDevice
