.file-management {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.page-header {
  margin-bottom: 24px;
}

.header-left {
  margin-bottom: 16px;
}

.header-left h2 {
  margin: 8px 0 0;
  font-size: 24px;
  font-weight: 500;
}

.file-content {
  flex: 1;
  min-height: 0;
}

.folder-tree {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tree-header {
  margin-bottom: 16px;
}

.folder-tree .ant-tree {
  flex: 1;
  overflow: auto;
}

.file-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Upload Modal styles */
.ant-upload-drag {
  padding: 24px;
}

.ant-upload-drag-icon {
  margin-bottom: 16px;
}

.ant-upload-text {
  margin-bottom: 8px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .file-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .file-content {
    flex-direction: column;
  }

  .folder-tree,
  .file-list {
    margin-bottom: 16px;
  }

  .ant-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .list-header {
    flex-direction: column;
    gap: 16px;
  }

  .ant-modal {
    max-width: 100vw;
    margin: 8px;
  }
}