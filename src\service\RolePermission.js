// API基础URL - 可以根据实际部署环境手动配置
import { API_PREFIX } from "./constant"
import { getLoginEntry } from '../utils/loginLogger';
import { message } from 'antd';

const handleApiError = (response) => {
  return response.json().then(data => {
    if (response.status === 401) {
      message.error(data?.message);
      setTimeout(() => {
        window.location.href = '/login'
      }, 500);
    } else if (response.status === 400 || response.status === 404 || response.status === 500) {
      message.error(data?.message);
    } else if (response.status === 403) {
      // Handle forbidden
    }
    return Promise.reject(data);
  });
};

// 检查响应数据是否包含错误码
const checkResponseData = (data) => {
  if (data && data.code && data.code !== 200) {
    message.error(data.message || '操作失败');
    return Promise.reject(data);
  }
  return data;
};

// 角色管理相关接口
export const rolePermissionApi = {
  // 获取租户下拉列表
  getTenantDropdown: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/tenant/dropdown-query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 获取角色分页列表
  getRolePage: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/role-permission/role/page`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 根据角色ID获取角色详情
  getRoleById: (id) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/role-permission/role/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 更新角色信息
  updateRole: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/role-permission/role/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 添加角色
  addRole: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/role-permission/role/add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 删除角色
  deleteRoles: (ids) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/role-permission/role/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify({ ids })
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 获取权限菜单树
  getPermissionMenu: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/role-permission/permission/menu`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 获取权限树
  getPermissionTree: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/role-permission/permission/tree`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 更新角色权限
  updateRolePermission: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/role-permission/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 获取权限数量统计
  getPermissionNum: (roleId) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/role-permission/permission/num?roleId=${roleId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      }
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  },

  // 添加权限
  addPermission: (params) => {
    const token = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/role-permission/permission/add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(params)
    }).then(response => {
      if(response.status === 200){
        return response.json().then(checkResponseData);
      }
      return handleApiError(response);
    })
  }
};

