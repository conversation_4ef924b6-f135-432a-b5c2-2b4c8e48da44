import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Table,
  Tag,
  Breadcrumb,
  Input,
  Select,
  Tree,
  Upload,
  Modal,
  Form,
  Tooltip,
  message,
} from "antd";
import {
  SearchOutlined,
  DeleteOutlined,
  EyeOutlined,
  CloudUploadOutlined,
  DownloadOutlined
} from "@ant-design/icons";
import dayjs from "dayjs";
import { fileManagementApi } from "../../service/fileManagement";
import "./styles.css";

const { Search } = Input;
const { Option } = Select;
const { DirectoryTree } = Tree;

const FileManagement = () => {
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [createFolderVisible, setCreateFolderVisible] = useState(false);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [uploadFileList, setUploadFileList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [fileType, setFileType] = useState("");
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewFileData, setPreviewFileData] = useState(null);
  const [pagination, setPagination] = useState({
    total: 0,
    current: 1,
    pageSize: 10,
  });

  // 获取文件列表数据
  const fetchFileList = async (
    page = 1,
    pageSize = 10,
    filename = "",
    contentType = ""
  ) => {
    try {
      setLoading(true);
      const params = {
        page,
        pageSize,
        filename,
        contentType,
      };
      const response = await fileManagementApi.getFileList(params);
      if (response.code === 200) {
        setFileList(response.data.content || []);
        setPagination((prev) => ({
          ...prev,
          total: response.data.totalElements,
          current: page,
          pageSize,
        }));
      }
    } catch (error) {
      console.error("获取文件列表失败", error);
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    fetchFileList();
  }, []);

  // Table columns
  const columns = [
    {
      title: "文件名",
      dataIndex: "filename",
      key: "filename",
      width: "15%",
      ellipsis: true,
      align: "center",
    },
    {
      title: "原始文件名",
      dataIndex: "originalFilename",
      key: "originalFilename",
      width: "15%",
      ellipsis: true,
      align: "center",
    },
    {
      title: "文件状态",
      dataIndex: "status",
      key: "status",
      align: "center",
      // width: "5%",
      render: (status) => (
        <Tag color={status === 0 ? "success" : "error"}>
          {status === 0 ? "正常" : "删除"}
        </Tag>
      ),
    },
    {
      title: "文件大小(B)",
      dataIndex: "size",
      key: "size",
      align: "center",
    },
    {
      title: "上传者",
      dataIndex: "uploader",
      key: "uploader",
      align: "center",
    },
    {
      title: "上传时间",
      dataIndex: "uploadTime",
      key: "uploadTime",
      align: "center",
      render: (text) => dayjs(text).format("YYYY-MM-DD HH:mm:ss"),
    },

    {
      title: "下载次数",
      dataIndex: "downloadNum",
      key: "downloadNum",
      // width: "5%",
      align: "center",
    },
    {
      title: "操作",
      key: "action",
      align: "center",
      render: (_, record) => (
        <Space>
          {/* <Tooltip title="预览">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={async () => {
                try {
                  const response = await fileManagementApi.getFileDetail(
                    record.id
                  );
                  setPreviewFileData(response);
                  setPreviewModalVisible(true);
                } catch (error) {
                  console.error("获取文件详情失败", error);
                }
              }}
            />
          </Tooltip> */}
          <Tooltip title="下载" key={`download-${record.id}`}>
            <Button
              type="text"
              icon={<DownloadOutlined />}
              onClick={async () => {
                try {
                  const blob = await fileManagementApi.downloadFile(record.id);
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement("a");
                  a.href = url;
                  a.download = record.originalFilename || record.filename;
                  document.body.appendChild(a);
                  a.click();
                  window.URL.revokeObjectURL(url);
                  document.body.removeChild(a);
                } catch (error) {
                  console.error("文件下载失败", error);
                }
              }}
            />
          </Tooltip>
          <Tooltip title="删除" key={`delete-${record.id}`}>
            <Button
              danger
              type="text"
              icon={<DeleteOutlined />}
              onClick={() => {
                Modal.confirm({
                  title: "确认删除",
                  content: "确定要删除这个文件吗？",
                  okText: "确定",
                  cancelText: "取消",
                  centered: true,
                  onOk: async () => {
                    try {
                      await fileManagementApi.deleteFile(record.id);
                      message.success("删除成功");
                      fetchFileList(
                        pagination.current,
                        pagination.pageSize,
                        searchText,
                        fileType
                      );
                    } catch (error) {
                      console.error("删除失败", error);
                    }
                  },
                });
              }}
            />
          </Tooltip>
          {/* <Tooltip title="分享">
            <Button type="text" icon={<ShareAltOutlined />} />
          </Tooltip>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'rename',
                  icon: <EditOutlined />,
                  label: '重命名'
                },
                {
                  key: 'copy',
                  icon: <CopyOutlined />,
                  label: '复制'
                },
                {
                  key: 'delete',
                  icon: <DeleteOutlined />,
                  label: '删除',
                  danger: true
                }
              ]
            }}
          >
            <Button type="text" icon={<FilterOutlined />} />
          </Dropdown> */}
        </Space>
      ),
    },
  ];

  const handleCreateFolder = (values) => {
    message.success("文件夹创建成功！");
    setCreateFolderVisible(false);
    form.resetFields();
  };

  return (
    <div className="file-management">

      {/* Main Content */}
      <Row gutter={16} className="file-content">
        {/* File List */}
        <Col span={24}>
          <Card className="file-list">
            <div className="list-header">
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={24} md={24} lg={24}>
                  <Space wrap>
                    <label>文件类型:</label>
                    <Select
                      defaultValue=""
                      style={{ width: 120 }}
                      onChange={(value) => setFileType(value)}
                    >
                      <Option value="">全部类型</Option>
                      <Option value="audio">音频</Option>
                      <Option value="video">视频</Option>
                      <Option value="image">图片</Option>
                      <Option value="document">文档</Option>
                      <Option value="sheet">表格</Option>
                    </Select>
                    <label>搜索:</label>
                    <Space.Compact>
                      <Input
                        placeholder="搜索文件的原始文件名..."
                        style={{ width: 200 }}
                        allowClear
                        value={searchText}
                        onChange={(e) => setSearchText(e.target.value)}
                      />
                      <Button
                        icon={<SearchOutlined />}
                        style={{ borderRadius: "5px", marginLeft: "10px" }}
                        type="primary"
                        onClick={() => {
                          const params = {
                            page: 1,
                            pageSize: pagination.pageSize,
                            filename: searchText,
                            contentType: fileType,
                          };
                          fetchFileList(
                            params.page,
                            params.pageSize,
                            params.filename,
                            params.contentType
                          );
                        }}
                      >
                        搜索
                      </Button>
                      <Button
                        style={{ borderRadius: "5px", marginLeft: "10px" }}
                        onClick={() => {
                          setSearchText("");
                          setFileType("");
                          fetchFileList(1, pagination.pageSize, "", "");
                        }}
                      >
                        重置
                      </Button>
                    </Space.Compact>
                    <Button
                      type="primary"
                      icon={<CloudUploadOutlined />}
                      onClick={() => setUploadModalVisible(true)}
                    >
                      上传文件
                    </Button>
                  </Space>
                </Col>
              </Row>
            </div>

            <Table
              columns={columns}
              dataSource={fileList.map(item => ({...item, key: item.id}))}
              scroll={{ y: fileList.length > 10 ? 900 : undefined }}
              pagination={{
                total: pagination.total,
                pageSize: pagination.pageSize,
                current: pagination.current,
                showQuickJumper: true,
                showSizeChanger: true,
                showTotal: (total) => `共 ${total} 个文件`,
                onChange: (page, pageSize) => {
                  fetchFileList(page, pageSize, searchText, fileType);
                },
              }}
              loading={loading}
            />
          </Card>
        </Col>
      </Row>

      {/* Upload Modal */}
      <Modal
        title="上传文件"
        open={uploadModalVisible}
        onCancel={() => {
          setUploadModalVisible(false);
          setUploadFileList([]);
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setUploadModalVisible(false);
              setUploadFileList([]);
            }}
          >
            取消
          </Button>,
          <Button
            key="upload"
            type="primary"
            onClick={async () => {
              if (uploadFileList.length === 0) {
                message.warning("请选择要上传的文件");
                return;
              }
              try {
                const response = await fileManagementApi.batchUploadFiles(
                  uploadFileList.map((file) => file.originFileObj)
                );
                if (response && !response.error) {
                  message.success("文件上传成功");
                  setUploadModalVisible(false);
                  setUploadFileList([]);
                  fetchFileList(
                    pagination.current,
                    pagination.pageSize,
                    searchText,
                    fileType
                  );
                }
              } catch (error) {
                console.error("文件上传失败", error);
              }
            }}
          >
            确定上传
          </Button>,
        ]}
      >
        <Upload.Dragger
          multiple
          fileList={uploadFileList}
          beforeUpload={() => false}
          onChange={({ fileList }) => setUploadFileList(fileList)}
        >
          <p className="ant-upload-drag-icon">
            <CloudUploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持单个或批量上传，严禁上传公司内部资料及其他违禁文件
          </p>
        </Upload.Dragger>
      </Modal>

      {/* Create Folder Modal */}
      <Modal
        title="新建文件夹"
        open={createFolderVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setCreateFolderVisible(false);
          form.resetFields();
        }}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateFolder}>
          <Form.Item
            name="name"
            label="文件夹名称"
            rules={[{ required: true, message: "请输入文件夹名称" }]}
          >
            <Input placeholder="请输入文件夹名称" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <Input.TextArea rows={4} placeholder="请输入文件夹描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Preview Modal */}
      <Modal
        title="文件预览"
        open={previewModalVisible}
        centered={true}
        onCancel={() => {
          setPreviewModalVisible(false);
          setPreviewFileData(null);
        }}
        footer={[
          <Button
            key="preview"
            type="primary"
            onClick={async () => {
              if (previewFileData && previewFileData.id) {
                try {
                  const response = await fileManagementApi.getPresignedUrl(
                    previewFileData.id
                  );
                  if (response && response.url) {
                    const url = response.url;
                    window.open(url, "_blank");
                  } else {
                    console.error("获取预览链接失败", response.msg);
                  }
                } catch (error) {
                  console.error("获取预览链接失败", error);
                }
              }
            }}
          >
            在线预览
          </Button>,
          <Button
            key="cancel"
            onClick={() => {
              setPreviewModalVisible(false);
              setPreviewFileData(null);
            }}
          >
            关闭
          </Button>,
        ]}
        width={600}
      >
        {previewFileData && (
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "16px",
              fontSize: "14px",
            }}
          >
            <div key="filename">
              <strong>文件名称：</strong>
              {previewFileData.filename}
            </div>
            <div key="uploader">
              <strong>上传者：</strong>
              {previewFileData.uploader}
            </div>
            <div key="originalFilename">
              <strong>原始文件名称：</strong>
              {previewFileData.originalFilename}
            </div>
            <div key="status">
              <strong>文件状态：</strong>
              {previewFileData.status === 0 ? "正常" : "删除"}
            </div>
            <div key="size">
              <strong>文件大小：</strong>
              {previewFileData.size}
            </div>
            <div key="contentType">
              <strong>文件类型：</strong>
              {previewFileData.contentType}
            </div>
            <div key="uploadTime">
              <strong>上传时间：</strong>
              {dayjs(previewFileData.uploadTime).format("YYYY-MM-DD HH:mm:ss")}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default FileManagement;
