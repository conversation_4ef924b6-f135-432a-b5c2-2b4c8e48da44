import fetch from './fetch';

import {API_PREFIX} from "./constant"

// 分页获取升级任务记录列表
export const getUpgradeTaskList = (params) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/upgrade/task`,
    method: 'GET',
    params
  })
}

// 获取任务统计详情
export const getTaskStatistics = () =>{
  return fetch({
    url: `${API_PREFIX["device-service"]}/upgrade/task/statistics`,
    method: "GET"
  })
}

// 启动任务
export const startTask = (taskId) => {
    return fetch({
      url: `${API_PREFIX["device-service"]}/upgrade/task/${taskId}/start`,
      method: "post"
    })
}

// 暂停任务
export const pauseTask = (taskId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/upgrade/task/${taskId}/pause`,
    method: "post"
  })
}

// 选择设备创建任务
export const createTaskByDevice = (data) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/upgrade/task`,
    method: "post",
    data
  })
}

// 选择位置创建任务
export const createTaskByLocation = (data) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/upgrade/task/createTaskByGroup`,
    method: "post",
    data
  })
}

// 获取任务详情
export const getTaskDetail = (taskId) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/upgrade/task/${taskId}`,
    method: "get"
  })
}

