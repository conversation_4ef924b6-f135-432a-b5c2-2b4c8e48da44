// API基础URL - 可以根据实际部署环境手动配置
import { API_PREFIX } from "./constant"
import { getLoginEntry } from '../utils/loginLogger';
import { message } from 'antd';

const handleApiError = (response) => {
  return response.json().then(data => {
    if (response.status === 401) {
      message.error(data?.message);
      setTimeout(() => {
        window.location.href = '/login'
      }, 500);
    } else if (response.status === 400 || response.status === 404 || response.status === 500) {
      message.error(data?.message);
    } else if (response.status === 403) {
      // Handle forbidden
    }
    return Promise.reject(data);
  });
};

// 检查响应数据是否包含错误码
const checkResponseData = (data) => {
  if (data && data.code && data.code !== 200) {
    message.error(data.message || '操作失败');
    return Promise.reject(data);
  }
  return data;
};


// 用户管理相关接口
export const tenantManagementApi = {
  // 分页查询接口
  getTenantPage: async (params) => {
    const response = await fetch(`${API_PREFIX["user-service"]}/tenant/page`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify(params)
    });
    
    if (!response.ok) {
      return handleApiError(response);
    }
    return response.json().then(checkResponseData);
  },

  // 获取租户详情
  getTenantDetail: async (id) => {
    const response = await fetch(`${API_PREFIX["user-service"]}/tenant/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getLoginEntry()
      }
    });
    
    if (!response.ok) {
      return handleApiError(response);
    }
    return response.json().then(checkResponseData);
  },

  // 获取租户下拉列表
  getTenantDropdown: async () => {
    const response = await fetch(`${API_PREFIX["user-service"]}/tenant/dropdown-query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify({})
    });
    
    if (!response.ok) {
      return handleApiError(response);
    }
    return response.json().then(checkResponseData);
  },

  // 添加租户
  addTenant: async (params) => {
    const response = await fetch(`${API_PREFIX["user-service"]}/tenant/add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify(params)
    });
    
    if (!response.ok) {
      return handleApiError(response);
    }
    return response.json().then(checkResponseData);
  },

  // 更新租户
  updateTenant: async (params) => {
    const response = await fetch(`${API_PREFIX["user-service"]}/tenant/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify(params)
    });
    
    if (!response.ok) {
      return handleApiError(response);
    }
    return response.json().then(checkResponseData);
  },

  // 禁用租户
  disableTenants: async (ids) => {
    const response = await fetch(`${API_PREFIX["user-service"]}/tenant/disabled`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify({ ids })
    });
    
    if (!response.ok) {
      return handleApiError(response);
    }
    return response.json().then(checkResponseData);
  },

  // 启用租户
  enableTenants: async (ids) => {
    const response = await fetch(`${API_PREFIX["user-service"]}/tenant/enable`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify({ ids })
    });
    
    if (!response.ok) {
      return handleApiError(response);
    }
    return response.json().then(checkResponseData);
  },

  // 删除租户
  deleteTenants: async (ids) => {
    const response = await fetch(`${API_PREFIX["user-service"]}/tenant/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getLoginEntry()
      },
      body: JSON.stringify({ ids })
    });
    
    if (!response.ok) {
      return handleApiError(response);
    }
    return response.json().then(checkResponseData);
  }
};

