import React, { useState } from 'react';
import {
  Layout,
  Card,
  Row,
  Col,
  Button,
  Input,
  Space,
  Table,
  Tag,
  Progress,
  Statistic,
  Select,
  Tree,
  Badge,
  Drawer,
  Tooltip,
  Tabs,
  DatePicker,
  Descriptions,
  Timeline,
  List
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  DownloadOutlined,
  ReloadOutlined,
  GlobalOutlined,
  TeamOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  EnvironmentOutlined,
  ThunderboltOutlined,
  ClockCircleOutlined,
  BulbOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  EyeOutlined,
  EditOutlined,
  PieChartOutlined
} from '@ant-design/icons';
import { Pie, Line } from '@ant-design/plots';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';
import './styles.css';

dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

const { Sider, Content } = Layout;
const { Search } = Input;
const { RangePicker } = DatePicker;

const StrategyScene = () => {
  const [selectedTenant, setSelectedTenant] = useState(null);
  const [viewMode, setViewMode] = useState('table');
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedScene, setSelectedScene] = useState(null);

  // Mock data for statistics
  const statistics = [
    {
      title: '总场景数',
      value: 256,
      icon: <PieChartOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
      trend: { value: 12, type: 'up' }
    },
    {
      title: '平均驱赶效果',
      value: 92,
      icon: <ThunderboltOutlined style={{ fontSize: '24px', color: '#52c41a' }} />,
      suffix: '分'
    },
    {
      title: '平均响应时间',
      value: 2.5,
      icon: <ClockCircleOutlined style={{ fontSize: '24px', color: '#faad14' }} />,
      suffix: '秒'
    },
    {
      title: '能效评分',
      value: 88,
      icon: <BulbOutlined style={{ fontSize: '24px', color: '#722ed1' }} />,
      suffix: '分'
    }
  ];

  // Mock data for tenants
  const tenants = [
    {
      key: '1',
      title: (
        <Space>
          <img
            src="https://api.dicebear.com/7.x/initials/svg?seed=TC"
            alt="Logo"
            style={{ width: 24, height: 24, borderRadius: '50%' }}
          />
          科技公司A
          <Badge count={25} />
        </Space>
      ),
      sceneCount: 25,
      warningCount: 2
    },
    {
      key: '2',
      title: (
        <Space>
          <img
            src="https://api.dicebear.com/7.x/initials/svg?seed=GV"
            alt="Logo"
            style={{ width: 24, height: 24, borderRadius: '50%' }}
          />
          政府机构B
          <Badge count={18} />
        </Space>
      ),
      sceneCount: 18,
      warningCount: 0
    }
  ];

  // Mock data for scenes
  const scenes = [
    {
      key: '1',
      status: 'success',
      startTime: '2024-02-20 15:30:00',
      duration: '5分钟',
      deviceId: 'USR-2024-001',
      eventType: 'auto',
      trigger: '红外感应',
      mouseType: '褐家鼠',
      strategy: '标准超声波策略A',
      score: 95,
      location: '3楼会议室A'
    },
    {
      key: '2',
      status: 'warning',
      startTime: '2024-02-20 15:00:00',
      duration: '8分钟',
      deviceId: 'USR-2024-002',
      eventType: 'manual',
      trigger: '手动触发',
      mouseType: '未识别',
      strategy: '复合策略B',
      score: 75,
      location: '2楼储藏室'
    }
  ];

  // Table columns
  const columns = [
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Badge
          status={
            status === 'success' ? 'success' :
            status === 'warning' ? 'warning' : 'error'
          }
          text={
            status === 'success' ? '成功' :
            status === 'warning' ? '警告' : '失败'
          }
        />
      )
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (time) => (
        <Tooltip title={time}>
          {dayjs(time).fromNow()}
        </Tooltip>
      )
    },
    {
      title: '持续时间',
      dataIndex: 'duration',
      key: 'duration'
    },
    {
      title: '设备ID',
      dataIndex: 'deviceId',
      key: 'deviceId'
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location'
    },
    {
      title: '事件类型',
      dataIndex: 'eventType',
      key: 'eventType',
      render: (type) => (
        <Tag color={
          type === 'auto' ? 'blue' :
          type === 'manual' ? 'green' : 'orange'
        }>
          {type === 'auto' ? '自动触发' :
           type === 'manual' ? '手动触发' : '计划执行'}
        </Tag>
      )
    },
    {
      title: '鼠类类型',
      dataIndex: 'mouseType',
      key: 'mouseType',
      render: (type) => (
        <Tag>{type}</Tag>
      )
    },
    {
      title: '执行策略',
      dataIndex: 'strategy',
      key: 'strategy'
    },
    {
      title: '效果评分',
      dataIndex: 'score',
      key: 'score',
      render: (score) => (
        <Tooltip title={`${score}分`}>
          <Progress
            percent={score}
            size="small"
            status={
              score >= 90 ? 'success' :
              score >= 70 ? 'normal' : 'exception'
            }
          />
        </Tooltip>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedScene(record);
              setDrawerVisible(true);
            }}
          >
            详情
          </Button>
          <Button type="text" icon={<EditOutlined />}>
            备注
          </Button>
        </Space>
      )
    }
  ];

  // Scene details drawer
  const renderSceneDetails = () => {
    if (!selectedScene) return null;

    // Mock data for mouse activity
    const mouseActivityData = Array.from({ length: 24 }, (_, i) => ({
      time: `${i}:00`,
      value: Math.floor(Math.random() * 100)
    }));

    const activityConfig = {
      data: mouseActivityData,
      xField: 'time',
      yField: 'value',
      smooth: true
    };

    return (
      <div className="scene-details">
        <Tabs
          items={[
            {
              key: 'basic',
              label: '基本信息',
              children: (
                <Card bordered={false}>
                  <Descriptions column={2}>
                    <Descriptions.Item label="场景ID">SCENE-20240220-001</Descriptions.Item>
                    <Descriptions.Item label="设备ID">{selectedScene.deviceId}</Descriptions.Item>
                    <Descriptions.Item label="开始时间">{selectedScene.startTime}</Descriptions.Item>
                    <Descriptions.Item label="持续时间">{selectedScene.duration}</Descriptions.Item>
                    <Descriptions.Item label="事件类型">{selectedScene.eventType}</Descriptions.Item>
                    <Descriptions.Item label="触发源">{selectedScene.trigger}</Descriptions.Item>
                    <Descriptions.Item label="鼠类类型">{selectedScene.mouseType}</Descriptions.Item>
                    <Descriptions.Item label="执行策略">{selectedScene.strategy}</Descriptions.Item>
                  </Descriptions>
                </Card>
              )
            },
            {
              key: 'environment',
              label: '环境与效果',
              children: (
                <Card bordered={false}>
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Card title="环境条件" size="small">
                        <List
                          size="small"
                          dataSource={[
                            { label: '温度', value: '25°C' },
                            { label: '湿度', value: '65%' },
                            { label: '光照', value: '200lux' }
                          ]}
                          renderItem={(item) => (
                            <List.Item>
                              <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                                <span>{item.label}</span>
                                <span>{item.value}</span>
                              </Space>
                            </List.Item>
                          )}
                        />
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card title="效果指标" size="small">
                        <List
                          size="small"
                          dataSource={[
                            { label: '响应时间', value: '2.5秒' },
                            { label: '效果评分', value: '95分' },
                            { label: '能源消耗', value: '-12%' }
                          ]}
                          renderItem={(item) => (
                            <List.Item>
                              <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                                <span>{item.label}</span>
                                <span>{item.value}</span>
                              </Space>
                            </List.Item>
                          )}
                        />
                      </Card>
                    </Col>
                  </Row>
                  <Card title="活动趋势" size="small" style={{ marginTop: 16 }}>
                    <Line {...activityConfig} />
                  </Card>
                </Card>
              )
            },
            {
              key: 'timeline',
              label: '事件时间线',
              children: (
                <Card bordered={false}>
                  <Timeline
                    items={[
                      {
                        color: 'blue',
                        children: '15:30:00 - 检测到活动'
                      },
                      {
                        color: 'green',
                        children: '15:30:02 - 启动驱赶策略'
                      },
                      {
                        color: 'red',
                        children: '15:32:00 - 检测到目标离开'
                      },
                      {
                        color: 'gray',
                        children: '15:35:00 - 事件结束'
                      }
                    ]}
                  />
                </Card>
              )
            }
          ]}
        />
      </div>
    );
  };

  return (
    <div className="strategy-scene">
      {/* Page Header */}
      <div className="page-header">
        <div className="header-left">
          <h2>驱鼠场景</h2>
        </div>
        <div className="header-right">
          <Space>
            <Search
              placeholder="搜索场景..."
              style={{ width: 200 }}
              allowClear
            />
            <Button icon={<FilterOutlined />}>筛选</Button>
            <Button icon={<DownloadOutlined />}>导出</Button>
            {/* <Button icon={<ReloadOutlined />}>刷新</Button> */}
            <Space className="view-switch">
              <Button
                type={viewMode === 'table' ? 'primary' : 'default'}
                icon={<UnorderedListOutlined />}
                onClick={() => setViewMode('table')}
              />
              <Button
                type={viewMode === 'card' ? 'primary' : 'default'}
                icon={<AppstoreOutlined />}
                onClick={() => setViewMode('card')}
              />
            </Space>
          </Space>
        </div>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} className="statistics-cards">
        {statistics.map((stat, index) => (
          <Col span={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
                // suffix={stat.suffix}
                suffix={
                  <>
                    {stat.suffix}
                    {stat.trend && (
                      <span style={{ marginLeft: '8px', fontSize: '14px' }} className={`trend-${stat.trend.type}`}>
                        {stat.trend.type === 'up' ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                        {stat.trend.value}% 环比
                      </span>
                    )}
                  </>
                }
              />
              {/* {stat.trend && (
                <div className="trend-info">
                  <span className={`trend-${stat.trend.type}`}>
                    {stat.trend.type === 'up' ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                    {stat.trend.value}%
                  </span>
                  <span className="trend-label">环比</span>
                </div>
              )} */}
            </Card>
          </Col>
        ))}
      </Row>

      {/* Main Content */}
      <Layout className="main-content">
        {/* Left Sider */}
        <Sider width={300} className="tenant-sider">
          <Card className="tenant-card">
            <div className="tenant-search">
              <Search
                placeholder="搜索租户..."
                allowClear
              />
            </div>
            <List
              dataSource={tenants}
              renderItem={(item) => (
                <List.Item
                  className={selectedTenant === item.key ? 'selected' : ''}
                  onClick={() => setSelectedTenant(item.key)}
                >
                  {item.title}
                  {item.warningCount > 0 && (
                    <Tag color="error">
                      {item.warningCount} 个异常
                    </Tag>
                  )}
                </List.Item>
              )}
            />
          </Card>
        </Sider>

        {/* Right Content */}
        <Content className="scene-content">
          <Card className="filter-bar">
            <Space wrap>
              <Select
                defaultValue="all"
                style={{ width: 120 }}
                options={[
                  { value: 'all', label: '全部类型' },
                  { value: 'auto', label: '自动触发' },
                  { value: 'manual', label: '手动触发' },
                  { value: 'scheduled', label: '计划执行' }
                ]}
              />
              <Select
                defaultValue="all"
                style={{ width: 120 }}
                options={[
                  { value: 'all', label: '全部状态' },
                  { value: 'success', label: '成功' },
                  { value: 'warning', label: '警告' },
                  { value: 'error', label: '失败' }
                ]}
              />
              <Select
                defaultValue="all"
                style={{ width: 120 }}
                options={[
                  { value: 'all', label: '全部评分' },
                  { value: 'excellent', label: '优(90+)' },
                  { value: 'good', label: '良(70-89)' },
                  { value: 'fair', label: '中(40-69)' },
                  { value: 'poor', label: '差(0-39)' }
                ]}
              />
              <RangePicker />
            </Space>
          </Card>

          <Card className="scene-table">
            <Table
              columns={columns}
              dataSource={scenes}
              pagination={{
                total: 100,
                pageSize: 10,
                showQuickJumper: true,
                showSizeChanger: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </Card>
        </Content>
      </Layout>

      {/* Scene Detail Drawer */}
      <Drawer
        title="场景详情"
        placement="right"
        width={720}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        {renderSceneDetails()}
      </Drawer>
    </div>
  );
};

export default StrategyScene;