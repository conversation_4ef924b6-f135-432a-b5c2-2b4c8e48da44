.alert-info {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.page-header {
  margin-bottom: 24px;
}

.header-left {
  margin-bottom: 16px;
}

.header-left h2 {
  margin: 8px 0 0;
  font-size: 24px;
  font-weight: 500;
}

.filter-bar {
  margin-bottom: 16px;
}

.statistics-cards {
  margin-bottom: 16px;
}

.statistics-cards .ant-card {
  border-radius: 8px;
}

.alert-list {
  flex: 1;
  min-height: 0;
}

.alert-content {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.detail-label {
  color: rgba(0, 0, 0, 0.45);
  margin-right: 8px;
  min-width: 80px;
  display: inline-block;
}

/* Modal styles */
.ant-modal-body .ant-form-item {
  margin-bottom: 24px;
}

.ant-modal-body .ant-radio-group {
  width: 100%;
  display: flex;
  gap: 8px;
}

.ant-modal-body .ant-radio-button-wrapper {
  flex: 1;
  text-align: center;
}

/* Responsive styles */
@media (max-width: 768px) {
  .alert-info {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .statistics-cards .ant-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .ant-modal {
    max-width: 100vw;
    margin: 8px;
  }
}