import { Drawer, Tag, message } from 'antd'
import PropTypes from 'prop-types'
import { upgradeDeviceMaxVersion } from '../../service/deviceRepellent'
import { useEffect, useState } from 'react'
import { dictionaryConfigApi } from '../../service/DictionaryConfig'

const DeviceDetail = ({ visible, onCancel, device }) => {
  const [dictType, setDictType] = useState([])
  const [deviceTypeDict, setDeviceTypeDict] = useState([])

  // 获取字典类型
  const getDictType = async () => {
    const res = await dictionaryConfigApi.getDictItemByTypeCode('device_type')
    setDictType(res)
  }

  const getDeviceTypeDic = async () => {
    const res = await dictionaryConfigApi.getDictItemByTypeCode(
      'device_version'
    )
    setDeviceTypeDict(res)
  }

  useEffect(() => {
    getDictType()
    getDeviceTypeDic()
  }, [])

  const statusMap = {
    0: { text: '离线', color: '#ff4d4f' },
    1: { text: '在线', color: '#52c41a' },
    2: { text: '驱鼠中', color: '#1677ff' },
    3: { text: '禁用', color: '#faad14' }
  }
  const status = statusMap[device?.deviceStatus] || statusMap[3]

  const upgrade = async deviceId => {
    if (!deviceId) {
      message.error('设备ID不能为空')
      return
    }

    try {
      const res = await upgradeDeviceMaxVersion(deviceId)
      if (res == true) {
        return message.success('已加入升级队列')
      }
    } catch (error) {
      console.error('升级失败:', error)
    }
  }

  return (
    <Drawer
      title='设备详情'
      open={visible}
      onClose={onCancel}
      width={600}
      placement="right"
    >
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
        <img src='/qsq.png' alt='驱鼠器图片' style={{ width: 120 }} />
      </div>

      <div className="section-container">
        <div className="section-header">
          <h3>基本信息</h3>
        </div>
        <div style={{ display: 'flex', flexWrap: 'wrap' }}>
          <div className="detail-item">
            <span className="detail-label">驱鼠器编号：</span>
            <span className="detail-value">{device?.deviceNo}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">驱鼠器名称：</span>
            <span className="detail-value">{device?.deviceName}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">摄像头编号：</span>
            <span className="detail-value">{device?.cameraNo}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">摄像头名称：</span>
            <span className="detail-value">{device?.cameraName}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">驱鼠器状态：</span>
            <span className="detail-value">
              <Tag color={status.color}>{status.text}</Tag>
            </span>
          </div>
          <div className="detail-item">
            <span className="detail-label">地址信息：</span>
            <span className="detail-value">{device?.deviceLocal || '-'}</span>
          </div>
        </div>
      </div>

      <div className="section-container">
        <div className="section-header">
          <h3>设备规格</h3>
        </div>
        <div style={{ display: 'flex', flexWrap: 'wrap' }}>
          <div className="detail-item">
            <span className="detail-label">驱鼠器类型：</span>
            <span className="detail-value">
              {(() => {
                const findItem = dictType.find(
                  item => item.itemCode === device?.deviceType
                )
                const typeName = findItem ? findItem.itemName : '未知'
                return typeName
              })()}
            </span>
          </div>
          <div className="detail-item">
            <span className="detail-label">驱鼠器型号：</span>
            <span className="detail-value">
              {(() => {
                const findItem = deviceTypeDict.find(
                  item => item.itemCode === device?.model
                )
                const typeName = findItem ? findItem.itemName : '-'
                return typeName
              })()}
            </span>
          </div>
          <div className="detail-item">
            <span className="detail-label">制造商信息：</span>
            <span className="detail-value">{device?.manufacturer}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">通信协议：</span>
            <span className="detail-value">
              {device?.communicationProtocol === 'MQTT'
                ? 'MQTT'
                : device?.communicationProtocol === 'HTTP'
                ? 'HTTP'
                : device?.communicationProtocol === 'CoAP'
                ? 'CoAP'
                : '未知'}
            </span>
          </div>
        </div>
      </div>

      <div className="section-container">
        <div className="section-header">
          <h3>技术信息</h3>
        </div>
        <div style={{ display: 'flex', flexWrap: 'wrap' }}>
          <div className="detail-item">
            <span className="detail-label">当前固件版本：</span>
            <span className="detail-value">
              {device?.softwareVersion}
              <a
                onClick={() => upgrade(device?.deviceId)}
                style={{
                  marginLeft: 8,
                  color: '#1890ff',
                  textDecoration: 'underline',
                  cursor: 'pointer'
                }}
                title='升级到最新版本'
              >
                升级
              </a>
            </span>
          </div>
          <div className="detail-item">
            <span className="detail-label">硬件版本：</span>
            <span className="detail-value">{device?.hardwareVersion}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">MAC地址：</span>
            <span className="detail-value">{device?.macAddress}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">超声波状态：</span>
            <span className="detail-value">{device?.ultrasoundState ? '开启' : '关闭'}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">LED状态：</span>
            <span className="detail-value">{device?.ledState ? '开启' : '关闭'}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">喇叭状态：</span>
            <span className="detail-value">{device?.soundState ? '开启' : '关闭'}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">固件升级状态：</span>
            <span className="detail-value">
              {device?.firmwareState === 0
                ? '等待升级'
                : device?.firmwareState === 1
                ? '升级中'
                : device?.firmwareState === 2
                ? '升级完成'
                : device?.firmwareState === 3
                ? '升级失败'
                : '未知'}
            </span>
          </div>
        </div>
      </div>

      <div className="section-container">
        <div className="section-header">
          <h3>时间信息</h3>
        </div>
        <div style={{ display: 'flex', flexWrap: 'wrap' }}>
          <div className="detail-item">
            <span className="detail-label">注册日期：</span>
            <span className="detail-value">{device?.installTime}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">最后在线时间：</span>
            <span className="detail-value">{device?.lastReportTime}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">升级时间：</span>
            <span className="detail-value">{device?.upgradeTime}</span>
          </div>
        </div>
      </div>

      {device?.notes && (
        <div className="section-container">
          <div className="section-header">
            <h3>备注信息</h3>
          </div>
          <div style={{ display: 'flex', flexWrap: 'wrap' }}>
            <div className="detail-item" style={{ width: '100%' }}>
              <span className="detail-value">{device?.notes}</span>
            </div>
          </div>
        </div>
      )}

      <style jsx="true">{`
        .section-container {
          margin-bottom: 24px;
          border-radius: 4px;
          background: #fff;
        }
        .section-header {
          padding-bottom: 8px;
          border-bottom: 1px solid #f0f0f0;
          margin-bottom: 16px;
        }
        .section-header h3 {
          margin: 0;
          color: #1890ff;
          font-size: 16px;
          font-weight: 500;
        }
        .detail-item {
          width: 50%;
          margin-bottom: 16px;
          display: flex;
        }
        .detail-label {
          color: #606266;
          font-weight: normal;
          margin-right: 8px;
          min-width: 100px;
        }
        .detail-value {
          color: #303133;
        }
      `}</style>
    </Drawer>
  )
}

export default DeviceDetail

DeviceDetail.propTypes = {
  visible: PropTypes.bool,
  onCancel: PropTypes.func,
  device: PropTypes.object
}
