// API基础URL - 可以根据实际部署环境手动配置
import { API_PREFIX } from "./constant";
import { getLoginEntry } from '../utils/loginLogger';
import { message } from 'antd';

const handleApiError = (response) => {
  return response.json().then(data => {
    if (response.status === 401) {
      message.error(data?.message);
      setTimeout(() => {
        window.location.href = '/login'
      }, 500);
    } else if (response.status === 400 || response.status === 404 || response.status === 500) {
      message.error(data?.message);
    } else if (response.status === 403) {
      // Handle forbidden
    }
    return Promise.reject(data);
  });
};


// 用户管理相关接口
export const systemConfigApi = {
  // 获取租户下拉列表数据
  getTenantDropdownList: () => {
    const authData = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/tenant/dropdown-query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authData
      },
      body: JSON.stringify({})
    })
    .then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },

  // 获取租户配置数据
  getTenantConfig: (tenantId) => {
    const authData = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/tenant/config/query?tenantId=${tenantId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authData
      }
    })
    .then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
      .then(data => {
        // 移除message字段并添加静默标记，防止全局成功提示
        const { message, ...restData } = data;
        return {
          ...restData,
          suppressGlobalMessage: true
        };
      })
      
  },

  // 更新租户配置数据
  updateTenantConfig: (configData) => {
    const authData = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/tenant/config/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authData
      },
      body: JSON.stringify(configData)
    })
    .then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },

  // 新增租户配置数据
  addTenantConfig: (configData) => {
    const authData = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/tenant/config/add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authData
      },
      body: JSON.stringify(configData)
    })
    .then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },

  // 激活租户配置
  activeTenantConfig: (ids) => {
    const authData = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/tenant/config/active`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authData
      },
      body: JSON.stringify({ ids })
    })
    .then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  },

  // 取消激活租户配置
  unActiveTenantConfig: (ids) => {
    const authData = getLoginEntry();
    return fetch(`${API_PREFIX["user-service"]}/tenant/config/unActive`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authData
      },
      body: JSON.stringify({ ids })
    })
    .then(response => {
      if(response.status === 200){
        return response.json()
      }
      return handleApiError(response);
    })
  }
};

