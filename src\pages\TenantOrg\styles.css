.tenant-org {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 8px 0 0;
  font-size: 24px;
  font-weight: 500;
}

.main-content {
  flex: 1;
  background: transparent;
  min-height: 0;
}

.org-sider {
  background: transparent !important;
  margin-right: 24px;
}

.tree-card {
  height: 100%;
}

.tree-header {
  padding: 0 0 16px;
  display: flex;
  gap: 8px;
}

.tree-tools {
  display: flex;
  gap: 8px;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 8px;
}

.node-title {
  flex: 1;
}

.node-actions {
  visibility: hidden;
}

.tree-node:hover .node-actions {
  visibility: visible;
}

.org-content {
  background: transparent !important;
  padding: 0 !important;
  overflow: auto;
}

.org-detail {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.action-bar {
  background: #fff;
  border-radius: 4px;
}

.detail-cards {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-card {
  background: #fff;
  border-radius: 8px;
}

.empty-state {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #fff;
  border-radius: 4px;
  padding: 48px;
  text-align: center;
}

.empty-state h3 {
  margin: 16px 0 8px;
  color: rgba(0, 0, 0, 0.88);
}

.empty-state p {
  color: rgba(0, 0, 0, 0.45);
}

/* Responsive styles */
@media (max-width: 768px) {
  .tenant-org {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .main-content {
    flex-direction: column;
  }

  .org-sider {
    width: 100% !important;
    max-width: 100% !important;
    margin-right: 0;
    margin-bottom: 24px;
  }

  .detail-cards {
    gap: 16px;
  }
}