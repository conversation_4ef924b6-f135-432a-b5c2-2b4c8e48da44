import fetch from './fetch';

import {API_PREFIX} from "./constant"

// 音频分页查询
export const getRatAudiosList = (params) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/rat-audios/search`,
    method: 'GET',
    params:params
  })
}

// 创建音频
export const createRatAudiosPackage = (data) => {
  return fetch({
    url: `${API_PREFIX["device-service"]}/rat-audios/upload`,
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除音频
export const delRatAudio = (audioId) =>{
  return fetch({
    url:  `${API_PREFIX["device-service"]}/rat-audios/${audioId}`,
    method:"delete"
  })
}

// 更新音频信息
export const updateRatAudio = (audioId,data) =>{
  return fetch({
    url: `${API_PREFIX["device-service"]}/rat-audios/${audioId}`,
    method: "put",
    data
  })
}

// 获取音频详情
export const getRatAudioById = (audioId) =>{
  return fetch({
    url: `${API_PREFIX["device-service"]}/rat-audios/${audioId}`,
    method: "get",
  })
}

// 下载音频
export const downloadRatAudio = (audioId) =>{
  return fetch({
    url: `${API_PREFIX["device-service"]}/rat-audios/${audioId}/download`,
    method: "get",
    responseType: 'blob',
  })
}

export const getAllRatAudioAategories = () =>{
  return fetch({
    url: `${API_PREFIX["device-service"]}/rat-audios/categories`,
    method: "get",
  })
}