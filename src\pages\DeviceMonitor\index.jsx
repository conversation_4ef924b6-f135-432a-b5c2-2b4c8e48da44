import React, { useEffect, useState, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Layout,
  Card,
  Row,
  Col,
  Select,
  DatePicker,
  Tabs,
  TreeSelect,
  Progress,
  Tag,
  Button,
  Form,
  Input,
  Space,
  Spin,
} from "antd";
import {
  DashboardOutlined,
  AlertOutlined,
  SearchOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import Overview from "./components/Overview";
import DeviceHealth from "./components/DeviceHealth";
import Performance from "./components/Performance";
import Alerts from "./components/Alerts";
import {
  getDeviceStatus,
  exportDeviceStatus,
  gettemperature,
  getHum,
  getAlarm,
} from "./../../service/deviceMonitor";
import { getCameraList } from "../../service/deviceCamera";
import {
  getDeviceListByCameraId,
  getDeviceRepellentList,
} from "../../service/deviceRepellent";
import { debounce, set, size } from "lodash";

import "./styles.css";
import dayjs from "dayjs";
import { use } from "react";

const { Option } = Select;
const { RangePicker } = DatePicker;

const DeviceMonitor = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [monitorData, setMonitorData] = useState({}); // 获取页面所有数据
  const [cameraData, setCameraData] = useState([]); // 获取摄像头数据
  const [deviceData, setDeviceData] = useState([]); // 获取驱鼠器数据
  const [selectedCamera, setSelectedCamera] = useState(null); // 选中的摄像头
  const [selectedDevice, setSelectedDevice] = useState(null); // 选中的驱鼠器
  const [repellentList, setRepellentList] = useState([]); // 驱鼠器列表数据
  const [selectedRepellent, setSelectedRepellent] = useState(null); // 选中的驱鼠器
  const [selectedRepellentInfo, setSelectedRepellentInfo] = useState(null); // 选中的驱鼠器完整信息
  const [cameraLoading, setCameraLoading] = useState(false); //摄像头loading
  const [tab, setTab] = useState("overview");
  const [loading, setLoading] = useState(false);
  const [searchCameraData, setSearchCameraData] = useState({
    keyword: "",
    size: 50,
    page: 0,
  }); // 分页查询摄像头
  const [totalPages, setTotalPages] = useState(0);
  const [selectedDate, setSelectedDate] = useState(dayjs()); // 选中的日期
  const [dateType, setDateType] = useState("day"); // 日期类型：day, week, month, custom
  const [dateRange, setDateRange] = useState([
    dayjs().hour(0).minute(0).second(0),
    dayjs().hour(23).minute(59).second(59),
  ]); //
  const [qsqData, setQsqData] = useState([]); // 获取驱鼠器数据
  const [searchData, setSearchData] = useState({
    deviceId: "",
    tenantId: 1,
    startTime: dayjs()
      .hour(0)
      .minute(0)
      .second(0)
      .format("YYYY-MM-DD HH:mm:ss"),
    endTime: dayjs()
      .hour(23)
      .minute(59)
      .second(59)
      .format("YYYY-MM-DD HH:mm:ss"),
  }); // 获取页面数据条件
  const [chart, setChart] = useState({
    chart1: {},
    chart2: {},
    chart3: {},
  }); // 图表数据
  const [repellentSearchForm] = Form.useForm(); // 驱鼠器搜索表单
  const [repellentSearchParams, setRepellentSearchParams] = useState({
    deviceName: "",
    deviceNo: "",
    page: 0,
    size: 100,
  }); // 驱鼠器搜索参数

  // 获取摄像头列表
  const fetchCameraList = async (obj = {}) => {
    try {
      const res = await getCameraList({
        ...searchCameraData,
        ...obj,
      });
      setTotalPages(res.totalPages);
      return res.content;
    } catch (error) {
      console.error("Failed to fetch camera list:", error);
    }
  };

  //获取驱鼠器列表
  const getDeviceData = async (searchParams = {}) => {
    setLoading(true);
    try {
      const params = {
        page: 0,
        size: 100,
        ...searchParams,
      };
      const res = await getDeviceRepellentList(params);
      setQsqData(res);
      setLoading(false);
    } catch (error) {
      console.log(error);
    }
  };

  // 搜索驱鼠器
  const handleRepellentSearch = async () => {
    const values = repellentSearchForm.getFieldsValue();
    const searchParams = {
      name: values.deviceName || "",
      keyword: values.deviceNo || "",
      page: 0,
      size: 100,
    };
    setRepellentSearchParams((prev) => ({ ...prev, ...searchParams }));
    await getDeviceData(searchParams);
  };

  // 重置搜索
  const handleRepellentReset = async () => {
    repellentSearchForm.resetFields();
    const resetParams = {
      name: "",
      keyword: "",
      page: 0,
      size: 100,
    };
    setRepellentSearchParams((prev) => ({ ...prev, ...resetParams }));
    await getDeviceData(resetParams);
  };

  useEffect(() => {
    (async () => {
      // 只有在没有路由参数时才执行默认的数据加载
      if (!location.state?.deviceNo) {
        await getDeviceData();
      }
    })();
  }, [location.state]);

  // 处理从驱鼠器页面传入的参数
  useEffect(() => {
    if (location.state?.deviceNo) {
      const { deviceNo } = location.state;
      // 设置搜索表单的值
      repellentSearchForm.setFieldsValue({
        deviceNo: deviceNo,
      });
      // 使用传入的设备编号进行搜索
      const searchParams = {
        page: 0,
        size: 10,
        keyword: deviceNo,
      };
      getDeviceData(searchParams);
      // 清除路由状态，避免重复处理
      navigate(".", { replace: true, state: null });
    }
  }, [location.state, navigate, repellentSearchForm]);

  // 当驱鼠器数据加载完成后，设置默认选中第一个设备
  useEffect(() => {
    if (qsqData?.content && qsqData.content.length > 0 && !selectedRepellent) {
      const firstDevice = qsqData.content[0];
      setSelectedRepellent(firstDevice.deviceId);
      setSelectedRepellentInfo(firstDevice); // 设置完整的设备信息
      setSearchData((prev) => ({
        ...prev,
        deviceId: firstDevice.deviceId,
        tenantId: firstDevice.tenantId || 1,
      }));
    }
  }, [qsqData, selectedRepellent]);

  // 获取驱鼠器列表
  const fetchDeviceList = async (cameraId) => {
    try {
      const res = await getDeviceListByCameraId(cameraId);
      setDeviceData(res);
      if (res && res.length > 0) {
        setSelectedDevice(res[0].deviceId);
        setSearchData({ ...searchData, deviceId: res[0].deviceId });
      } else {
        setSelectedDevice(null);
      }
    } catch (error) {
      console.error("Failed to fetch device list:", error);
    }
  };

  // 获取运行概览数据
  const fetchOverviewData = async () => {
    setLoading(true);
    try {
      const res = await getDeviceStatus(searchData);
      const chart1 = {
        x: [],
        y: [],
      };
      res.deviceHeartBeatAtToday?.forEach((element) => {
        chart1.x.push(element.time);
        chart1.y.push(element.status);
      });

      setMonitorData(res);
      setChart((prev) => ({
        ...prev,
        chart1,
      }));
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 获取设备温度数据
  const fetchTemperatureData = async () => {
    setLoading(true);
    try {
      const res = await gettemperature(searchData);
      const chart2 = {
        x: [],
        y: [],
      };
      // 根据实际返回的数据结构调整
      res.temOfDevice?.forEach((element) => {
        chart2.x.push(element.time);
        chart2.y.push(element.temperature);
      });

      setChart((prev) => ({
        ...prev,
        chart2,
      }));
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 获取设备湿度数据
  const fetchHumidityData = async () => {
    setLoading(true);
    try {
      const res = await getHum(searchData);
      const chart3 = {
        x: [],
        y: [],
      };
      // 根据实际返回的数据结构调整
      res.temOfDevice?.forEach((element) => {
        chart3.x.push(element.time);
        chart3.y.push(element.humidity);
      });

      setChart((prev) => ({
        ...prev,
        chart3,
      }));
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 获取告警数据
  const fetchAlarmData = async () => {
    setLoading(true);
    try {
      const res = await getAlarm(searchData);
      setMonitorData((prev) => ({
        ...prev,
        addToday: res.addToday ?? 0,
        levelStats: res.levelStats,
        totalAlarms: res.totalAlarms,
        unresolvedAlarms: res.unresolvedAlarms,
      }));
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 根据当前tab获取对应数据
  const fetchDataByTab = async (currentTab = tab) => {
    if (!searchData.deviceId) return;

    switch (currentTab) {
      case "overview":
        await fetchOverviewData();
        break;
      case "health":
        await fetchTemperatureData();
        break;
      case "performance":
        await fetchHumidityData();
        break;
      case "alerts":
        await fetchAlarmData();
        break;
      default:
        break;
    }
  };

  // 下拉搜索
  const handleSearch = async (value) => {
    setCameraLoading(true);
    try {
      const data = await fetchCameraList({ keyword: value, page: 0 });
      // 过滤出有绑定设备的摄像头（onlineDeviceStatus不为3）
      const camerasWithDevices =
        data?.filter((camera) => camera.onlineDeviceStatus !== 3) || [];
      setCameraData(camerasWithDevices);
    } catch (error) {
      console.error("Failed to search cameras:", error);
    } finally {
      setCameraLoading(false);
    }
  };

  // 滚动加载
  const loadMore = async (e) => {
    if (cameraLoading) return;
    const bottom =
      e.target.scrollHeight === e.target.scrollTop + e.target.clientHeight;
    if (bottom && totalPages - 1 > searchCameraData.page) {
      setSearchCameraData({
        ...searchCameraData,
        page: searchCameraData.page + 1,
      });
      setCameraLoading(true);
      const newOptions = await fetchCameraList({
        page: searchCameraData.page + 1,
      });
      // 过滤出有绑定设备的摄像头（onlineDeviceStatus不为3）
      const newCamerasWithDevices =
        newOptions?.filter((camera) => camera.onlineDeviceStatus !== 3) || [];
      setCameraData((prevOptions) => [
        ...prevOptions,
        ...newCamerasWithDevices,
      ]);
      setCameraLoading(false);
    }
  };

  //获取驱鼠器列表
  const handleDateChange = (date) => {
    setSelectedDate(date);
    if (date) {
      setSearchData((prev) => ({
        ...prev,
        startTime: date.format("YYYY-MM-DD 00:00:00"),
        endTime: date.format("YYYY-MM-DD 23:59:59"),
      }));
    }
  };

  const handleDateTypeChange = (type) => {
    setDateType(type);
    let start, end;

    switch (type) {
      case "day":
        start = dayjs().hour(0).minute(0).second(0);
        end = dayjs().hour(23).minute(59).second(59);
        break;
      case "week":
        start = dayjs().startOf("week").hour(0).minute(0).second(0);
        end = dayjs().endOf("week").hour(23).minute(59).second(59);
        break;
      case "month":
        start = dayjs().startOf("month").hour(0).minute(0).second(0);
        end = dayjs().endOf("month").hour(23).minute(59).second(59);
        break;
      case "custom":
        // 自定义时设置为当天，但允许用户修改
        start = dayjs().hour(0).minute(0).second(0);
        end = dayjs().hour(23).minute(59).second(59);
        break;
      default:
        start = dayjs().hour(0).minute(0).second(0);
        end = dayjs().hour(23).minute(59).second(59);
    }

    setDateRange([start, end]);
    setSearchData((prev) => ({
      ...prev,
      startTime: start.format("YYYY-MM-DD HH:mm:ss"),
      endTime: end.format("YYYY-MM-DD HH:mm:ss"),
    }));
  };

  const handleRangeChange = (dates) => {
    if (!dates || dates.length !== 2) return;

    setDateRange(dates);
    setSearchData((prev) => ({
      ...prev,
      startTime: dates[0].format("YYYY-MM-DD HH:mm:ss"),
      endTime: dates[1].format("YYYY-MM-DD HH:mm:ss"),
    }));
  };

  // 处理驱鼠器选择
  const handleRepellentSelect = (deviceId) => {
    setSelectedRepellent(deviceId);
    // 找到选中的设备信息
    const selectedDeviceInfo = qsqData?.content?.find(
      (item) => item.deviceId === deviceId
    );
    if (selectedDeviceInfo) {
      setSelectedRepellentInfo(selectedDeviceInfo); // 保存完整的设备信息
      setSearchData((prev) => ({
        ...prev,
        deviceId: selectedDeviceInfo.deviceId,
        tenantId: selectedDeviceInfo.tenantId || 1,
      }));
    }
    console.log("Selected repellent device:", deviceId);
  };

  useEffect(() => {
    (async () => {
      // 获取摄像头列表
      const res = await fetchCameraList();
      setCameraData(res);
      if (res && res.length > 0) {
        setSelectedCamera(res[0].cameraId);
        // 获取驱鼠器列表
        await fetchDeviceList(res[0].cameraId);
      }
    })();

    // 清理函数，在组件卸载时取消请求
    return () => {
      if (window.cancelToken) {
        window.cancelToken(); // 取消请求
      }
    };
  }, []);

  useEffect(() => {
    if (searchData.deviceId) {
      fetchDataByTab();
    } else {
      setMonitorData({});
    }
  }, [searchData]);

  // 当tab切换时获取对应数据
  useEffect(() => {
    if (searchData.deviceId) {
      fetchDataByTab(tab);
    }
  }, [tab]);

  const handleDeviceChange = (value) => {
    setSelectedDevice(value);
    setSearchData({ ...searchData, deviceId: value });
  };

  const handleExport = async () => {
    try {
      const result = await exportDeviceStatus(searchData);
      const blob = new Blob([result], { type: "application/xlsx" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "download.xlsx";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Export failed:", error);
    }
  };

  const tabItems = [
    {
      key: "overview",
      label: (
        <span>
          <DashboardOutlined />
          运行概览
        </span>
      ),
      children: (
        <Overview
          data={{
            deviceWorkState: monitorData?.deviceWorkState
              ? (monitorData?.deviceWorkState * 100).toFixed(2)
              : null, // 设备健度
            deviceTemperature: monitorData?.deviceTemperature
              ? monitorData.deviceTemperature.toFixed(2)
              : null, // 设备温度
            deviceHumidity: monitorData?.deviceHumidity
              ? monitorData.deviceHumidity.toFixed(2)
              : null, // 设备湿度
            deviceStatus: monitorData?.device?.deviceStatus, // 设备状态
            chart1: chart.chart1,
          }}
          isActive={tab === "overview"}
        />
      ),
    },
    {
      key: "health",
      label: (
        <span className="icon_tab_tem">
          <span></span>
          设备温度
        </span>
      ),
      children: (
        <DeviceHealth
          data={{
            chart2: chart.chart2,
          }}
          isActive={tab === "health"}
        />
      ),
    },
    {
      key: "performance",
      label: (
        <span className="icon_tab_hum">
          <span></span>
          设备湿度
        </span>
      ),
      children: (
        <Performance
          data={{
            chart3: chart.chart3,
          }}
          isActive={tab === "performance"}
        />
      ),
    },
    {
      key: "alerts",
      label: (
        <span>
          <AlertOutlined />
          告警与事件
        </span>
      ),
      children: (
        <Alerts
          {...{
            addToday: monitorData?.addToday ?? 0,
            levelStats: monitorData?.levelStats,
            totalAlarms: monitorData?.totalAlarms,
            unresolvedAlarms: monitorData?.unresolvedAlarms,
          }}
        />
      ),
    },
  ];

  return (
    <div className="device-monitor">
      {/* Main Content */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        {/* 左侧驱鼠器列表 */}
        <Col span={7}>
          <Card
            title="驱鼠器列表"
            className="repellent-list-card"
            style={{ height: "calc(100vh - 184px)" }}
            styles={{
              body: {
                padding: "8px",
                height: "calc(100vh - 234px)",
                overflow: "hidden",
                display: "flex",
                flexDirection: "column",
              },
            }}
          >
            {/* 搜索表单 */}
            <Form
              form={repellentSearchForm}
              layout="inline"
              style={{ marginBottom: "12px", flexShrink: 0 }}
              size="small"
            >
              <Form.Item
                name="deviceName"
                label="驱鼠器名称"
                style={{ marginBottom: "8px", marginRight: "4px" }}
                labelCol={{ style: { fontSize: "12px" } }}
              >
                <Input
                  placeholder="请输入驱鼠器名称"
                  size="small"
                  style={{ width: "120px" }}
                />
              </Form.Item>
              <Form.Item
                name="deviceNo"
                label="驱鼠器编号"
                style={{ marginBottom: "8px", marginRight: "4px" }}
                labelCol={{ style: { fontSize: "12px" } }}
              >
                <Input
                  placeholder="请输入驱鼠器编号"
                  size="small"
                  style={{ width: "120px" }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: "8px" }}>
                <Space size={4}>
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    onClick={handleRepellentSearch}
                    size="small"
                  >
                    搜索
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleRepellentReset}
                    size="small"
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>

            {/* 驱鼠器列表 */}
            <div
              className="repellent-list"
              style={{ flex: 1, overflow: "auto" }}
              loading={loading}
            >
              {qsqData?.content?.map((item) => (
                <div
                  key={item.deviceId}
                  className={`repellent-item ${
                    selectedRepellent === item.deviceId ? "selected" : ""
                  }`}
                  onClick={() => handleRepellentSelect(item.deviceId)}
                  style={{
                    padding: "8px 12px",
                    margin: "4px 0",
                    border: "1px solid #d9d9d9",
                    borderRadius: "4px",
                    cursor: "pointer",
                    backgroundColor:
                      selectedRepellent === item.deviceId ? "#e6f7ff" : "#fff",
                    borderColor:
                      selectedRepellent === item.deviceId
                        ? "#1890ff"
                        : "#d9d9d9",
                  }}
                >
                  <div
                    style={{
                      fontWeight: "bold",
                      marginBottom: "4px",
                      float: "left",
                    }}
                  >
                    {item.deviceName || "-"}
                  </div>
                  <div
                    style={{ fontSize: "12px", color: "#666", float: "right" }}
                  >
                    编号: {item.deviceNo || "-"}
                  </div>
                  <div style={{ clear: "both" }}></div>
                </div>
              ))}
            </div>
          </Card>
        </Col>

        {/* 右侧主要内容 */}
        <Col span={17}>
          {/* Header Section */}
          <Card className="monitor-header" style={{ marginBottom: 20 }}>
            <Form layout="horizontal">
              <Row gutter={[16, 16]} align="middle">
                <Col xs={24} sm={24} md={8} lg={6}>
                  <Form.Item label="摄像头编号" style={{ marginBottom: 0 }}>
                    {/* <Select
                      showSearch
                      // allowClear
                      placeholder="请输入摄像头编号"
                      onPopupScroll={loadMore}
                      onSearch={debounce(handleSearch, 300)}
                      loading={cameraLoading}
                      style={{ width: "100%" }}
                      optionFilterProp="children"
                      filterOption={false}
                      value={selectedCamera}
                      onChange={async (value) => {
                        setSelectedCamera(value);
                        setMonitorData({});
                        setChart({
                          chart1: {},
                          chart2: {},
                          chart3: {},
                        });
                        await fetchDeviceList(value);
                      }}
                      onDropdownVisibleChange={async (open) => {
                        if (open) {
                          const tempSearch = {
                            keyword: "",
                            size: 50,
                            page: 0,
                          };
                          const res = await fetchCameraList(tempSearch);
                          setSearchCameraData(tempSearch);
                          // 过滤出有绑定设备的摄像头（onlineDeviceStatus不为3）
                          const camerasWithDevices =
                            res?.filter(
                              (camera) => camera.onlineDeviceStatus !== 3
                            ) || [];
                          setCameraData(camerasWithDevices);
                          if (
                            camerasWithDevices &&
                            camerasWithDevices.length > 0 &&
                            !selectedCamera
                          ) {
                            setSelectedCamera(camerasWithDevices[0].cameraId);
                          }
                        }
                      }}
                    >
                      {cameraData?.map((option) => (
                        <Option key={option.cameraId} value={option.cameraId}>
                          {option.cameraNo}
                        </Option>
                      ))}
                    </Select> */}
                    <Input
                      disabled
                      value={selectedRepellentInfo?.cameraNo || "-"}
                    />
                  </Form.Item>
                </Col>
                {/* <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label='驱鼠器编号' style={{ marginBottom: 0 }}>
                <Select
                  placeholder='请选择驱鼠器'
                  style={{ width: '100%' }}
                  // allowClear
                  showSearch
                  value={selectedDevice}
                  onChange={handleDeviceChange}
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                >
                  {deviceData?.map(option => (
                    <Option key={option.deviceId} value={option.deviceId}>
                      {option.deviceNo}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col> */}
                <Col xs={24} sm={12} md={12} lg={10}>
                  <Form.Item label="日期" style={{ marginBottom: 0 }}>
                    <div className="custom-date-picker">
                      <Select
                        value={dateType}
                        onChange={handleDateTypeChange}
                        style={{ width: "80px", marginRight: "8px" }}
                        size="middle"
                      >
                        <Option value="day">今天</Option>
                        <Option value="week">本周</Option>
                        <Option value="month">本月</Option>
                        <Option value="custom">自定义</Option>
                      </Select>
                      <RangePicker
                        style={{ width: "380px" }}
                        value={dateRange}
                        onChange={handleRangeChange}
                        allowClear={false}
                        disabled={dateType !== "custom"}
                        showTime={{
                          format: "HH:mm:ss",
                          defaultValue: [
                            dayjs("00:00:00", "HH:mm:ss"),
                            dayjs("23:59:59", "HH:mm:ss"),
                          ],
                        }}
                        format="YYYY-MM-DD HH:mm:ss"
                        placeholder={["开始时间", "结束时间"]}
                        disabledDate={(current) =>
                          current && current > dayjs().endOf("day")
                        }
                        size="middle"
                      />
                    </div>
                  </Form.Item>
                </Col>

                {/* <Col xs={24} sm={24} md={24} lg={6} style={{ textAlign: 'right' }}>
              <Space>
                <Button icon={<DownloadOutlined />} onClick={handleExport}>
                  导出
                </Button>
              </Space>
            </Col> */}
              </Row>
            </Form>
          </Card>
          <Card
            className="monitor-content"
            style={{ height: "calc(100vh - 284px)", position: 'relative' }}
          >
            <div className="tabs-wrapper">
              <Tabs
                defaultActiveKey="overview"
                items={tabItems}
                onChange={(value) => {
                  setTab(value);
                }}
                className="monitor-tabs"
              />
              {loading && (
                <div className="content-loading-overlay">
                  <Spin size="large" tip="加载中..." />
                </div>
              )}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DeviceMonitor;
