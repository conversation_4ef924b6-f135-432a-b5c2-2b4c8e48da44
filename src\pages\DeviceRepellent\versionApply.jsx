import { Modal, message, Form, Select} from 'antd'
import {
  deviceConfigList,
  applyVersionConfig
} from './../../service/deviceRepellent'
import PropTypes from 'prop-types'
import { useEffect,useState  } from 'react'

const VersionApply = ({ visible, onCancel, currentDevice }) => {
  const [selectData, setSelectData] = useState([])
  const [form] = Form.useForm()

  // 获取版本配置列表
  const fetchDeviceConfigList = async () => {
    try {
      const res = await deviceConfigList(currentDevice.deviceId)

      // 处理数据，转换为树形结构
      const processedData = res.map(item => {
        const title = `版本${item.configVersion}`
        if (item.isApply) {
          form.setFieldsValue({
            deviceConfigId: item.id
          })
        }
        return {
          label: title,
          value: item.id
        }
      })

      setSelectData(processedData)
    } catch (error) {
      console.error(error)
      message.error('获取配置列表失败')
    }
  }

  const handleSubmit = async() => {
    try {
      const values = await form.validateFields()
      await applyVersionConfig(values.deviceConfigId,currentDevice.deviceId)
      message.success("版本配置成功")
      onCancel(true)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() =>{
    fetchDeviceConfigList()
  },[])
  return (
    <Modal
      title='版本应用'
      open={visible}
      onOk={() => form.submit()}
      onCancel={() =>  onCancel()}
      width={500}
    >
      <Form
        form={form}
        layout='inline'
        onFinish={handleSubmit}
        initialValues={currentDevice}
      >
        <Form.Item
          name='deviceConfigId'
          label='策略版本'
          rules={[{ required: true, message: '策略版本必填' }]}
        >
          <Select
            showSearch
            placeholder='请选择策略版本'
            style={{ width: "350px" }}
            filterOption={false}
            options={selectData}
          ></Select>
        </Form.Item>
      </Form>
    </Modal>
  )
}

VersionApply.propTypes = {
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  currentDevice: PropTypes.object
}

export default VersionApply
